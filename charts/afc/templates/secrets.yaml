apiVersion: v1
kind: Secret
metadata:
  name: "{{ .Release.Name }}-app-secrets"
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "app.labels" . | nindent 4 }}
type: Opaque
data:
  # redis settings
  {{- if .Values.redis.enabled }}
  # provisioned redis settings
  APP_REDIS_URI: {{ printf "redis://%s-redis-master:6379" .Release.Name  | b64enc }}
  {{- else }}
  # custom redis settings
  {{- with .Values.app.redis }}
  {{- include "app.secretValue" ( dict "Key" "APP_REDIS_URI" "Value" .uri ) }}
  {{- end }}
  {{- end }}

  # database settings
  {{- with .Values.app.database }}
  {{- include "app.secretValue" ( dict "Key" "APP_DB_URI" "Value" .uri ) }}
  {{- include "app.secretValue" ( dict "Key" "APP_DB_NAME" "Value" .name ) }}
  {{- include "app.secretValue" ( dict "Key" "APP_DB_POOL" "Value" .pool ) }}
  {{- end }}

  # CSFLE settings
  {{- with .Values.app.csfle }}
  {{- include "app.secretValue" ( dict "Key" "APP_CSFLE_MODE" "Value" .mode ) }}
  {{- include "app.secretValue" ( dict "Key" "APP_CSFLE_KEY_VAULT" "Value" .keyVault ) }}
  {{- include "app.secretValue" ( dict "Key" "APP_CSFLE_MASTER_KEY" "Value" .masterKey ) }}
  {{- include "app.secretValue" ( dict "Key" "APP_CSFLE_ACCESS_KEY" "Value" .accessKey ) }}
  {{- include "app.secretValue" ( dict "Key" "APP_CSFLE_SECRET_KEY" "Value" .secretKey ) }}
  {{- end }}

  # session settings
  {{- with .Values.app.session }}
  {{- include "app.secretValue" ( dict "Key" "APP_SESSION_SECRET" "Value" .secret ) }}
  {{- end }}

  # smtp settings
  {{- with .Values.app.smtp }}
  {{- include "app.secretValue" ( dict "Key" "APP_SMTP_HOST" "Value" .host ) }}
  {{- include "app.secretValue" ( dict "Key" "APP_SMTP_PORT" "Value" .port ) }}
  {{- include "app.secretValue" ( dict "Key" "APP_SMTP_SECURE" "Value" .secure ) }}
  {{- include "app.secretValue" ( dict "Key" "APP_SMTP_USER" "Value" .user ) }}
  {{- include "app.secretValue" ( dict "Key" "APP_SMTP_PASSWORD" "Value" .password ) }}
  {{- end }}


  # sms settings
  {{- with .Values.app.sms }}
  {{- include "app.secretValue" ( dict "Key" "APP_SMS_TWILIO_ACCOUNT" "Value" .twilio.account ) }}
  {{- include "app.secretValue" ( dict "Key" "APP_SMS_TWILIO_TOKEN" "Value" .twilio.token ) }}
  {{- end }}

  {{- with .Values.app.rekognition }}
  {{- include "app.secretValue" ( dict "Key" "APP_REKOGNITION_ACCESS_KEY" "Value" .accessKey ) }}
  {{- include "app.secretValue" ( dict "Key" "APP_REKOGNITION_SECRET_KEY" "Value" .secretKey ) }}
  {{- end }}

  # storage settings
  {{- if .Values.minio.enabled }}
  APP_STORAGE_ENDPOINT: {{ printf "%s-minio" .Release.Name | b64enc }}
  APP_STORAGE_ACCESS_KEY: {{ .Values.minio.auth.rootUser | b64enc }}
  APP_STORAGE_SECRET_KEY: {{ .Values.minio.auth.rootPassword | b64enc }}
  APP_STORAGE_SSL: {{ "false" | b64enc }}
  APP_STORAGE_PORT: {{ "9000" | b64enc }}
  APP_STORAGE_BUCKET: {{ .Values.minio.defaultBuckets | b64enc }}
  {{- else }}
  {{- with .Values.app.storage }}
  {{- include "app.secretValue" ( dict "Key" "APP_STORAGE_ENDPOINT" "Value" .endpoint ) }}
  {{- include "app.secretValue" ( dict "Key" "APP_STORAGE_ACCESS_KEY" "Value" .accessKey ) }}
  {{- include "app.secretValue" ( dict "Key" "APP_STORAGE_SECRET_KEY" "Value" .secretKey ) }}
  {{- include "app.secretValue" ( dict "Key" "APP_STORAGE_SSL" "Value" .ssl ) }}
  {{- include "app.secretValue" ( dict "Key" "APP_STORAGE_PORT" "Value" .port ) }}
  {{- include "app.secretValue" ( dict "Key" "APP_STORAGE_REGION" "Value" .region ) }}
  {{- include "app.secretValue" ( dict "Key" "APP_STORAGE_PRIVATE_BUCKET" "Value" .privateBucket ) }}
  {{- include "app.secretValue" ( dict "Key" "APP_STORAGE_PUBLIC_BUCKET" "Value" .publicBucket ) }}
  {{- include "app.secretValue" ( dict "Key" "APP_STORAGE_PUBLIC_ENDPOINT" "Value" .publicEndpoint ) }}
  {{- end }}
  {{- end }}

  # bedrock settings
  {{- with .Values.app.bedrock }}
  {{- include "app.secretValue" ( dict "Key" "APP_BEDROCK_ACCESS_KEY" "Value" .accessKey ) }}
  {{- include "app.secretValue" ( dict "Key" "APP_BEDROCK_SECRET_ACCESS_KEY" "Value" .secretAccessKey ) }}
  {{- end }}

  # global C@P integration settings
  {{- with .Values.app.cap }}
  {{- include "app.secretValue" ( dict "Key" "APP_CAP_PPN_HOSTNAME" "Value" .ppnHostname ) }}
  {{- include "app.secretValue" ( dict "Key" "APP_CAP_PPN_RESOURCE" "Value" .ppnResource ) }}
  {{- include "app.secretValue" ( dict "Key" "APP_CAP_PPN_CLIENT_ID" "Value" .ppnClientId ) }}
  {{- include "app.secretValue" ( dict "Key" "APP_CAP_PPN_CLIENT_SECRET" "Value" .ppnClientSecret ) }}
  {{- include "app.secretValue" ( dict "Key" "APP_CAP_BASEURL" "Value" .baseUrl ) }}
  {{- include "app.secretValue" ( dict "Key" "APP_CAP_REGION" "Value" .region ) }}
  {{- include "app.secretValue" ( dict "Key" "APP_CAP_SERVICE_GROUP" "Value" .serviceGroup ) }}
  {{- include "app.secretValue" ( dict "Key" "APP_CAP_ENV" "Value" .env ) }}
  {{- include "app.secretValue" ( dict "Key" "APP_CAP_CLIENT_ID" "Value" .clientId ) }}
  {{- include "app.secretValue" ( dict "Key" "APP_CAP_CLIENT_SECRET" "Value" .clientSecret ) }}
  {{- include "app.secretValue" ( dict "Key" "APP_CAP_AUTH_GROUP" "Value" .authGroup ) }}
  {{- include "app.secretValue" ( dict "Key" "APP_CAP_MARKET_ID" "Value" .marketId ) }}
  {{- include "app.secretValue" ( dict "Key" "APP_CAP_LANGUAGE" "Value" .language ) }}
  {{- include "app.secretValue" ( dict "Key" "APP_CAP_DATA_ORIGIN" "Value" .dataOrigin ) }}
  {{- end }}

  # mapbox Api settings
  {{- with .Values.app.mapboxApi }}
  {{- include "app.secretValue" ( dict "Key" "APP_MAPBOX_API_URL" "Value" .url ) }}
  {{- include "app.secretValue" ( dict "Key" "APP_MAPBOX_API_ACCESS_TOKEN" "Value" .accessToken ) }}
  {{- end }}

    # Japan Postal Code Api settings
  {{- with .Values.app.japanPostalCodeApi }}
  {{- include "app.secretValue" ( dict "Key" "APP_JAPAN_POSTAL_CODE_API_URL" "Value" .url ) }}
  {{- include "app.secretValue" ( dict "Key" "APP_JAPAN_POSTAL_CODE_API_CLIENT_ID" "Value" .clientId ) }}
  {{- include "app.secretValue" ( dict "Key" "APP_JAPAN_POSTAL_CODE_API_SECRET_KEY" "Value" .secretKey ) }}
  {{- end }}