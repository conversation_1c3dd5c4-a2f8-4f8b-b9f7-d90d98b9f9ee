{"resetPassword": {"subject": "{{companyName}} : Reset Password", "introTitle": "Have you forgotten the password?", "greeting": "Dear {{username}},", "body": {"main": "Have you forgotten the password for your account? We received a request to reset the password for your account.", "ignore": "If you did not request to have your password reset or no longer wish to change your password, you can ignore this email."}, "link": {"name": "Reset Password", "expiration": "This link is only valid for 15 minutes."}}, "recipientName": "{{customerName}}", "createUser": {"subject": "{{companyName}} : Welcome!", "introTitle": "Welcome! Confirm Your Account Activation.", "greeting": "Dear {{displayName}},", "body": {"welcome": "Welcome to Porsche Lead C@Pture Tool."}, "link": {"name": "Setup your password & activate your account", "expiration": "Activation link will expire in 7 days. Please contact your administrator if it has expired."}, "general": {"disclaimerText": "This e-mail has been generated automatically. Direct replies are not possible. {{companyName}} is not responsible for any content published on external websites.", "legalNotice": "Legal Notice", "privacyPolicy": "Privacy Policy", "copyright": "© {{currentYear}} {{companyCopyright}}. All rights reserved."}}, "exportApplicationPassword": {"subject": "{{companyName}}: {{applicationType}} {{documentType}} Downloaded", "greeting": "Dear {{userName}},", "body": {"typeAndDate": "The password for the {{applicationType}} {{documentType}} you downloaded on {{date}} is", "password": "{{password}}"}}, "leadExportReady": {"subject": "{{companyName}}: Your Requested Leads File", "greeting": "Dear {{userName}},", "requestMessage": "The Leads file you requested on {{date}} is attached."}, "leadExportPassword": {"subject": "[Confidential] {{companyName}}: Leads File Password", "greeting": "Dear {{userName}},", "requestMessage": "The password for the Leads file is:"}, "eventLeadExportReady": {"subject": "{{companyName}}: Your {{leadStageType}} Excel File", "greeting": "Dear {{userName}},", "requestMessage": "The {{leadStageType}} file you requested on {{date}} is attached."}, "eventLeadExportPassword": {"subject": "[Confidential] {{companyName}}: {{leadStageType}} File Password", "greeting": "Dear {{userName}},", "requestMessage": "The password for the {{leadStageType}} file is:"}, "eventLeadExportFail": {"subject": "{{companyName}}: Unable to Download {{leadStageType}}", "greeting": "Dear {{userName}},", "requestMessage": "We were unable to process your {{leadStageType}} file request on {{date}}.", "supportMessage": "Please try again or contact support for assistance."}, "leadExportFail": {"subject": "{{companyName}}: Unable to Download Leads", "greeting": "Dear {{userName}},", "requestMessage": "We were unable to process your leads file request on {{date}}.", "supportMessage": "Please try again or contact support for assistance."}, "customerExportReady": {"subject": "{{companyName}}: Your Requested Customer Records File", "greeting": "Dear {{userName}},", "requestMessage": "The Customer Records file you requested on {{date}} is attached."}, "customerExportPassword": {"subject": "[Confidential] {{companyName}}: Customer Records File Password", "greeting": "Dear {{userName}},", "requestMessage": "The password for the Customer Records file is:"}, "customerExportFail": {"subject": "{{companyName}}: Unable to Download Customer Records", "greeting": "Dear {{userName}},", "requestMessage": "We were unable to process your Customer Records file request on {{date}}.", "supportMessage": "Please try again or contact support for assistance."}, "applicationExportReady": {"subject": "{{companyName}}: Your Requested {{applicationType}} File", "greeting": "Dear {{userName}},", "requestMessage": "The {{applicationType}} file you requested on {{date}} is attached."}, "applicationExportPassword": {"subject": "[Confidential] {{companyName}}: {{applicationType}} File Password", "greeting": "Dear {{userName}},", "requestMessage": "The password for the {{applicationType}} file is:"}, "applicationExportFail": {"subject": "{{companyName}}: Unable to Download {{applicationType}}", "greeting": "Dear {{userName}},", "requestMessage": "We were unable to process your {{applicationType}} file request on {{date}}.", "supportMessage": "Please try again or contact support for assistance."}, "applicationTypes": {"Financing": "Finance Applications", "Appointment": "Test Drives", "Insurance": "Insurance Applications", "Mobility": "Applications", "Reservation": "Reservations", "VisitAppointment": "Showroom Visits", "Lead": "Leads", "TradeIn": "Trade-In"}, "shareDetails": {"subject": "{{companyName}}: {{vehicleName}}", "comparisonSubject": "{{companyName}}: {{vehicleName}} Comparison", "greeting": "Dear {{customerName}},", "body": {"thankyou": "Thank you for your interest. Your requested information, as attached, is accurate only at the point of sharing.", "contact": "Please contact {{<PERSON><PERSON><PERSON>}} at {{phoneNumber}} if you have further enquiries."}, "fileName": "SharedApplication"}, "otp": {"subject": "{{company}}: OTP to Complete Application ID {{applicationId}}", "greeting": "Dear {{displayName}}", "code": "Your OTP is: {{code}}", "expiration": "(This code will expire in {{expiration}} minutes)"}, "verifyEmailUpdate": {"subject": "{{companyName}} : Verify Email Address", "greeting": "Dear {{displayName}}", "body": {"description": "A request has been made to change the email address of your account.", "link": {"name": "Verify Em<PERSON> Address"}, "ignore": "If you did not request or no longer wish to change your email address, you can ignore this email."}}, "configurator": {"general": {"connectText": "Connect with {{ companyName }}", "disclaimerText": "This e-mail has been generated automatically. Direct replies are not possible. {{companyName}} is not responsible for any content published on external websites.", "colorSection": "Colors", "packageSection": "Packages", "contactUs": "If you have any questions, please contact:", "legalNotice": "Legal Notice", "privacyPolicy": "Privacy Policy", "copyright": "© {{currentYear}} {{companyCopyright}}. All rights reserved."}, "saveOrder": {"subject": "{{companyName}}: Your {{variantName}}", "introTitle": "So close you can almost touch it", "contentText": "Great news, your dream car is within reach - you have just a few more clicks to go.\n\nWhile youre taking a quick pit stop, we've saved your configuration for when you're ready to jump back in.", "selectionText": "You have also selected:", "resumeApplication": "Resume my {{companyName}} configuration"}, "submitOrder": {"subject": "{{companyName}}: {{submissionType}} Submitted", "subjectSalesperson": "{{companyName}}: {{submissionType}} {{applicationId}} has been submitted by {{customerName}}", "requestForFinancingSubjectSalesperson": "{{companyName}}: Request for Financing ID {{applicationId}}: {{customerName}}", "introSalesperson": {"default": "{{submissionType}} {{applicationId}} has been submitted by {{customerName}}", "requestForFinancing": "{{customerName}} has requested for financing: ID {{applicationId}}"}, "introTitle": "Dream Payment Forget dream car. Now, it's your real car.", "contentText": "Dear {{customerName}},\n\nThank you for customising your dream.\n\nFrom its iconic design DNA to innovative and powerful performance, get ready to experience ultimate driving pleasure behind the wheel of a true sports car.\n\nOne of our {{companyName}} Sales Consultants will be in touch soon.\n\nReference Number: {{applicationId}}", "orderSummary": "Your {{variantName}} Order Summary", "paymentInformation": "Payment Information", "financeSummary": "Finance Summary", "reservation": "Reservation Deposit", "reservationValue": "Included", "totalPrice": "Total Price", "pricingSectionText": "Pricing", "appointment": "Booking Information"}}, "event": {"general": {"connectText": "Connect with {{ companyName }}", "disclaimerText": "This e-mail has been generated automatically. Direct replies are not possible. {{companyName}} is not responsible for any content published on external websites.", "contactUs": "If you have any questions, please contact:", "legalNotice": "Legal Notice", "privacyPolicy": "Privacy Policy", "copyright": "© {{currentYear}} {{companyCopyright}}. All rights reserved."}, "submitOrder": {"subject": "{{companyName}}: {{submissionType}} Submitted", "introTitle": "{{variantName}}", "contentText": "Congratulations on the purchase of your new {{companyName}} {{modelName}}. You're about to embark on a journey fuelled by sheer exhilaration and thrilling adventures. Get ready to experience what {{companyName}} enthusiasts around the world describe as a truly transformative moment.", "subjectSalesperson": "{{companyName}}: {{submissionType}} {{applicationId}} has been submitted by {{customerName}}", "introSalesperson": "{{variantName}}", "contentTextSalesperson": "{{submissionType}} {{applicationId}} has been submitted by {{customerName}}"}}, "standard": {"general": {"connectText": "Connect with {{ companyName }}", "disclaimerText": "This e-mail has been generated automatically. Direct replies are not possible. {{companyName}} is not responsible for any content published on external websites.", "contactUs": "If you have any questions, please contact:", "legalNotice": "Legal Notice", "privacyPolicy": "Privacy Policy", "copyright": "© {{currentYear}} {{companyCopyright}}. All rights reserved."}, "proceedOrder": {"subject": "Additional Information Required for Your Application", "introTitle": "You're one step closer to your dream car.", "contentText": "<b>Dear {{customerName}}</b>,\n\nTo proceed with your application {{applicationId}} for {{variantName}}, we require some additional information.\n\nPlease follow the link below to proceed:", "contentTextContact": "This application link will expire in 3 days.\n\nShould you have any queries, please contact your Sales Consultant, {{salesPersonName}} at +{{prefix}} {{phone}}.\n\nReference Number: {{applicationId}}\n\nSincerely\n\nYour {{companyName}} Team,", "financeSummary": "Finance Summary"}}, "appointment": {"general": {"connectText": "Connect with {{ companyName }}", "disclaimerText": "This e-mail has been generated automatically. Direct replies are not possible. {{companyName}} is not responsible for any content published on external websites.", "contactUs": "If you have any questions, please contact:", "legalNotice": "Legal Notice", "privacyPolicy": "Privacy Policy", "copyright": "© {{currentYear}} {{companyCopyright}}. All rights reserved."}, "customerSubmitConfirmation": {"subject": "{{companyName}}: Test Drive Submitted", "introTitle": "You will be contacted shortly to confirm the Test Drive", "contentText": "Please note Test Drive is subject to availability of the model and the vehicle. Booking will be cancelled if Vehicle is purchased."}, "customerEndTestDriveWithProcess": {"subject": "{{companyName}}: Test Drive Ended", "introTitle": "Enjoyed your Test Drive car and like to own it?", "contentText": "Reserve the car via this link: <url>{{link}}</url>"}, "customerCompleteTestDriveWithoutProcess": {"subject": "{{companyName}}: Test Drive Completed", "introTitle": "Enjoyed your Test Drive car and like to own it?", "contentText": "Reserve the car via this link: <url>{{link}}</url>"}, "customerBookingAmendment": {"subject": "{{companyName}}: Test Drive Booking Updated", "introTitle": "Your updated Test Drive Booking", "contentText": "A change in Test Drive Booking details has been made. Please note Test Drive is subject to availability of the vehicle. Booking will be cancelled if Vehicle is purchased.<br><br>**Appointment Date: {{apptDate}}**<br><br>**Appointment Time: {{apptTime}}**"}, "customerBookingConfirmation": {"subject": "{{companyName}}: Test Drive Confirmed", "introTitle": "Your Test Drive is confirmed", "contentText": "Please note Test Drive is subject to availability of the vehicle. Booking will be cancelled if Vehicle is purchased."}, "customerBookingCancellation": {"subject": "{{companyName}}: Test Drive {{identifier}} Cancelled", "introTitle": "Hi {{customerName}},\nYour Test Drive has been cancelled", "contentText": "Should you have any queries, please contact {{assignee.name}} at {{assignee.phone}}."}, "salesPersonSubmitConfirmation": {"subject": "{{companyName}}: Test Drive {{vehicleName}} {{inventoryID}} Request: {{customerName}}", "introTitle": "Customer's Test Drive Request", "contentText": "Please note Test Drive is subject to availability of the vehicle. Booking will be cancelled if Vehicle is purchased.<br><br>**Appointment Date: {{apptDate}}**<br><br>**Appointment Time: {{apptTime}}**<br><br>Please contact customer to confirm booking."}, "salesPersonBookingConfirmation": {"subject": "{{companyName}}: Test Drive {{vehicleName}} {{inventoryID}} Confirmed: {{customerName}}", "introTitle": "Customer's Test Drive Confirmed", "contentText": "Test Drive Booking has been confirmed. Please note Test Drive is subject to availability of the vehicle. Booking will be cancelled if Vehicle is purchased.<br><br>**Appointment Date: {{apptDate}}**<br><br>**Appointment Time: {{apptTime}}**"}, "salesPersonBookingCancellation": {"subject": "{{companyName}}: Test Drive ID {{identifier}}: Cancelled: {{customerName}}", "introTitle": "{{initiator<PERSON>ame}} has cancelled Test Drive ID: {{identifier}}", "contentText": "You can access this test drive using the following link: <url>{{link}}</url>"}, "salesPersonBookingAmendment": {"subject": "{{companyName}}: Test Drive {{vehicleName}} {{inventoryID}} Updated: {{customerName}}", "introTitle": "Customer's Test Drive Updated", "contentText": "A change in Test Drive Booking details has been made. Please note Test Drive is subject to availability of the vehicle. Booking will be cancelled if Vehicle is purchased.<br><br>**Appointment Date: {{apptDate}}**<br><br>**Appointment Time: {{apptTime}}**"}, "salesPersonFinderReservation": {"subject": "{{companyName}}: Test Drive {{vehicleName}} {{inventoryID}} Reserved: {{customerName}}", "introTitle": "{{vehicleName}} {{inventoryID}} is reserved", "contentText": "Please rearranged below upcoming Test Drive bookings of this vehicle.", "listTestDriveTitle": "List of Test Drive Bookings", "listTestDriveItem": "{{identifier}} - <url>{{link}}</url>"}, "salesPersonEndTestDriveReminder": {"subject": "{{companyName}}: Action Required - End Test Drive Booking", "introTitle": "Action Required - End Test Drive Booking", "contentText": "Our record indicates that the Test Drive Booking started more than {{hours}} hours ago and is still alive in the system. If the test drive has been completed, please log in at your earliest convenience to close the booking: <url>{{link}}</url>"}}, "showroomVisitAppointment": {"general": {"connectText": "Connect with {{ companyName }}", "disclaimerText": "This e-mail has been generated automatically. Direct replies are not possible. {{companyName}} is not responsible for any content published on external websites.", "contactUs": "If you have any questions, please contact:", "legalNotice": "Legal Notice", "privacyPolicy": "Privacy Policy", "copyright": "© {{currentYear}} {{companyCopyright}}. All rights reserved."}, "customerSubmitConfirmation": {"subject": "{{companyName}}: Showroom Visit Received", "introTitle": "You will be contacted shortly to confirm the Showroom Visit appointment.", "contentText": "Please note that the Showroom Visit appointment is subject to the availability of both the showroom and an assigned salesperson. We will contact you shortly to confirm the details."}, "customerBookingAmendment": {"subject": "{{companyName}}: Showroom Visit Updated", "introTitle": "Your Showroom Visit appointment is updated.", "contentText": "A change in Showroom Visit appointment details has been made.<br><br>**Appointment Date: {{apptDate}}**<br><br>**Appointment Time: {{apptTime}}**"}, "customerBookingComplete": {"subject": "{{companyName}}: Showroom Visit Completed", "introTitle": "Thanks for visiting our showroom.", "contentText": "Thank you for visiting our showroom today. We hope you found the experience informative.<br><br>Should you have any queries, please contact {{assignee.name}} at {{assignee.phone}}.<br><br>We appreciate your interest and time."}, "customerBookingConfirmation": {"subject": "{{companyName}}: Showroom Visit Confirmed", "introTitle": "Your Showroom Visit appointment is confirmed.", "contentText": "Your Showroom Visit appointment has been successfully confirmed.<br><br>We look forward to welcoming you. Should you need to make any changes to your appointment, please contact us in advance."}, "customerBookingCancellation": {"subject": "{{companyName}}: Showroom Visit {{identifier}} Cancelled", "introTitle": "Hi {{customerName}},\nYour Showroom Visit has been cancelled", "contentText": "Should you have any queries, please contact {{assignee.name}} at {{assignee.phone}}."}, "salesPersonSubmitConfirmation": {"subject": "{{companyName}}: Showroom Visit Request: {{customerName}}", "introTitle": "Customer's Showroom Visit Request.", "contentText": "Please contact customer to confirm the appointment."}, "salesPersonBookingConfirmation": {"subject": "{{companyName}}: Showroom Visit {{vehicleName}} {{inventoryID}} Confirmed: {{customerName}}", "introTitle": "Customer's Showroom Visit Confirmed.", "contentText": "Showroom Visit appointment has been confirmed.<br><br>**Appointment Date: {{apptDate}}**<br><br>**Appointment Time: {{apptTime}}**"}, "salesPersonBookingAmendment": {"subject": "{{companyName}}: Showroom Visit Updated: {{customerName}}", "introTitle": "Customer's Showroom Visit Updated.", "contentText": "A change in Showroom Visit appointment details has been made.<br><br>**Appointment Date: {{apptDate}}**<br><br>**Appointment Time: {{apptTime}}**"}, "salesPersonBookingCancellation": {"subject": "{{companyName}}: Showroom Visit ID {{identifier}}: Cancelled: {{customerName}}", "introTitle": "{{initiator<PERSON>ame}} has cancelled Showroom Visit ID: {{identifier}}", "contentText": "You can access this Showroom Visit using the following link: <url>{{link}}</url>"}}, "standardApplication": {"general": {"connectText": "Connect with {{ companyName }}", "disclaimerText": "This e-mail has been generated automatically. Direct replies are not possible. {{companyName}} is not responsible for any content published on external websites.", "contactUs": "If you have any questions, please contact:", "legalNotice": "Legal Notice", "privacyPolicy": "Privacy Policy", "copyright": "© {{currentYear}} {{companyCopyright}}. All rights reserved."}, "customerShare": {"subject": "{{companyName}}: {{vehicleName}}", "introTitle": "", "contentText": "Thank you for your interest. Your requested information, as attached, is accurate only at the point of sharing.<br><br>Please contact {{salePerson}} at {{phoneNumber}} if you have further enquiries.", "fileName": "{{vehicleName}} PFS Calculation Example"}, "customerComparisonShare": {"subject": "{{companyName}}: {{vehicleName}} Comparison", "introTitle": "", "contentText": "Thank you for your interest. Your requested information, as attached, is accurate only at the point of sharing.<br><br>Please contact {{salePerson}} at {{phoneNumber}} if you have further enquiries.", "fileName": "{{vehicleName}} PFS Calculation Example"}, "customerProceedWithCustomerDevice": {"subject": "Additional Information Required for Your Application", "introTitle": "You're one step closer to your dream car.", "contentText": "<b>Dear {{customerName}}</b>,\n\nTo proceed with your application {{applicationId}} for {{variantName}}, we require some additional information.\n\nPlease follow the link below to proceed:"}, "customerSubmissionConfirmation": {"subject": "{{companyName}}: Application Submitted", "introTitle": "Thank you! Your application has been submitted.", "contentText": "{{hint}}{{hintLineBreak}}You will be contacted once it has been approved.<br><br>Should you have any queries, please contact {{assignee.name}} at {{assignee.phone}}."}, "customerApproved": {"subject": "{{companyName}}: Application ID {{identifier}}: Approve", "introTitle": "Your application has been approved.", "contentText": "Should you have any queries, please contact {{assignee.name}} at {{assignee.phone}}."}, "customerRejected": {"subject": "{{companyName}}: Application ID {{identifier}}: Rejected", "introTitle": "Your application has been rejected.", "contentText": "Should you have any queries, please contact {{assignee.name}} at {{assignee.phone}}."}, "customerCancelled": {"subject": "{{companyName}}: Application ID {{identifier}} Cancelled", "introTitle": "Your application has been cancelled.", "contentText": "Should you have any queries, please contact {{assignee.name}} at {{assignee.phone}}."}, "salesPersonApproved": {"subject": "{{companyName}}: Application ID {{identifier}}: Approved: {{customerName}}", "introTitle": "{{initiator<PERSON>ame}} has approved Application ID: {{identifier}}", "contentText": "You can access this application using the following link: <url>{{link}}</url>"}, "salesPersonRejected": {"subject": "{{companyName}}: Application ID {{identifier}}: Rejected: {{customerName}}", "introTitle": "{{initiator<PERSON>ame}} has rejected Application ID: {{identifier}}", "contentText": "You can access this application using the following link: <url>{{link}}</url>"}, "salesPersonCancelled": {"subject": "{{companyName}}: Application ID {{identifier}}: Cancelled: {{customerName}}", "introTitle": "{{initiator<PERSON>ame}} has cancelled Application ID: {{identifier}}", "contentText": "You can access this application using the following link: <url>{{link}}</url>"}}, "finder": {"general": {"connectText": "Connect with {{ companyName }}", "disclaimerText": "This e-mail has been generated automatically. Direct replies are not possible. {{companyName}} is not responsible for any content published on external websites.", "contactUs": "If you have any questions, please contact:", "legalNotice": "Legal Notice", "privacyPolicy": "Privacy Policy", "copyright": "© {{currentYear}} {{companyCopyright}}. All rights reserved."}}, "component": {"payment": {"cardType": "Card Type", "purchaseDate": "Date & Time of Purchase", "depositAmount": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "email": "Email Address", "invoiceNumber": "Invoice Number", "nameOnCard": "Name on Card", "emailAddress": "Email Address", "paymentMethod": "Payment Method", "purchaseTime": "Date & Time of Purchase", "amountTransacted": "Amount Transacted", "reservationDeposit": "Reservation Deposit"}, "vehicle": {"power": "Power", "engine": "Engine", "mileage": "Mileage", "registration": "First Registration", "startingPrice": "Starting Price", "stockID": "Stock ID: {{stock_id}}", "transmission": "Transmission", "exteriorColor": "Exterior Color", "interiorColor": "Interior Color", "acceleration": "Acceleration 0-100km/h", "displacement": "Displacement"}, "applicationStage": {"lead": "Lead", "contact": "Contact", "reservation": "Reservation", "financing": "Application", "insurance": "Insurance", "appointment": "Test Drive", "visitAppointment": "Showroom Visit", "mobility": "Mobility"}, "leadStage": {"lead": "Lead", "contact": "Contact"}, "appointment": {"id": "Appointment ID", "purpose": "Appointment Type", "date": "Appointment Date/Time", "testDrive": "Test Drive", "showroomVisit": "Showroom Visit"}, "title": {"priceSummary": "Price Summary", "customerSelection": "Customer Selection", "bookingInformation": "Booking Information", "technicalData": "Technical Data", "paymentInformation": "Payment Information", "financeApplicationSummary": "Finance Application Summary", "insuranceApplicationSummary": "Insurance Application Summary"}, "customerSelection": {"preferredDealership": "Preferred Dealership", "vehicleModel": "Vehicle Model", "vehicleVariant": "Vehicle Variant"}, "insurance": {"id": "Insurance Application ID", "premium": "Insurance Premium", "legalName": "Insurance Product", "sumInsured": "Sum Insured"}, "reservation": {"id": "Reservation ID"}, "financing": {"id": "Finance ID"}}, "updateSalesperson": {"greeting": "Hi {{assignee}},", "subject": "{{companyName}}: {{submissionType}} {{applicationId}} has been assigned to you", "body": {"information": "{{submissionType}} {{applicationId}} has been assigned to you by {{assignedBy}}.<br/><br/>You can access this {{submissionType}} using the following link: <url>{{link}}</url>"}}, "updateLocationOperator": {"greeting": "Hi {{assignee}},", "subject": "{{companyName}}: Application ID: {{identifier}} has been unassigned from you", "body": {"information": "Application ID: {{identifier}} has been unassigned from you by {{assignedBy}}."}}, "customerSubmissionConfirmation": {"subject": "{{companyName}}: Application Submitted", "greeting": "Hi {{customerName}},", "financingTitle": "Application ID: {{identifier}}", "introduction": "Thank you! Your application has been submitted.", "body": {"content": "{{hint}}{{hintLineBreak}}You will be contacted once it has been approved", "contact": "Should you have any queries, please contact {{assignee.name}} at {{assignee.phone}}."}, "hint": {"attachment": "The application is attached for your reference.", "random": "The document password will be provided in a separate email.", "birthdayOnly": "Please access the file with your Date of Birth ({{dobFormat}}).", "identityNumberOnly": "Please access the file with your Identity number.", "birthdayAndIdentityNumber": "Please access the file with your Identity number and Date of Birth (DDMMYYYY)."}}, "sendPasswordToCustomer": {"subject": "{{companyName}}: Application Submitted", "body": "The password for the Application {{applicationId}} is"}, "customerDepositConfirmation": {"subject": "{{companyName}}: Payment Invoice", "greeting": "Dear {{customerName}},", "body": {"introduction": "Thank you for your reservation. Your payment is successful.", "paymentSection": {"title": "Reservation Details", "applicationId": "Application ID: {{identifier}}", "paymentMethod": "Payment Method: {{paymentMethod}}", "paymentDate": "Payment Date: {{paymentDate}}", "amountPaid": "Amount Paid: {{paymentAmount}}", "selectedVehicle": "Selected Vehicle: {{vehicle}}"}, "contact": "Should you have any queries, please contact {{assignee.name}} at {{assignee.phone}}."}}, "customerCancelConfirmation": {"subject": "{{companyName}}: Application ID {{identifier}} Cancelled", "greeting": "Hi {{customerName}},", "introduction": "Your application has been cancelled.", "body": {"contact": "Should you have any queries, please contact {{assignee.name}} at {{assignee.phone}}."}, "general": {"connectText": "Connect with {{ companyName }}", "disclaimerText": "This e-mail has been generated automatically. Direct replies are not possible. {{companyName}} is not responsible for any content published on external websites.", "contactUs": "If you have any questions, please contact:", "legalNotice": "Legal Notice", "privacyPolicy": "Privacy Policy", "copyright": "© {{currentYear}} {{companyCopyright}}. All rights reserved."}}, "customerApproveConfirmation": {"subject": "{{companyName}}: Application ID {{identifier}}: Approved", "greeting": "Hi {{customerName}},", "introduction": "Your application has been approved.", "body": {"contact": "Should you have any queries, please contact {{assignee.name}} at {{assignee.phone}}."}, "general": {"connectText": "Connect with {{ companyName }}", "disclaimerText": "This e-mail has been generated automatically. Direct replies are not possible. {{companyName}} is not responsible for any content published on external websites.", "contactUs": "If you have any questions, please contact:", "legalNotice": "Legal Notice", "privacyPolicy": "Privacy Policy", "copyright": "© {{currentYear}} {{companyCopyright}}. All rights reserved."}}, "customerRejectConfirmation": {"subject": "{{companyName}}: Application ID {{identifier}}: Rejected", "greeting": "Hi {{customerName}},", "introduction": "Your application has been rejected.", "body": {"contact": "Should you have any queries, please contact {{assignee.name}} at {{assignee.phone}}."}, "general": {"connectText": "Connect with {{ companyName }}", "disclaimerText": "This e-mail has been generated automatically. Direct replies are not possible. {{companyName}} is not responsible for any content published on external websites.", "contactUs": "If you have any questions, please contact:", "legalNotice": "Legal Notice", "privacyPolicy": "Privacy Policy", "copyright": "© {{currentYear}} {{companyCopyright}}. All rights reserved."}}, "customerCompleteConfirmation": {"subject": "{{companyName}}: Application ID {{identifier}}: Completed", "greeting": "Hi {{customerName}},", "introduction": "Your application has been completed.", "body": {"contact": "Should you have any queries, please contact {{assignee.name}} at {{assignee.phone}}."}, "general": {"connectText": "Connect with {{ companyName }}", "disclaimerText": "This e-mail has been generated automatically. Direct replies are not possible. {{companyName}} is not responsible for any content published on external websites.", "contactUs": "If you have any questions, please contact:", "legalNotice": "Legal Notice", "privacyPolicy": "Privacy Policy", "copyright": "© {{currentYear}} {{companyCopyright}}. All rights reserved."}}, "customerUpdateConfirmation": {"subject": "{{companyName}}: Application ID {{identifier}} Updated", "greeting": "Hi {{customerName}},", "introduction": "Your application has been updated.", "body": {"content": "{{hint}}{{hintLineBreak}}You will be contacted once it has been approved", "contact": "Should you have any queries, please contact {{assignee.name}} at {{assignee.phone}}."}}, "salesPersonSubmissionConfirmation": {"subject": "{{companyName}}: Application ID {{identifier}}: {{customerName}}", "body": {"topic": "{{initiator<PERSON>ame}} has submitted Application ID: {{identifier}}", "content": "You can access this application using the following link: <url>{{link}}</url>"}}, "salesPersonUpdateConfirmation": {"subject": "{{companyName}}: Application ID {{identifier}}: Updated: {{customerName}}", "body": {"topic": "{{initiator<PERSON>ame}} has updated Application ID: {{identifier}}", "content": "You can access this application using the following link: <url>{{link}}</url>"}}, "salesPersonCancelConfirmation": {"subject": "{{companyName}}: Application ID {{identifier}}: Cancelled: {{customerName}}", "body": {"topic": "{{initiator<PERSON>ame}} has cancelled Application ID: {{identifier}}", "content": "You can access this application using the following link: <url>{{link}}</url>"}, "general": {"connectText": "Connect with {{ companyName }}", "disclaimerText": "This e-mail has been generated automatically. Direct replies are not possible. {{companyName}} is not responsible for any content published on external websites.", "contactUs": "If you have any questions, please contact:", "legalNotice": "Legal Notice", "privacyPolicy": "Privacy Policy", "copyright": "© {{currentYear}} {{companyCopyright}}. All rights reserved."}}, "salesPersonApproveConfirmation": {"subject": "{{companyName}}: Application ID {{identifier}}: Approved: {{customerName}}", "body": {"topic": "{{initiator<PERSON>ame}} has approved Application ID: {{identifier}}", "content": "You can access this application using the following link: <url>{{link}}</url>", "comment": {"header": "Comments"}}, "general": {"connectText": "Connect with {{ companyName }}", "disclaimerText": "This e-mail has been generated automatically. Direct replies are not possible. {{companyName}} is not responsible for any content published on external websites.", "contactUs": "If you have any questions, please contact:", "legalNotice": "Legal Notice", "privacyPolicy": "Privacy Policy", "copyright": "© {{currentYear}} {{companyCopyright}}. All rights reserved."}}, "salesPersonRejectConfirmation": {"subject": "{{companyName}}: Application ID {{identifier}}: Rejected: {{customerName}}", "body": {"topic": "{{initiator<PERSON>ame}} has rejected Application ID: {{identifier}}", "content": "You can access this application using the following link: <url>{{link}}</url>"}, "general": {"connectText": "Connect with {{ companyName }}", "disclaimerText": "This e-mail has been generated automatically. Direct replies are not possible. {{companyName}} is not responsible for any content published on external websites.", "contactUs": "If you have any questions, please contact:", "legalNotice": "Legal Notice", "privacyPolicy": "Privacy Policy", "copyright": "© {{currentYear}} {{companyCopyright}}. All rights reserved."}}, "salesPersonCompleteConfirmation": {"subject": "{{companyName}}: Application ID {{identifier}}: Completed: {{customerName}}", "body": {"topic": "{{initiator<PERSON>ame}} has completed Application ID: {{identifier}}", "content": "You can access this application using the following link: <url>{{link}}</url>"}, "general": {"connectText": "Connect with {{ companyName }}", "disclaimerText": "This e-mail has been generated automatically. Direct replies are not possible. {{companyName}} is not responsible for any content published on external websites.", "contactUs": "If you have any questions, please contact:", "legalNotice": "Legal Notice", "privacyPolicy": "Privacy Policy", "copyright": "© {{currentYear}} {{companyCopyright}}. All rights reserved."}}, "salesPersonDepositConfirmation": {"subject": "{{companyName}}: Payment Received - {{applicationStage}} ID {{identifier}}: {{customerName}}", "greeting": "Dear {{customerName}},", "body": {"introduction": "{{customerName}} has submitted a payment", "paymentSection": {"title": "{{applicationStage}} Details", "applicationId": "{{applicationStage}} ID: {{identifier}}", "paymentMethod": "Payment Method: {{paymentMethod}}", "paymentDate": "Payment Date: {{paymentDate}}", "amountPaid": "Amount Paid: {{paymentAmount}}", "selectedVehicle": "Selected Vehicle: {{vehicle}}"}, "contact": "Should you have any queries, please contact {{assignee.name}} at {{assignee.phone}}."}}, "bankSubmission": {"subject": "{{companyName}}: Application ID {{identifier}}: {{customerName}}", "body": {"topic": "{{initiator<PERSON>ame}} has submitted Application ID: <url>{{identifier}}</url>", "content": "The application is attached. {{hint}}"}, "passwordBody": "The password for the Application {{applicationId}} is", "hint": "The access password will be provided in a separate email."}, "bankCancellation": {"subject": "{{companyName}}: Application ID {{identifier}}: Cancelled: {{customerName}}", "body": {"topic": "{{initiator<PERSON>ame}} has cancelled Application ID: {{identifier}}", "content": "Should you have any queries, please contact {{assignee.name}} at {{assignee.phone}}."}}, "bankOrInsurerFailed": {"subject": "{{companyName}}: Action Required - Application Submission to {{bankOrInsurerName}} Failed - {{identifier}}", "introTitle": "Action Required - Application Submission to {{bankOrInsurerName}} Failed", "contentText": "Hi {{assignee}},\n\nThe application submission to {{bankOrInsurerName}} has failed. \n\n Application ID: {{identifier}} \n\n Error Details: {{errorMessage}}\n\nPlease review and take necessary action. You can access this application using the following link: <url>{{link}}</url>"}, "financingSection": {"fields": {"carPrice": {"label": "Car\nPrice"}, "coe": {"label": "COE"}, "ppsr": {"label": "PPSR"}, "estFee": {"label": "Dealer Establishment Fee"}, "bankEstFee": {"label": "{{bankLegalName}} Establishment Fee"}, "totalPrice": {"label": "Total Price"}, "financeProduct": {"label": "Finance Product"}, "securityDeposit": {"label": "Security Deposit"}, "tenure": {"label": "Tenure"}, "monthlyPayment": {"label": "Monthly Payment"}, "remarks": {"label": "Remarks"}, "bank": {"label": "Bank"}, "interestRate": {"label": "Interest Rate"}, "downPayment": {"label": "Downpayment"}, "downPaymentWithAdditional": {"label": "Downpayment"}, "loanAmount": {"label": "<PERSON><PERSON>"}, "deposit": {"label": "Security Deposit"}, "promoCode": {"label": "Promo Code"}, "carModel": {"label": "Car Model"}, "startingPrice": {"label": "Starting Price"}, "paymentMode": {"label": "Mode of Payment"}, "licensePlateFee": {"label": "License Plate Fee"}, "commission": {"label": "Commission"}, "monthlyPaymentFixedRate": {"label": "Monthly Payment (Fixed Interest Rate)"}, "displacement": {"label": "Displacement"}, "licenseAndFuelTax": {"label": "License & Fuel Tax"}, "insuranceFee": {"label": "Insurance Fee"}, "taxLoss": {"label": "Tax Loss"}, "marginOfFinance": {"label": "Margin Of Finance"}, "hirePurchase": {"label": "HP Amount"}, "assuredResaleValue": {"label": "Assured Resale Value"}, "estimatedSurplusBalloon": {"label": "Estimated Surplus After Assured Resale Value"}, "mileage": {"label": "Mileage"}, "residualValue": {"label": "Residual Value"}, "balloonPayment": {"label": "Balloon Payment"}, "totalAmountPayable": {"label": "Total Amount Payable"}, "dealerOptions": {"label": "Dealer Options"}, "discount": {"label": "Discount"}, "discountDescription": {"label": "Discount Description"}}, "paymentMethod": {"advance": "Advance", "arrears": "Arrears"}}, "insurerSubmission": {"subject": "{{companyName}}: Application ID {{identifier}}: {{customerName}}", "body": {"topic": "{{initiator<PERSON>ame}} has submitted Application ID: {{identifier}}", "content": "The application is attached. {{hint}}"}, "passwordBody": "The password for the Application {{applicationId}} is", "hint": "The access password will be provided in a separate email."}, "insurancingSection": {"fields": {"carPrice": {"label": "Car\nPrice"}, "coe": {"label": "COE"}, "ppsr": {"label": "PPSR"}, "estFee": {"label": "Dealer Establishment Fee"}, "bankEstFee": {"label": "{{bankLegalName}} Establishment Fee"}, "totalPrice": {"label": "Total Price"}, "remarks": {"label": "Remarks"}, "insurer": {"label": "Insurance Company"}, "dateOfRegistration": {"label": "Original Registration Date"}, "insuranceProduct": {"label": "Insurance Product"}, "sumInsured": {"label": "Sum Insured"}, "insurancePremium": {"label": "Insurance Premium"}}}, "displacementValue": "{{value}}cc", "commissionValue": "{{value}}%", "marginOfFinanceValue": "{{value}}%", "requestReleaseLetter": {"subject": "{{companyName}}: Application ID {{identifier}}: {{customerName}}", "body": {"topic": "{{initiator<PERSON>ame}} is requesting release letter for Application ID: {{identifier}}", "content": "Chassis No.: {{chassisNo}}<br />Engine No.: {{engineNo}}<br /><br />The application is attached. {{hint}}"}, "passwordBody": "The password for the Application {{applicationId}} is", "hint": "The access password will be provided in a separate email."}, "requestDisbursement": {"subject": "{{companyName}}: Application ID {{identifier}}: {{customerName}}", "body": {"topic": "{{initiator<PERSON>ame}} is requesting disbursement for Application ID : {{identifier}}", "content": "The documents are attached. {{hint}}"}, "passwordBody": "The password for the Application {{applicationId}} is", "hint": "The access password will be provided in a separate email."}, "mobilityEmail": {"mobilityDetails": {"addon": {"title": "Add-Ons"}, "additionalInfo": {"title": "Included Services"}}, "technicalData": {"title": "Technical Data", "labels": {"power": "Power", "acceleration": "Acceleration 0-100km/h"}}, "specifications": {"title": "Specifications", "labels": {"modelYear": "Model Year", "seats": "Seats", "transmission": "Transmission", "appleCarPlay": "Apple Car Play", "powser": "Power", "acceleration": "Acceleration 0-100km/h", "available": "Available", "unavailable": "Unavailable"}}, "priceSummary": {"title": "Price Summary", "labels": {"rentalRate": "Rental Rate", "rentalPeriod": "Rental Period", "rentalAmount": "<PERSON><PERSON> Amount", "addOns": "Add-Ons", "promoCode": "Promo Code", "giftVoucher": "Gift Voucher", "totalPrice": "Total Price"}}, "payment": {"title": "Payment Confirmation", "cardHolderName": "Cardholder Name", "cardType": "Card Type", "rentalAmount": "<PERSON><PERSON> Amount", "emailAddress": "Email Address", "referenceNumber": "Reference Number"}, "actions": {"amendBooking": "Amend Booking", "cancelBooking": "Cancel Booking"}}, "finderEmail": {"porscheFinder": {"subject": "Thank you for registering your interest on Porsche Finder", "intro": "<h1><b>Forget dream car. Now, it's your real car</b></h1>", "introSalesperson": "{{submissionType}} {{applicationId}} has been submitted by {{customerName}}", "link": "You can access this application using the following link: <url>{{link}}</url>", "content": "Dear {{ title }} {{ customerName }},\n\nThank you for registering your interest on Porsche Finder.\n\nDriving pleasure is timeless and with a {{ carCondition }}Porsche, you can enjoy affordable access to the pinnacle of sports luxury driving. Now is the time to embrace your sports car fascination and get into the driver's seat of your dream car.\n\nHang tight and one of our Sales Consultants will be in touch soon to answer any questions and help you book your test drive.", "referenceNumber": "Reference Number: {{ applicationId }}", "details": {}, "label": {"power": "Power", "engine": "Engine", "mileage": "Mileage", "registration": "First Registration", "startingPrice": "Starting Price", "coe": "COE", "totalPrice": "Total Price", "priceDisclaimer": "Price Disclaimer", "desiredTradeInAmount": "Desired Trade-in Amount", "remainingCashPayment": "Remaining Cash Payment", "financeProduct": "Finance Product", "interestRate": "Interest Rate", "downpayment": "Downpayment", "tenure": "Tenure", "loanAmount": "<PERSON><PERSON>", "monthlyPayment": "Monthly Payment", "stockID": "Stock ID: {{stock_id}}", "transmission": "Transmission", "exteriorColor": "Exterior Color", "interiorColor": "Interior Color"}}, "others": {"subject": "", "intro": "", "content": "", "details": {}}}, "insurance": {"dateOfBirthDisclaimer": "The date of birth updated here does not match the one provided in insurance premium calculator. The insurance premium provided is based on the previous date of birth provided, {{oldDateOfBirth}}. Your actual insurance premium will be reviewed and may be different."}, "promoCode": {"offValue": "{{value}} off total price", "offValueWithPercentage": "{{value}}% off total price", "freeGift": "Free {{value}}"}, "assigneeReassignment": {"subject": "{{companyName}}: Applications have been re-assigned to you", "greeting": "Hi {{new<PERSON><PERSON><PERSON>}},", "body": {"information": "Applications previously assigned to {{previous<PERSON>signee}} has been assigned to you by {{assignedBy}}", "assignedApplications": "Applications Assigned:", "assignedModules": "<PERSON><PERSON>les Assigned:", "assignedLeadGenForms": "Lead C@Pture Forms Assigned:"}}, "giftVoucher": {"confirmation": {"subject": "Gift Voucher Confirmation", "introTitle": "", "contentText": ""}, "description": {"title": "The Gift Voucher grants the recipient: ", "variantUsage": "The usage of {{variantName}} for {{duration}} days.", "alternateOptions": "Alternatively, a vehicle of their choice can be selected and an amount of up to {{value}} can be redeemed."}, "redemption": {"title": "Seamless Redemption", "subtitle": "The recipient can redeem the voucher with ease by following these steps:", "vehicle": "Visit our website at <url>{{link}}</url>", "applyGiftCode": "On the Payment Page, locate the 'Gift Code' field.", "giftCode": "Enter the unique gift voucher code: <b>{{giftCode}}</b>.", "giftCodeInstruction": "Complete the reservation process following the on-screen instructions."}, "additional": {"title": "Additional Information", "validityPeriod": "1", "period": "The gift voucher remains valid for {{start}} - {{end}}", "termsAndConditions": "For a comprehensive overview of our terms and conditions, please refer to <url>Terms and Conditions</url>", "termsAndConditionsURL": ""}, "summary": {"offValue": "{{value}} off total price", "validity": "Gift vouchers purchased are only valid for 1 year."}}, "generalEdm": {"footer": {"connectText": "Connect with {{ companyName }}", "disclaimerText": "This e-mail has been generated automatically. Direct replies are not possible. {{companyName}} is not responsible for any content published on external websites.", "contactUs": "If you have any questions, please contact:", "legalNotice": "Legal Notice", "privacyPolicy": "Privacy Policy", "copyright": "© {{currentYear}} {{companyCopyright}}. All rights reserved."}}, "emptyVariantImageReminder": {"subject": "{{companyName}}: Action Required - Input the missing Vehicle Image", "intro": "Action Required - Input the missing Vehicle Variant Images", "body": "There are currently {{noVariantImageCount}} variant(s) missing vehicle image.<br><br>Kindly take action and upload the required images promptly. Ensuring all vehicle variants have images will enhance the overall listing quality and improve user experience.<br><br>To ensure all vehicle variants have images will enhance the overall listing quality and improve user experience, please kindly upload the required images promptly in this page:<br>"}, "porscheMasterDataAutoSyncUpdate": {"subject": "{{companyName}}: Action Required - Vehicle Updates", "intro": "Action Required - Vehicle Updates", "summary": "The following vehicle updates have been detected:", "note": "Newly created variants are automatically assigned to all journeys without vehicle images. Please upload the relevant images or unassign these variants if they are not intended for use."}, "tradeInPendingToSalesManager": {"subject": "{{companyName}}: Trade-in Request - {{identifier}}", "title": "Trade-in Request", "greeting": "Dear {{salesManagerName}},", "introduction": "{{salesConsultantName}} has requested a trade-in vehicle value for {{customerName}}:", "body": {"vehicleMakeModel": "Make & Model: {{vehicleMakeModel}}", "vehicleYear": "Manufacturing Year: {{vehicleYear}}", "vin": "Engine/Motor Number: {{vin}}", "mileage": "Mileage: {{mileage}} km", "transfersCount": "Transfers Count: {{transfersCount}}", "vehicleHandoverDate": "Vehicle Handover Date: {{vehicleHandoverDate}}"}, "linkContent": {"message": "Please review the request and provide the trade-in value at your earliest convenience."}, "signature": "Your Porsche Singapore Team"}, "tradeInVehicleChangedToSalesConsultant": {"subject": "{{companyName}}: Trade-in Request - {{identifier}}", "title": "Trade-in Request", "greeting": "Dear {{salesConsultantName}},", "introduction": "{{salesManagerName}} has provided the trade-in vehicel value for {{customerName}}:", "body": {"vehicleMakeModel": "Make & Model: {{vehicleMakeModel}}", "vehicleYear": "Manufacturing Year: {{vehicleYear}}", "vin": "Engine/Motor Number: {{vin}}", "mileage": "Mileage: {{mileage}} km", "transfersCount": "Transfers Count: {{transfersCount}}", "vehicleHandoverDate": "Vehicle Handover Date: {{vehicleHandoverDate}}", "tradeInValue": "Trade-in Value: {{tradeInValue}}"}, "linkContent": {"message": "Please proceed with informing the customer."}, "signature": "Your Porsche Singapore Team"}, "tradeInVehicleChangedToSalesManager": {"subject": "{{companyName}}: Trade-in Request - {{identifier}}", "title": "Trade-in Request", "greeting": "Dear {{salesManagerName}},", "introduction": "{{salesConsultantName}} has requested a trade-in vehicle value for {{customerName}}:", "body": {"vehicleMakeModel": "Make & Model: {{vehicleMakeModel}}", "vehicleYear": "Manufacturing Year: {{vehicleYear}}", "vin": "Engine/Motor Number: {{vin}}", "mileage": "Mileage: {{mileage}} km", "transfersCount": "Transfers Count: {{transfersCount}}", "vehicleHandoverDate": "Vehicle Handover Date: {{vehicleHandoverDate}}"}, "linkContent": {"message": "Please review the request and provide the trade-in value at your earliest convenience."}, "signature": "Your Porsche Singapore Team"}, "salesOffer": {"contactUs": "If you have any questions, please contact:", "deposit": "Make Payment", "signing": "Sign Now"}, "followUpPlanned": {"subject": "{{companyName}}: New Follow-Up - {{customerName}}", "title": "Follow-Up Scheduled", "contentText": "A Follow-Up has been scheduled for **{{customerName}}** and assigned to **{{assignee.name}}**.\n\n**Next Action**: Please contact the customer as planned.\n\n{{identifier}} - <url>{{link}}</url>"}, "followUpUpdated": {"subject": "{{companyName}}: Follow-Up Rescheduled - {{customerName}}", "title": "Follow-Up Details Updated", "contentText": "The Follow-Up for **{{customerName}}** (assigned to **{{assignee.name}}**) has been rescheduled.\n\n**New Follow-Up Date**: {{apptDate}}\n\n**New Follow-Up Time**: {{apptTime}}\n\n**Next Action**: Please Follow-Up at the updated time.\n\n{{identifier}} - <url>{{link}}</url>"}, "followUpCancelled": {"subject": "{{companyName}}: Follow-Up Cancelled - {{customerName}}", "title": "Follow-Up Cancelled", "contentText": "The Follow-Up for **{{customerName}}** (assigned to **{{assignee.name}}**) has been cancelled.\n\n{{identifier}} - <url>{{link}}</url>"}}