{"title": "Consents", "addTitle": "Consent", "detailTitle": "Consent - {{name}}", "additionalConsentsTitle": "Additional Consents", "cardTitles": {"configuration": "Main Details", "consentDetails": "Consent Details", "translations": "Translations", "conditions": "Conditions"}, "columns": {"company": "Company", "displayName": "Display Name", "orderNumber": "Order No.", "type": "Type", "isMandatory": "Mandatory", "isActive": "Active", "actions": "Actions"}, "fields": {"displayName": {"label": "Display name"}, "consentType": {"label": "Consent Type"}, "title": {"label": "Title"}, "module": {"label": "<PERSON><PERSON><PERSON>"}, "hasCheckbox": {"label": "Checkbox"}, "isMandatory": {"label": "Mandatory"}, "isActive": {"label": "Active"}, "description": {"label": "Description"}, "groupDescription": {"label": "Group Description"}, "hasLegalMarkup": {"label": "Display in Modal"}, "legalMarkup": {"label": "Legal Text"}, "actions": {"label": "Actions"}, "orderNumber": {"label": "Order No."}, "location": {"label": "Location"}, "placement": {"label": "Placement"}, "phone": {"label": "Phone"}, "email": {"label": "Email"}, "sms": {"label": "SMS"}, "mail": {"label": "Mail"}, "fax": {"label": "Fax"}, "dataField": {"label": "Data Field"}, "moduleName": {"label": "<PERSON><PERSON><PERSON>"}, "conditionType": {"label": "Condition Type"}, "applicationModule": {"label": "Application Module"}, "giftVoucherModule": {"label": "Gift Codes"}, "bank": {"label": "Bank"}, "dealer": {"label": "Dealer"}, "insurer": {"label": "Insurer"}, "defaultChecked": {"label": "<PERSON><PERSON><PERSON> Checked"}, "legalTextPosition": {"label": "Legal Text Placement"}, "salesOfferAgreement": {"label": "Sales Offer Agreement"}}, "tooltips": {"legalTextPosition": "Auto-enabled when Legal Text is provided."}, "logicConditionDivider": "Or", "andDivider": "AND", "orDivider": "OR", "addCondition": "Add new condition", "removeLogicCondition": "Remove condition", "addConditionInGroup": "Add new condition in group", "removeCondition": "Remove group condition", "addLogicCondition": "Add '{{conditionType}}' Condition", "conditionLabels": {"isApplicationModule": "Application module is <bold>{{displayName}}</bold>", "isDealer": "Dealer is <bold>{{displayName}}</bold>", "isInsurer": "Insurer is <bold>{{displayName}}</bold>", "isBank": "Bank is <bold>{{displayName}}</bold>", "isApplyingForFinancing": "Application is applying for financing", "isApplyingForInsurance": "Application is applying for insurance", "isApplicant": "Customer is applicant", "isGuarantor": "Customer is guarantor", "isCorporate": "Customer is corporate", "isTradeIn": "Application has trade in", "isTestDrive": "Application has test drive", "isShowroomVisit": "Application has showroom visit", "withMyinfo": "With <PERSON><PERSON><PERSON>", "withoutMyinfo": "Without Myinfo", "isLocation": "Location is <bold>{{displayName}}</bold>", "isTestDriveProcess": "Test Drive Process", "isGiftVoucher": "Gift Codes Module is <bold>{{displayName}}</bold>", "forCapQualification": "For C@P Qualification", "salesOfferAgreement": "Sales Offer Agreement is for <bold>{{displayName}}</bold>"}, "conditionsDrawer": {"title": "Add Condition", "cancelButton": "Cancel", "submitButton": "Add", "messages": {"success": "Condition added", "remove": "Condition removed", "incompleteCondition": "Some conditions are incomplete. Please complete or delete them."}}, "actions": {"newConsentsAndDeclarations": "New Consent", "create": "Create", "manage": "manage", "update": "Update", "delete": "Delete", "addAdditionalConsents": "Add Consent", "addMore": "Add More"}, "noData": {"message": "There is no consent and declaration in the system yet, do you want to create one ?", "action": "Create a Consent and Declaration"}, "creationModal": {"okText": "Create", "cancelText": "Cancel", "title": "New Consent and Declaration"}, "messages": {"creationSubmitting": "Creating new Consent and Declaration..", "creationSuccessful": "New Consent and Declaration created", "updateSubmitting": "Updating Consent and Declaration..", "updateSuccessful": "Consent and Declaration updated", "deleteSubmitting": "Requesting Consent and Declaration deletion..", "deleteSuccessful": "Consent and Declaration deleted"}, "deleteModal": {"title": "Deletion", "content": "Do you confirm the deletion for this Consent and Declaration ? This action cannot be reverted.", "okText": "Confirm & Delete", "cancelText": "Cancel"}, "options": {"purpose": {"share": "Share", "kyc": "KYC", "payment": "Payment"}, "consentTypes": {"text": "Consent With Text Only", "checkbox": "Consent With Checkbox", "marketing": "Marketing Consent", "group": "Group Consents with Checkboxes"}, "dataField": {"none": "None", "dataProcessing": "Data Processing"}, "conditionTypes": {"and": "'And' Condition", "or": "'Or' Condition", "applicationModule": "Match Application Module", "bank": "Match Bank", "dealer": "Match Dealer", "insurer": "Match Insurer", "applicant": "Customer is applicant", "guarantor": "Customer is guarantor", "corporate": "Customer is corporate", "testDrive": "Has test drive", "showroomVisit": "Has showroom visit", "tradeIn": "Has trade in", "applyingForFinancing": "Is Applying For Financing", "applyingForInsurance": "Is Applying For Insurance", "withMyinfo": "With <PERSON><PERSON><PERSON>", "withoutMyinfo": "Without Myinfo", "location": "Match Location", "isTestDriveProcess": "Test Drive Process", "giftVoucher": "Gift Codes", "forCapQualification": "For C@P Qualification", "salesOfferAgreements": "Sales Offer Agreements"}, "legalTextPosition": {"before": "Display before Description", "after": "Display after Description", "modal": "Display in Modal"}}}