{"title": "Sales Control Board", "actions": {"filter": "Filter", "reset": "Reset Filters", "applyFilters": "Apply Filters", "import": "Import", "export": "Export"}, "modal": {"title": "Import Data", "error": {"title": "Partial Import Completed", "description": "The file [{{ fileName }}] was partially imported. Some rows were skipped because they contain missing or incomplete information.", "subDescription": "Please fix the issues and re-import the affected rows."}, "description": "Please select the Data Type and upload the indicated file.", "fields": {"dataType": {"label": "Data Type"}, "reportingMonth": {"label": "Reporting Month"}}, "buttons": {"submit": "Submit", "cancel": "Cancel", "understood": "Understood"}, "file": {"placeholder": "Select file or drop file here", "button": "Select File", "allowExtension": "Accepted format: CSV."}}, "noCompanySelected": "Please select Company to view Sales Control Board.", "noDealerSelected": "Please select Dealer to view Sales Control Board.", "noDataWarningOnMonth": "There is no data imported for type {{ types }} on {{ month }}.", "noDataWarning": "There is no data imported yet.", "performanceOverview": {"titleForManager": "Performance Overview - {{ selected<PERSON>onth }}", "titleForConsultant": "My Performance Overview - {{ selected<PERSON>onth }}", "leadsCreated": "Leads Created", "testDrives": "Test Drives", "salesOffers": "Sales Offers", "orderIntakes": "Order Intakes", "retails": "Retails"}, "progressToGoal": {"title": "Progress to Goal - {{ <PERSON><PERSON><PERSON><PERSON> }}", "mtd": "Month to Date", "ytd": "Year to Date", "finance": "Finance", "insurance": "Insurance", "target": "Target: ", "percentage": "{{ percentage }}%", "dev": "{{ dev }} units", "actualOfTarget": "{{ actual }} of {{ target }} units", "ofTarget": " (of {{ target }})"}, "performance": {"titleForManager": "Sales Consultant Performance - {{ selectedMonth }}", "titleForConsultant": "My Performance - {{ <PERSON><PERSON><PERSON><PERSON> }}", "tabs": {"overview": "Overview", "fiCommissions": "F&I Commissions"}, "fiCommissions": {"consultant": "Consultant", "inHouseFinance": "In-House Finance", "inHouseInsurance": "In-House Insurance", "target": "Target", "mtd": "MTD", "ytd": "YTD", "3MAvg": "3M Avg", "total": "Total"}}, "weekSalesFunnel": {"title": "Week {{ count }} Sales Funnel: {{ dateRange }}", "conversionRate": "Conversion Rate", "leadToTestDrive": "Leads Created to Test Drives", "testDriveToSalesOffer": "Test Drives to Sales Offers", "salesOfferToOrderIntake": "Sales Offers to Order Intakes", "orderIntakeToRetail": "Order Intakes to Retails"}, "filter": {"title": "Filter", "fields": {"monthOfImport": {"label": "Month of Import"}, "reportingView": {"label": "Reporting View"}, "salesConsultant": {"label": "Sales Consultant"}, "vehicleModel": {"label": "Vehicle Model"}, "reportingViewOptions": {"month": "Month", "ytd": "YTD"}}}, "messages": {"importing": "Importing data...", "importSuccess": "Data imported successfully."}}