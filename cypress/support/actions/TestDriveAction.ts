import dayjs from 'dayjs';
import { isNil } from 'lodash/fp';
import urljoin from 'url-join';
import dialCodeList from '../../../src/app/datasets/dialCode';
import BaseActions from './BaseActions';
import { DropdownValue } from './shared';

class TestDriveAction extends BaseActions {
    accessLeadPage(url: string, leadSuiteId: string) {
        cy.visit(urljoin(url, 'leads', leadSuiteId));
        cy.waitForAssets();
        cy.waitForGraphQL();
    }

    checkIfSelectExists(name: string) {
        cy.get(`select[name="${name}"]`).should('exist');
    }

    fillDatePicker(dateCy: string, value: string) {
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

        // Step 1: Parse the input date into a standardized format (YYYY-MM-DD)
        const parsedDate = dayjs(value);
        if (!parsedDate.isValid()) {
            throw new Error(`Invalid date format: ${value}`);
        }
        const targetYear = parsedDate.year();
        const targetMonth = parsedDate.month();
        const targetDate = parsedDate.format('YYYY-MM-DD');

        // Open the date picker
        cy.get(`[data-cy="${dateCy}"]`).click();

        // Step 2: Navigate to the correct year
        cy.get('.ant-picker-header-view .ant-picker-year-btn').then($yearBtn => {
            const currentYear = parseInt($yearBtn.text(), 10);

            if (currentYear > targetYear) {
                const steps = currentYear - targetYear;
                for (let i = 0; i < steps; i++) {
                    cy.get('.ant-picker-header-super-prev-btn').click(); // Navigate to previous year
                }
            } else if (currentYear < targetYear) {
                const steps = targetYear - currentYear;
                for (let i = 0; i < steps; i++) {
                    cy.get('.ant-picker-header-super-next-btn').click(); // Navigate to next year
                }
            }
        });

        // Step 3: Navigate to the correct month
        cy.get('.ant-picker-header-view .ant-picker-month-btn').then($monthBtn => {
            const currentMonth = $monthBtn.text();
            const currentMonthIndex = months.indexOf(currentMonth);

            if (currentMonthIndex > targetMonth) {
                const steps = currentMonthIndex - targetMonth;
                for (let i = 0; i < steps; i++) {
                    cy.get('.ant-picker-header-prev-btn').click(); // Navigate to previous month
                }
            } else if (currentMonthIndex < targetMonth) {
                const steps = targetMonth - currentMonthIndex;
                for (let i = 0; i < steps; i++) {
                    cy.get('.ant-picker-header-next-btn').click(); // Navigate to next month
                }
            }
        });

        // Step 4: Select the target date
        cy.get('.ant-picker-date-panel')
            .find(`td[title="${targetDate}"]`) // Find the specific date cell
            .click(); // Select the date
    }

    fillAndValidatePhoneField(type: string, value: string, countryCode: string, prefix?: string) {
        const dialCode = dialCodeList.find(code => code.code === countryCode);
        const inputName = `input[name='customer.fields.${type}.value`;

        if (isNil(dialCode)) {
            expect(dialCode).to.throw('dialCode not found');
            throw new Error('dialCode not found');
        }

        if (prefix) {
            // force type in new field
            cy.get(`${inputName}.prefix']`).clear().type(prefix).should('have.value', prefix).should('equal', prefix);
        } else {
            // default prefilled by the formik
            cy.get(`${inputName}.prefix']`).should('have.value', dialCode?.dial_code?.substring(1));
        }
        cy.get(`${inputName}.value']`).clear().should('be.visible').type(value);
    }

    testDriveModalShouldShow() {
        cy.get('[data-cy="create-test-drive-modal"]').shadow().find('dialog').and('have.attr', 'open');
    }

    testDriveModalShouldHide() {
        cy.get('[data-cy="create-test-drive-modal"]').shadow().find('dialog').and('have.attr', 'inert');
    }

    submitTestDriveModalForm(isWaitForGraphQL = true) {
        cy.get('[data-cy="create-test-drive-button"] button').click({ force: true });
        if (isWaitForGraphQL) {
            cy.readyForNetworkIdling();
            cy.waitForAssets();
        }
    }

    cancelTestDriveModalForm() {
        cy.get('[data-cy="cancel-test-drive-creation-button"]').shadow().find('button').click({ force: true });
    }

    isInTestDriveDetailPage() {
        cy.get('[data-cy="lead-detail-header"]').should('contain.text', 'Test Drive Details').and('be.visible');
    }

    verifyTestDriveFieldContainValue(dataCy: string, expectedValue: String) {
        cy.get(`[data-cy="${dataCy}"]`)
            .find('input[type="text"]')
            .invoke('val')
            .then(value => {
                expect(value).to.include(expectedValue);
            });
    }

    verifyTestDriveField(dataCy: string, expectedValue: String) {
        cy.get(`[data-cy="${dataCy}"]`)
            .find('input[type="text"]')
            .invoke('val')
            .then(value => {
                expect(value).to.equal(expectedValue);
            });
    }

    validateDateValue(dataCy: string, expectedValue: string) {
        cy.get(`input[data-cy="${dataCy}"]`)
            .invoke('val')
            .then(value => {
                expect(value).to.equal(expectedValue);
            });
    }

    validateDropdown(name: string, expectedOption: string) {
        cy.get(`select[name='${name}']`)
            .invoke('val')
            .then(value => {
                expect(value).to.equal(expectedOption);
            });
    }

    validateTabs() {
        const tabNames = ['Test Drive', 'Documents', 'Logs'];

        cy.get('[data-cy="appointment-details-tabs"]')
            .find('p-tabs')
            .shadow()
            .find('p-tabs-bar > button')
            .should('have.length', tabNames.length)
            .each((tab, index) => {
                cy.wrap(tab).should('contain.text', tabNames[index]);
            });
    }

    verifySelectedVehicleDetail(variantName: string) {
        cy.get('div[class*="ContentItem__Container-sc-"]  h3[class*="ContentItem__ContentItemTitle-sc-"]')
            .contains('Vehicle of Interest')
            .should('be.visible')
            .click();

        cy.get('div[data-cy="vehicle-name"]')
            .find('input[type="text"]')
            .invoke('val')
            .then(value => {
                expect(value).to.equal(variantName);
            });
    }

    clearDateFieldValue(name: string) {
        cy.get(`[data-cy="${name}"]`)
            .parents('div[class*="DatePickerField__StyledDatePickerFormItem-sc-"]')
            .next('p-button-pure')
            .click({ force: true });
    }

    checkDateFieldErrorMessage(name: string, errorMessage: string) {
        cy.get(`[data-cy="${name}"]`)
            .parents('div.ant-form-item-control')
            .should(error => {
                expect(error).contain(errorMessage);
            });
    }

    checkSelectFieldErrorMessage(name: string, errorMessage: string) {
        cy.get(`select[name='${name}']`)
            .parent()
            .should(error => {
                expect(error).contain(errorMessage);
            });
    }

    checkInputFieldErrorMessage(name: string, errorMessage: string) {
        cy.get(`input[name='${name}']`)
            .parent()
            .should(error => {
                expect(error).contain(errorMessage);
            });
    }
}

export default TestDriveAction;
