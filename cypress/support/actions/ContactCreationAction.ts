import dayjs from 'dayjs';
import { isNil } from 'lodash';
import dialCodeList from '../../../src/app/datasets/dialCode';
import BaseActions from './BaseActions';

class ContactCreationAction extends BaseActions {
    accessLaunchPadPage(url: string) {
        cy.visit(url);
        cy.waitForAssets();
    }

    shouldSeeCreateContactModal() {
        cy.get('[data-cy="create-contact-modal"]').shadow().find('dialog').should('have.attr', 'open');
    }

    modalShouldBeHidden() {
        cy.get('[data-cy="create-contact-modal"]').shadow().find('dialog').and('have.attr', 'inert');
    }

    checkModalHeader(headerText: string) {
        cy.get('div[slot="header"]').should('contain.text', headerText);
    }

    closeModal() {
        cy.get('.launchpad-modal').shadow().find('.modal > .dismiss').click({ force: true });
    }

    fillAndValidateInput(type: string, input: string) {
        this.fillAndValidateInputByName(`customer.fields.${type}.value`, input);
    }

    fillAndValidatePhoneField(type: string, value: string, countryCode: string, prefix?: string) {
        const dialCode = dialCodeList.find(code => code.code === countryCode);
        const inputName = `input[name='customer.fields.${type}.value`;

        if (isNil(dialCode)) {
            expect(dialCode).to.throw('dialCode not found');
            throw new Error('dialCode not found');
        }

        if (prefix) {
            // force type in new field
            cy.get(`${inputName}.prefix']`).clear().type(prefix).should('have.value', prefix).should('equal', prefix);
        } else {
            // default prefilled by the formik
            cy.get(`${inputName}.prefix']`).should('have.value', dialCode?.dial_code?.substring(1));
        }
        cy.get(`${inputName}.value']`).clear().should('be.visible').type(value);
    }

    fillBirthday(type: string, value: string, format = 'DD/MM/YYYY') {
        const parsedDate = dayjs(value);

        if (!parsedDate.isValid()) {
            throw new Error(`Invalid date format: ${value}`);
        }
        const date = parsedDate.format(format);

        cy.get(`[data-cy="form-birthday-customer.fields.${type}.value"]`)
            .clear()
            .type(date)
            .should('be.visible')
            .should('have.value', date);
    }

    checkFieldsErrorMessage(type: string, errorMessage: string) {
        cy.get(`[name='customer.fields.${type}.value']`)
            .parents('div.ant-form-item-control-input')
            .should(error => {
                expect(error).contain(errorMessage);
            });
    }

    checkPhoneFieldsErrorMessage(type: string, errorMessage: string) {
        cy.get(`[name='customer.fields.${type}.value.value']`)
            .parents('div.ant-form-item-control-input')
            .should(error => {
                expect(error).contain(errorMessage);
            });
    }

    submitCreateContact(isWaitForGraphQL = true) {
        cy.get('#applicantForm').submit();
        isWaitForGraphQL && cy.waitForGraphQL();
    }

    isInContactDetailPage() {
        cy.get('[data-cy="lead-detail-header"]').should('contain.text', 'Contact Details').and('be.visible');
    }

    findAndAccessLeadDetailByCustomerName(customerName: string) {
        cy.get('[role="article"]').contains(customerName).click();
    }

    verifyLeadDetail(status: string, userName: string) {
        cy.get('p[data-cy="customer-name"]').should('contain.text', userName);

        cy.get('[data-cy="lead-status"]')
            .find('input[type="text"]')
            .then($inputs => {
                const values = Array.from($inputs).map(input => (input as HTMLInputElement).value);
                expect(values).to.include(status);
            });
    }

    verifyAssignedUser(userId: string) {
        cy.get(`select[name='assignee.id']`)
            .invoke('val')
            .then(options => {
                expect(userId).equal(options);
            });
    }

    verifyNotificationMessage(message: string) {
        cy.get('#root')
            .find('p-toast')
            .shadow()
            .find('p-toast-item')
            .shadow()
            .find('#toast')
            .contains(message)
            .should('be.visible');
    }
}

export default ContactCreationAction;
