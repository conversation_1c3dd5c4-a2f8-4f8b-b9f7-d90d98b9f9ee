type FICommission {
    salesConsultantName: String!
    inHouseFinanceTarget: Float!
    inHouseFinanceMtd: Float!
    inHouseFinanceYtd: Float!
    inHouseFinance3MAvg: Float!
    inHouseInsuranceTarget: Float!
    inHouseInsuranceMtd: Float!
    inHouseInsuranceYtd: Float!
    inHouseInsurance3MAvg: Float!
}

type WeekFunnel {
    start: DateTime!
    end: DateTime!
    leadsCreated: Int!
    testDrives: Int!
    salesOffers: Int!
    orderIntakes: Int!
    retails: Int!
    leadToTestDriveRate: Float!
    testDriveToSalesOfferRate: Float!
    salesOfferToOrderIntakeRate: Float!
    orderIntakeToRetailRate: Float!
}

type ProgressGoal {
    retailTargetMonth: Int!
    retailActualMonth: Int!
    retailMonthRate: Float!
    retailMonthDev: Int!
    retailTargetYtd: Int!
    retailActualYtd: Int!
    retailYtdRate: Float!
    retailYtdDev: Int!
    financeTarget: Float!
    financeActualRate: Float!
    insuranceTarget: Float!
    insuranceActualRate: Float!
}

type SalesPerformanceOverview {
    leadsCreated: Int!
    testDrives: Int!
    salesOffers: Int!
    orderIntakes: Int!
    retails: Int!
}

type SalesControlBoard {
    fiCommissions: [FICommission!]!
    weekFunnels: [WeekFunnel!]!
    progressGoal: ProgressGoal
    performanceOverview: SalesPerformanceOverview!
}
