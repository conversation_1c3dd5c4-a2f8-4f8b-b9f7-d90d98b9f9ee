type FinderApplicationModuleSubmitOrderContent {
    subject: DealerTranslationText!
    introTitle: DealerTranslationText!
    introImage: UploadedFileWithPreview
    contentText: DealerTranslationText!
    isSummaryVehicleVisible: Boolean!
}

type FinderApplicationModuleReminderEmailContent {
    subject: DealerTranslationText!
    introTitle: DealerTranslationText!
    introImage: UploadedFileWithPreview
    contentText: DealerTranslationText!
    sendingTime: DateTime!
    isActive: Boolean!
}

type FinderApplicationModuleSubmitOrderScenarioOverrideContent {
    subject: DealerTranslationText!
    introTitle: DealerTranslationText!
    introImage: UploadedFileWithPreview
    contentText: DealerTranslationText!
    isSummaryVehicleVisible: Boolean!
    scenarios: [ApplicationScenario!]!
    isActive: Boolean!
}

type FinderApplicationModuleReminderEmailScenarioOverrideContent {
    subject: DealerTranslationText!
    introTitle: DealerTranslationText!
    introImage: UploadedFileWithPreview
    contentText: DealerTranslationText!
    sendingTime: DateTime!
    scenarios: [ApplicationScenario!]!
    isActive: Boolean!
}

type FinderApplicationEmailWithScenarioOverridesSubmitOrderContent {
    defaultValue: FinderApplicationModuleSubmitOrderContent!
    overrides: [FinderApplicationModuleSubmitOrderScenarioOverrideContent!]!
}

type FinderApplicationEmailWithScenarioOverridesReminderContent {
    defaultValue: FinderApplicationModuleReminderEmailContent!
    overrides: [FinderApplicationModuleReminderEmailScenarioOverrideContent!]!
}

type FinderApplicationModuleEmailContents {
    submitOrder: FinderApplicationEmailWithScenarioOverridesSubmitOrderContent!
    reminderEmail: FinderApplicationEmailWithScenarioOverridesReminderContent!
}
