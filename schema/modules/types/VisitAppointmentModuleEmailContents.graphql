type VisitAppointmentModuleEmailContent {
    subject: DealerTranslatedStringSetting!
    introTitle: DealerTranslatedStringSetting!
    introImage: UploadedFileWithPreview
    contentText: DealerTranslatedStringSetting!
    isSummaryVehicleVisible: DealerBooleanSetting!
}

type VisitAppointmentModuleEmailContentCustomer {
    submitConfirmation: VisitAppointmentModuleEmailContent!
    bookingConfirmation: VisitAppointmentModuleEmailContent!
    bookingCancellation: VisitAppointmentModuleEmailContent!
    bookingAmendment: VisitAppointmentModuleEmailContent!
    bookingComplete: VisitAppointmentModuleEmailContent!
}

type VisitAppointmentModuleEmailContentSalesPerson {
    submitConfirmation: VisitAppointmentModuleEmailContent!
    bookingCancellation: VisitAppointmentModuleEmailContent!
    bookingAmendment: VisitAppointmentModuleEmailContent!
}

type VisitAppointmentModuleEmailContents {
    customer: VisitAppointmentModuleEmailContentCustomer!
    salesPerson: VisitAppointmentModuleEmailContentSalesPerson!
}
