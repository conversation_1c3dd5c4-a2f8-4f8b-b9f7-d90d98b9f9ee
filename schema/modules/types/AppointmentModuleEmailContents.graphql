type AppointmentModuleEmailContent {
    subject: DealerTranslatedStringSetting!
    introTitle: DealerTranslatedStringSetting!
    introImage: UploadedFileWithPreview
    contentText: DealerTranslatedStringSetting!
    isSummaryVehicleVisible: DealerBooleanSetting!
}

type AppointmentModuleEmailContentFinderReservation {
    subject: DealerTranslatedStringSetting!
    introTitle: DealerTranslatedStringSetting!
    introImage: UploadedFileWithPreview
    contentText: DealerTranslatedStringSetting!
    listTestDriveTitle: DealerTranslatedStringSetting
    listTestDriveItem: DealerTranslatedStringSetting
    isSummaryVehicleVisible: DealerBooleanSetting!
}

type AppointmentModuleEmailContentCustomer {
    submitConfirmation: AppointmentModuleEmailContent!
    bookingConfirmation: AppointmentModuleEmailContent!
    bookingCancellation: AppointmentModuleEmailContent!
    bookingAmendment: AppointmentModuleEmailContent!
    endTestDriveWithProcess: AppointmentModuleEmailContent!
    completeTestDriveWithoutProcess: AppointmentModuleEmailContent!
}

type AppointmentModuleEmailContentSalesPerson {
    submitConfirmation: AppointmentModuleEmailContent!
    bookingCancellation: AppointmentModuleEmailContent!
    finderReservation: AppointmentModuleEmailContentFinderReservation!
    endTestDriveReminder: AppointmentModuleEmailContent!
    bookingAmendment: AppointmentModuleEmailContent!
}

type AppointmentModuleEmailContents {
    customer: AppointmentModuleEmailContentCustomer!
    salesPerson: AppointmentModuleEmailContentSalesPerson!
}
