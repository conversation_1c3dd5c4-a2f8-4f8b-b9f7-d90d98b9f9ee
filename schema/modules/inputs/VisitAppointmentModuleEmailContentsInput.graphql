input VisitAppointmentModuleEmailContentInput {
    subject: DealerTranslatedStringSettingInput!
    introTitle: DealerTranslatedStringSettingInput!
    contentText: DealerTranslatedStringSettingInput!
    isSummaryVehicleVisible: DealerBooleanSettingInput!
}

input VisitAppointmentModuleEmailContentCustomerInput {
    submitConfirmation: VisitAppointmentModuleEmailContentInput!
    bookingConfirmation: VisitAppointmentModuleEmailContentInput!
    bookingCancellation: VisitAppointmentModuleEmailContentInput!
    bookingAmendment: VisitAppointmentModuleEmailContentInput!
    bookingComplete: VisitAppointmentModuleEmailContentInput!
}

input VisitAppointmentModuleEmailContentSalesPersonInput {
    submitConfirmation: VisitAppointmentModuleEmailContentInput!
    bookingCancellation: VisitAppointmentModuleEmailContentInput!
}

input VisitAppointmentModuleEmailContentsInput {
    customer: VisitAppointmentModuleEmailContentCustomerInput!
    salesPerson: VisitAppointmentModuleEmailContentSalesPersonInput!
    emailContentUpdateType: EmailContentUpdateType!
    dealerId: ObjectID
}
