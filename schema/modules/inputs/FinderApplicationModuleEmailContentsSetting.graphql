input FinderApplicationModuleSubmitOrderInput {
    subject: DealerTranslationTextInput!
    introTitle: DealerTranslationTextInput!
    contentText: DealerTranslationTextInput!
    isSummaryVehicleVisible: Boolean!
}

input FinderApplicationModuleReminderEmailInput {
    subject: DealerTranslationTextInput!
    introTitle: DealerTranslationTextInput!
    contentText: DealerTranslationTextInput!
    sendingTime: DateTime!
    isActive: Boolean!
}

input FinderApplicationModuleSubmitOrderScenarioOverrideInput {
    subject: DealerTranslationTextInput!
    introTitle: DealerTranslationTextInput!
    contentText: DealerTranslationTextInput!
    isSummaryVehicleVisible: Boolean!
    scenarios: [ApplicationScenario!]!
    isActive: Boolean!
}

input FinderApplicationModuleReminderEmailScenarioOverrideInput {
    subject: DealerTranslationTextInput!
    introTitle: DealerTranslationTextInput!
    contentText: DealerTranslationTextInput!
    sendingTime: DateTime!
    scenarios: [ApplicationScenario!]!
    isActive: Boolean!
}

input FinderApplicationEmailWithScenarioOverridesSubmitOrderInput {
    defaultValue: FinderApplicationModuleSubmitOrderInput!
    overrides: [FinderApplicationModuleSubmitOrderScenarioOverrideInput!]!
}

input FinderApplicationEmailWithScenarioOverridesReminderInput {
    defaultValue: FinderApplicationModuleReminderEmailInput!
    overrides: [FinderApplicationModuleReminderEmailScenarioOverrideInput!]!
}

input FinderApplicationModuleEmailContentsSetting {
    submitOrder: FinderApplicationEmailWithScenarioOverridesSubmitOrderInput!
    reminderEmail: FinderApplicationEmailWithScenarioOverridesReminderInput!
    emailContentUpdateType: EmailContentUpdateType!
    dealerId: ObjectID
}
