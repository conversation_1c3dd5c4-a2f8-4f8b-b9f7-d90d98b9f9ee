input EventApplicationModuleEmailSubmitOrderInput {
    subject: DealerTranslationTextInput!
    introTitle: DealerTranslationTextInput!
    contentText: DealerTranslationTextInput!
}

input EventApplicationModuleSubmitOrderScenarioOverrideInput {
    subject: DealerTranslationTextInput!
    introTitle: DealerTranslationTextInput!
    contentText: DealerTranslationTextInput!
    scenarios: [ApplicationScenario!]!
    isActive: Boolean!
}

input EventApplicationModuleEmailWithScenarioOverridesSubmitOrderInput {
    defaultValue: EventApplicationModuleEmailSubmitOrderInput!
    overrides: [EventApplicationModuleSubmitOrderScenarioOverrideInput!]!
}

input EventApplicationModuleEmailContentsSetting {
    submitOrder: EventApplicationModuleEmailWithScenarioOverridesSubmitOrderInput!
    emailContentUpdateType: EmailContentUpdateType!
    dealerId: ObjectID
}
