input AppointmentModuleEmailContentInput {
    subject: DealerTranslatedStringSettingInput!
    introTitle: DealerTranslatedStringSettingInput!
    contentText: DealerTranslatedStringSettingInput!
    isSummaryVehicleVisible: DealerBooleanSettingInput!
}

input AppointmentModuleEmailContentFinderReservationInput {
    subject: DealerTranslatedStringSettingInput!
    introTitle: DealerTranslatedStringSettingInput!
    contentText: DealerTranslatedStringSettingInput!
    listTestDriveTitle: DealerTranslatedStringSettingInput
    listTestDriveItem: DealerTranslatedStringSettingInput
    isSummaryVehicleVisible: DealerBooleanSettingInput!
}

input AppointmentModuleEmailContentCustomerInput {
    submitConfirmation: AppointmentModuleEmailContentInput!
    bookingConfirmation: AppointmentModuleEmailContentInput!
    bookingCancellation: AppointmentModuleEmailContentInput!
    bookingAmendment: AppointmentModuleEmailContentInput!
    endTestDriveWithProcess: AppointmentModuleEmailContentInput!
    completeTestDriveWithoutProcess: AppointmentModuleEmailContentInput!
}

input AppointmentModuleEmailContentSalesPersonInput {
    submitConfirmation: AppointmentModuleEmailContentInput!
    bookingCancellation: AppointmentModuleEmailContentInput!
    finderReservation: AppointmentModuleEmailContentFinderReservationInput!
    endTestDriveReminder: AppointmentModuleEmailContentInput!
}

input AppointmentModuleEmailContentsInput {
    customer: AppointmentModuleEmailContentCustomerInput!
    salesPerson: AppointmentModuleEmailContentSalesPersonInput!
    emailContentUpdateType: EmailContentUpdateType!
    dealerId: ObjectID
}
