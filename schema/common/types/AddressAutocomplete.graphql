"""
Address autocomplete result
"""
type AddressAutocompleteResult {
    """
    Complete formatted address
    """
    address: String!

    """
    Array of address components
    """
    components: [AddressComponent!]!

    """
    Geographic coordinates
    """
    coordinates: AddressCoordinates

    """
    Unique identifier for the address
    """
    id: String!
}

"""
Individual address component
"""
type AddressComponent {
    """
    Type of the component (e.g., street, city, region, country)
    """
    type: AddressComponentType!

    """
    Short name of the component
    """
    shortName: String!

    """
    Long name of the component
    """
    longName: String!
}

"""
Geographic coordinates for an address
"""
type AddressCoordinates {
    """
    Latitude
    """
    latitude: Float!

    """
    Longitude
    """
    longitude: Float!

    """
    Accuracy of the coordinates
    """
    accuracy: String
}

"""
Types of address components
"""
enum AddressComponentType {
    ADDRESS
    SECONDARY_ADDRESS
    STREET
    NEIGHBORHOOD
    LOCALITY
    DISTRICT
    REGION
    POSTCODE
    COUNTRY
    PLACE
    BLOCK
}

enum AddressSearchType {
    """
    Search for address/postal code
    """
    PARTIAL

    """
    Search for postal codes
    """
    POSTALCODE
}

"""
Input for address autocomplete query
"""
input AddressAutocompleteInput {
    """
    Search query text
    """
    query: String!

    """
    Search type
    """
    searchType: AddressSearchType

    """
    Country code to limit results (ISO 3166-1 alpha-2)
    """
    countryCode: String

    """
    Language for results (IETF language tag)
    """
    language: String

    """
    Maximum number of results (default: 5, max: 10)
    """
    limit: Int

    """
    Proximity coordinates to bias results [longitude, latitude]
    """
    proximity: [Float!]

    """
    Types of features to include in results
    """
    types: [AddressComponentType!]
}
