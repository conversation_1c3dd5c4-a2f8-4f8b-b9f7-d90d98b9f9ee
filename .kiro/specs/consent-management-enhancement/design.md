# Design Document: Consent Management Enhancement

## Overview

The Consent Management Enhancement feature aims to improve the user experience when managing consents in the Porsche Digital Finance Solutions (PDFS) platform. This design document outlines the approach for implementing the "Select All" functionality and the parent-child checkbox relationship for consents. The enhancement will also include modal handling for consents that require user confirmation through a modal interface.

### Affected Pages and Flows

This enhancement will be implemented across all pages and flows that involve consent management outside of admin pages, including:

1. **Application Creation Flow**: When users create new applications and need to provide consents
2. **Lead Generation Forms**: When collecting consents during lead generation
3. **Customer Profile Management**: When updating consent preferences in customer profiles
4. **Test Drive Booking**: When consents are required for test drive bookings
5. **Showroom Visit Scheduling**: When consents are collected for showroom visits
6. **Gift Voucher Redemption**: When consents are required for gift voucher redemption
7. **Sales Offer Agreement**: When consents are collected during sales offer creation

The enhancement will NOT be applied to admin pages where consents are configured and managed by administrators.

## Architecture

The consent management system in PDFS is built on a GraphQL API with React frontend components. The system uses the following key components:

1. **Backend GraphQL Schema**: Defines the structure of consent data and operations
2. **Frontend Components**: Renders consent checkboxes and handles user interactions
3. **State Management**: Manages the state of consent selections

The enhancement will primarily focus on the frontend components and state management, with no changes required to the backend schema or data structure.

## Components and Interfaces

### 1. ConsentSelectionContext

We will create a new React context to manage the state of consent selections across components:

```typescript
type ConsentSelectionContextType {
  selectedConsents: Set<string>; // Set of consent IDs
  selectAll: () => void;
  deselectAll: () => void;
  isSelected: (id: string) => boolean;
  toggleConsent: (id: string, isSelected: boolean, childIds?: string[]) => void;
  areAllSelected: boolean;
  consentsWithModals: ConsentsAndDeclarations[]; // Consents that require modal confirmation
  currentModalIndex: number; // Current index in the modal sequence
  totalModals: number; // Total number of modals to show
  showNextModal: () => void; // Show the next modal in sequence
  closeModals: () => void; // Close all modals
}
```

### 2. SelectAllButton Component

A new reusable component that will provide the "Select All" functionality:

```typescript
type SelectAllButtonProps {
  consentIds: string[];
  consents: ConsentsAndDeclarations[];
  disabled?: boolean;
  groupDescription: string; // Text from the description field of consent data with group type
}
```

The `groupDescription` will be used as the button text instead of a generic "Select All" label. This text comes from the `description` field of the consent data with group type.

### 3. Enhanced ConsentCheckboxField Component

We will enhance the existing `ConsentCheckboxField` component to support parent-child relationships:

```typescript
type ConsentCheckboxFieldProps = Omit<CheckboxProps, 'onChange'> & {
  name: string;
  label?: string;
  itemProps?: Omit<FormItemProps, 'label' | 'meta' | 'required' | 'children'>;
  required?: boolean;
  customComponent?: React.ComponentType<CheckboxProps>;
  // New props for parent-child relationship
  isParent?: boolean;
  childIds?: string[];
  parentId?: string;
  // For modal handling
  hasLegalMarkup?: boolean;
  legalMarkup?: string;
  consent?: ConsentsAndDeclarations;
}
```

### 4. Enhanced ConsentModal Component

We will enhance the existing `ConsentModal` component to support sequential display of consent modals:

```typescript
type EnhancedConsentModalProps = ConsentModalProps & {
  consents: ConsentsAndDeclarations[];
  currentIndex: number;
  totalCount: number;
  showNextModal: () => void;
}
```

The modal will display a header with "Consent X of Y" format, where X is the current consent index (1-based) and Y is the total number of consents. When the user clicks the "Agree" button, the next consent in the sequence will be shown. When the user reaches the final consent and clicks "Agree", the modal will close.

## Data Models

The existing consent data models will be used without modification. The key models involved are:

1. **ConsentsAndDeclarations**: The base type for all consent types
2. **GroupConsentsAndDeclarations**: Represents a parent consent with children
3. **CheckboxConsentsAndDeclarations**: Represents a checkbox consent

The parent-child relationship is already established in the data model through the `parentId` field in the `ConsentsAndDeclarations` type and the `children` field in the `GroupConsentsAndDeclarations` type.

## Implementation Details

### 1. ConsentSelectionContext Implementation

```typescript
const ConsentSelectionContext = createContext<ConsentSelectionContextType | undefined>(undefined);

export const ConsentSelectionProvider: React.FC<{ children: React.ReactNode; consents: ConsentsAndDeclarations[] }> = ({ 
  children, 
  consents 
}) => {
  const [selectedConsents, setSelectedConsents] = useState<Set<string>>(new Set());
  const [consentsWithModals, setConsentsWithModals] = useState<ConsentsAndDeclarations[]>([]);
  const [currentModalIndex, setCurrentModalIndex] = useState<number>(0);
  const [showModals, setShowModals] = useState<boolean>(false);
  
  const allConsentIds = useMemo(() => consents.map(consent => consent.id), [consents]);
  
  const selectAll = useCallback(() => {
    // Filter consents that require modal confirmation
    const modalConsents = consents.filter(consent => 
      consent.__typename === 'CheckboxConsentsAndDeclarations' && 
      consent.hasLegalMarkup && 
      consent.legalTextPosition === LegalTextPosition.Modal
    );
    
    if (modalConsents.length > 0) {
      // When "Select All" is clicked, show modals sequentially
      setConsentsWithModals(modalConsents);
      setCurrentModalIndex(0);
      setShowModals(true);
    } else {
      // If no modals are required, select all consents immediately
      setSelectedConsents(new Set(allConsentIds));
    }
  }, [allConsentIds, consents]);
  
  const deselectAll = useCallback(() => {
    setSelectedConsents(new Set());
  }, []);
  
  const isSelected = useCallback((id: string) => {
    return selectedConsents.has(id);
  }, [selectedConsents]);
  
  const toggleConsent = useCallback((id: string, isSelected: boolean, childIds?: string[]) => {
    setSelectedConsents(prev => {
      const newSet = new Set(prev);
      
      if (isSelected) {
        newSet.add(id);
        // Add all child consents if this is a parent
        if (childIds) {
          childIds.forEach(childId => newSet.add(childId));
        }
      } else {
        newSet.delete(id);
        // Remove all child consents if this is a parent
        if (childIds) {
          childIds.forEach(childId => newSet.delete(childId));
        }
      }
      
      return newSet;
    });
  }, []);
  
  const showNextModal = useCallback(() => {
    if (currentModalIndex < consentsWithModals.length - 1) {
      // Move to the next modal in the sequence
      setCurrentModalIndex(prev => prev + 1);
    } else {
      // When all modals have been shown and agreed to, select all consents and close modals
      setSelectedConsents(new Set(allConsentIds));
      setShowModals(false);
      setCurrentModalIndex(0);
    }
  }, [currentModalIndex, consentsWithModals, allConsentIds]);
  
  const closeModals = useCallback(() => {
    // User canceled the modal sequence
    setShowModals(false);
    setCurrentModalIndex(0);
    setConsentsWithModals([]);
  }, []);
  
  const areAllSelected = useMemo(() => {
    return allConsentIds.length > 0 && allConsentIds.every(id => selectedConsents.has(id));
  }, [allConsentIds, selectedConsents]);
  
  return (
    <ConsentSelectionContext.Provider 
      value={{ 
        selectedConsents, 
        selectAll, 
        deselectAll, 
        isSelected, 
        toggleConsent, 
        areAllSelected,
        consentsWithModals,
        currentModalIndex,
        totalModals: consentsWithModals.length,
        showNextModal,
        closeModals
      }}
    >
      {children}
      {showModals && consentsWithModals.length > 0 && (
        <SequentialConsentModal 
          open={showModals}
          consents={consentsWithModals}
          currentIndex={currentModalIndex}
          totalCount={consentsWithModals.length}
          onAgree={showNextModal}
          onCancel={closeModals}
          onClose={closeModals}
        />
      )}
    </ConsentSelectionContext.Provider>
  );
};
```

### 2. SelectAllButton Implementation

```typescript
const SelectAllButton: React.FC<SelectAllButtonProps> = ({ consentIds, consents, disabled, groupDescription }) => {
  const { t } = useTranslation();
  const { areAllSelected, selectAll, deselectAll } = useContext(ConsentSelectionContext);
  const { Button } = useThemeComponents();
  
  const handleClick = () => {
    if (areAllSelected) {
      deselectAll();
    } else {
      selectAll();
    }
  };
  
  // Always use the groupDescription from the consent data with group type
  // This comes from the description field of the consent data
  const buttonText = groupDescription;
  
  return (
    <Button 
      onClick={handleClick} 
      disabled={disabled || consentIds.length === 0}
    >
      {buttonText}
    </Button>
  );
};
```

### 3. Enhanced ConsentCheckboxField Implementation

```typescript
const EnhancedConsentCheckboxField: React.FC<ConsentCheckboxFieldProps> = ({ 
  name,
  required,
  label,
  itemProps,
  customComponent,
  isParent,
  childIds,
  parentId,
  hasLegalMarkup,
  legalMarkup,
  consent,
  ...props
}) => {
  const [{ onChange, onBlur, ...field }, meta] = useField({ name });
  const { isSelected, toggleConsent } = useContext(ConsentSelectionContext);
  const [showModal, setShowModal] = useState<boolean>(false);
  
  const UsedCheckboxComponent = useMemo(() => customComponent ?? StyledConsentCheckBox, [customComponent]);
  
  // For parent checkboxes, check if all children are selected
  const allChildrenSelected = useMemo(() => {
    if (!isParent || !childIds || childIds.length === 0) return false;
    return childIds.every(childId => isSelected(childId));
  }, [isParent, childIds, isSelected]);
  
  // For child checkboxes, update parent when all siblings are selected
  useEffect(() => {
    if (parentId && childIds) {
      const allSiblingsSelected = childIds.every(childId => isSelected(childId));
      if (allSiblingsSelected && !isSelected(parentId)) {
        toggleConsent(parentId, true, []);
      }
    }
  }, [parentId, childIds, isSelected, toggleConsent]);
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const isChecked = e.target.checked;
    
    // Update Formik field value
    onChange(e);
    
    if (isChecked && hasLegalMarkup && legalMarkup) {
      // If this consent requires modal confirmation, show the modal
      setShowModal(true);
    } else {
      // Otherwise, toggle the consent directly in the context
      toggleConsent(name, isChecked, isParent ? childIds : undefined);
    }
  };
  
  const handleModalAgree = () => {
    toggleConsent(name, true, isParent ? childIds : undefined);
    setShowModal(false);
  };
  
  const handleModalCancel = () => {
    setShowModal(false);
  };
  
  return (
    <FormItem {...itemProps} label={label} meta={meta} required={required}>
      <UsedCheckboxComponent
        checked={field.value || (isParent && allChildrenSelected)}
        onChange={handleChange}
        {...props}
        {...field}
      />
      
      {hasLegalMarkup && legalMarkup && (
        <ConsentModal
          open={showModal}
          title={label}
          onOk={handleModalAgree}
          onCancel={handleModalCancel}
          disabled={false}
        >
          <div dangerouslySetInnerHTML={{ __html: legalMarkup }} />
        </ConsentModal>
      )}
    </FormItem>
  );
};
```

### 4. SequentialConsentModal Component Implementation

```typescript
const SequentialConsentModal: React.FC<{
  open: boolean;
  consents: ConsentsAndDeclarations[];
  currentIndex: number;
  totalCount: number;
  onAgree: () => void;
  onCancel: () => void;
  onClose: () => void;
}> = ({
  open,
  consents,
  currentIndex,
  totalCount,
  onAgree,
  onCancel,
  onClose
}) => {
  const { t } = useTranslation();
  const { ConsentModal } = useThemeComponents();
  const currentConsent = consents[currentIndex];
  
  if (!currentConsent) {
    return null;
  }
  
  const title = `${t('consentsAndDeclarations:consent')} ${currentIndex + 1} ${t('consentsAndDeclarations:of')} ${totalCount}`;
  
  return (
    <ConsentModal
      open={open}
      title={title}
      onOk={onAgree}
      onCancel={onCancel}
      disabled={false}
    >
      <div>
        <h3>{currentConsent.title || currentConsent.displayName}</h3>
        <div dangerouslySetInnerHTML={{ __html: currentConsent.legalMarkup || '' }} />
      </div>
    </ConsentModal>
  );
};
```

When the "Select All" button is clicked and there are consents that require modal confirmation, the system will show the first consent modal with a header "Consent 1 of X" (where X is the total number of consents requiring confirmation). When the user clicks "Agree", the next consent modal will be shown with "Consent 2 of X" header. This process continues until the user has reviewed all consents. When the user reaches the final consent and clicks "Agree", all consents will be selected and the modal will close.

### 5. Integration with Existing Components

The enhanced consent management components will be integrated into the existing consent management pages. The `ConsentSelectionProvider` will wrap the consent list components, and the `SelectAllButton` will be added to the top of the consent list.

## Error Handling

1. **Invalid Parent-Child Relationships**: The system will validate parent-child relationships to ensure that circular references are not created.
2. **Mandatory Consents**: Mandatory consents will be handled appropriately, ensuring they cannot be deselected.
3. **Backend Synchronization**: Error handling for backend synchronization will ensure that the UI state reflects the actual state of consents in the backend.
4. **Modal Sequence Errors**: The system will handle errors in the modal sequence, ensuring that users can cancel the process at any point.

## Performance Considerations

1. **Optimized State Updates**: The implementation uses React's `useMemo` and `useCallback` hooks to optimize rendering performance.
2. **Efficient Data Structures**: Using a `Set` for tracking selected consents provides O(1) lookup performance.
3. **Minimized Re-renders**: The context is designed to minimize unnecessary re-renders of components.
4. **Modal Loading**: Modals will be loaded only when needed to avoid unnecessary rendering.

## Accessibility

1. **Keyboard Navigation**: All consent checkboxes and the Select All button will be fully keyboard accessible.
2. **Screen Reader Support**: Appropriate ARIA attributes will be added to ensure screen reader compatibility.
3. **Focus Management**: Focus will be managed appropriately when selections change and when modals are opened/closed.
4. **Modal Accessibility**: Modals will be fully accessible, with proper focus trapping and keyboard navigation.

## Internationalization

All user-facing text will be added to the related translation files (public/locales/en/consentsAndDeclarations.json) to support multiple languages. The following new translation keys will be added:

```json
{
  "consent": "Consent",
  "of": "of",
  "agree": "Agree",
  "cancel": "Cancel"
}
```

Note that the "Select All" text will come directly from the `description` field of the consent data with group type, so it doesn't need to be added to the translation files.

The existing i18next setup will be used for this purpose.