import dayjs from 'dayjs';
import { ObjectId } from 'mongodb';
import {
    ApplicationScenario as GraphQlApplicationScenario,
    ConditionType as GraphQlConditionType,
    EventInput as GraphQlEventInput,
    EventKycPresetInput as GraphQlEventKycPresetInput,
    EventLeadOriginType as GraphQlEventLeadOriginType,
    EventMediumType as GraphQlEventMediumType,
    KycFieldPurpose as GraphQlKycFieldPurpose,
    LocalCustomerFieldKey as GraphQlLocalCustomerFieldKey,
    AgeCalculationMethod as GraphQlAgeCalculationMethod,
} from '../../app/api/types';
import {
    AgeCalculationMethod,
    ApplicationScenario,
    ConditionType,
    Event,
    EventLeadOriginType,
    EventMediumType,
    KycFieldPurpose,
    LocalCustomerFieldKey,
} from '../../server/database/documents';
import { eventModule } from './modules';
import { commonVersioning } from './shared';

const defaultEmailContent = {
    subject: { defaultValue: { defaultValue: null, overrides: [] }, overrides: [] },
    introTitle: { defaultValue: { defaultValue: null, overrides: [] }, overrides: [] },
    contentText: { defaultValue: { defaultValue: null, overrides: [] }, overrides: [] },
    introImage: null,
};

export const events: Event[] = [
    {
        _id: new ObjectId(),
        customizedFields: [],
        dealerIds: [],
        dealerVehicles: [],
        depositAmount: {
            defaultValue: 0,
            overrides: [],
        },
        displayAppointmentDatepicker: false,
        displayVisitAppointmentDatepicker: false,
        displayName: 'Test Form',
        enableDynamicUtmTracking: false,
        hasCustomiseEmail: false,
        isActive: true,
        isAllowTestDrive: false,
        isAllowTradeIn: false,
        isCustomerDataRetreivalByPorscheId: false,
        isPorscheIdLoginMandatory: null,
        isSearchCapCustomerOptional: true,
        name: {
            defaultValue: 'Test Form',
            overrides: [],
        },
        period: {
            start: dayjs().startOf('year').toDate(),
            end: dayjs().endOf('year').toDate(),
        },
        privateAccess: true,
        scenarios: [],
        showDealership: true,
        showLiveChat: false,
        skipForDeposit: false,
        urlSlug: 'test',
        utmParametersSettings: {
            defaultValue: {
                capLeadSource: EventLeadOriginType.Dealer,
                capLeadOrigin: EventMediumType.Internet,
                capCampaignId: 'LOREMIPSUM',
            },
            overrides: [],
        },
        moduleId: eventModule._id,
        identifier: 'EVENT-{YY}-129',
        isDeleted: false,
        _versioning: commonVersioning,
        kycPresets: [
            {
                _id: new ObjectId(),
                conditions: [{ type: ConditionType.IsApplicant }],
                displayName: 'KYC Fields',
                isActive: true,
                fields: [
                    {
                        isRequired: true,
                        key: LocalCustomerFieldKey.Title,
                        purpose: [KycFieldPurpose.KYC],
                    },
                    {
                        isRequired: true,
                        key: LocalCustomerFieldKey.Email,
                        purpose: [KycFieldPurpose.KYC],
                    },
                    {
                        isRequired: true,
                        key: LocalCustomerFieldKey.Phone,
                        purpose: [KycFieldPurpose.KYC],
                    },
                ],
            },
        ],
        kycExtraSettings: {
            minimumAge: 18,
            ageCalculationMethod: AgeCalculationMethod.BirthdayBased,
            mobileVerification: false,
        },
        hasCustomiseBanner: false,
        capPrequalification: false,
        porscheIdModuleId: null,
        emailContents: {
            submitOrder: defaultEmailContent,
            testDrive: {
                customer: {
                    submitConfirmation: {
                        ...defaultEmailContent,
                        isSummaryVehicleVisible: { defaultValue: false, overrides: [] },
                    },
                    bookingConfirmation: {
                        ...defaultEmailContent,
                        isSummaryVehicleVisible: { defaultValue: false, overrides: [] },
                    },
                    bookingAmendment: {
                        ...defaultEmailContent,
                        isSummaryVehicleVisible: { defaultValue: false, overrides: [] },
                    },
                    bookingCancellation: {
                        ...defaultEmailContent,
                        isSummaryVehicleVisible: { defaultValue: false, overrides: [] },
                    },
                    endTestDriveWithProcess: {
                        ...defaultEmailContent,
                        isSummaryVehicleVisible: { defaultValue: false, overrides: [] },
                    },
                    completeTestDriveWithoutProcess: {
                        ...defaultEmailContent,
                        isSummaryVehicleVisible: { defaultValue: false, overrides: [] },
                    },
                },
            },
        },
        userIds: [],
        hasVehicleIntegration: true,
        salesConsultantAutoAssignmentEnabled: false,
        hasCustomiseThankYouPage: false,
        thankYouPageContent: {
            introTitle: {
                defaultValue: { defaultValue: 'Your application has been submitted successfully.', overrides: [] },
                overrides: [],
            },
            contentText: {
                defaultValue: {
                    defaultValue: 'Our sales consultant will be in touch with you shortly.',
                    overrides: [],
                },
                overrides: [],
            },
            isCustomRedirectionButton: false,
            redirectButton: {
                title: {
                    defaultValue: { defaultValue: 'Continue Browsing', overrides: [] },
                    overrides: [],
                },
                url: 'https://www.porsche.com/singapore/en/models/911/',
            },
        },
    },
    {
        _id: new ObjectId(),
        customizedFields: [],
        dealerIds: [],
        dealerVehicles: [],
        depositAmount: {
            defaultValue: 0,
            overrides: [],
        },
        displayAppointmentDatepicker: false,
        displayVisitAppointmentDatepicker: false,
        displayName: 'Test Form 2',
        enableDynamicUtmTracking: false,
        hasCustomiseEmail: false,
        isActive: true,
        isAllowTestDrive: false,
        isAllowTradeIn: false,
        isCustomerDataRetreivalByPorscheId: false,
        isPorscheIdLoginMandatory: null,
        isSearchCapCustomerOptional: true,
        name: {
            defaultValue: 'Test Form 2',
            overrides: [],
        },
        period: {
            start: dayjs().startOf('year').toDate(),
            end: dayjs().endOf('year').toDate(),
        },
        privateAccess: true,
        scenarios: [],
        showDealership: true,
        showLiveChat: false,
        skipForDeposit: false,
        urlSlug: 'test2',
        utmParametersSettings: {
            defaultValue: {
                capLeadSource: EventLeadOriginType.Dealer,
                capLeadOrigin: EventMediumType.Internet,
                capCampaignId: 'LOREMIPSUM',
            },
            overrides: [],
        },
        moduleId: eventModule._id,
        identifier: 'EVENT-{YY}-234',
        isDeleted: false,
        _versioning: commonVersioning,
        kycPresets: [
            {
                _id: new ObjectId(),
                conditions: [{ type: ConditionType.IsApplicant }],
                displayName: 'KYC Fields',
                isActive: true,
                fields: [
                    {
                        isRequired: true,
                        key: LocalCustomerFieldKey.Title,
                        purpose: [KycFieldPurpose.KYC],
                    },
                    {
                        isRequired: true,
                        key: LocalCustomerFieldKey.Email,
                        purpose: [KycFieldPurpose.KYC],
                    },
                    {
                        isRequired: true,
                        key: LocalCustomerFieldKey.Phone,
                        purpose: [KycFieldPurpose.KYC],
                    },
                ],
            },
        ],
        kycExtraSettings: {
            minimumAge: 20,
            ageCalculationMethod: AgeCalculationMethod.CalendarYearBased,
            mobileVerification: false,
        },
        hasCustomiseBanner: false,
        capPrequalification: false,
        porscheIdModuleId: null,
        emailContents: {
            submitOrder: defaultEmailContent,
            testDrive: {
                customer: {
                    submitConfirmation: {
                        ...defaultEmailContent,
                        isSummaryVehicleVisible: { defaultValue: false, overrides: [] },
                    },
                    bookingConfirmation: {
                        ...defaultEmailContent,
                        isSummaryVehicleVisible: { defaultValue: false, overrides: [] },
                    },
                    bookingAmendment: {
                        ...defaultEmailContent,
                        isSummaryVehicleVisible: { defaultValue: false, overrides: [] },
                    },
                    bookingCancellation: {
                        ...defaultEmailContent,
                        isSummaryVehicleVisible: { defaultValue: false, overrides: [] },
                    },
                    endTestDriveWithProcess: {
                        ...defaultEmailContent,
                        isSummaryVehicleVisible: { defaultValue: false, overrides: [] },
                    },
                    completeTestDriveWithoutProcess: {
                        ...defaultEmailContent,
                        isSummaryVehicleVisible: { defaultValue: false, overrides: [] },
                    },
                },
            },
        },
        userIds: [],
        hasVehicleIntegration: true,
        salesConsultantAutoAssignmentEnabled: false,
        hasCustomiseThankYouPage: false,
        thankYouPageContent: {
            introTitle: {
                defaultValue: { defaultValue: 'Your application has been submitted successfully.', overrides: [] },
                overrides: [],
            },
            contentText: {
                defaultValue: {
                    defaultValue: 'Our sales consultant will be in touch with you shortly.',
                    overrides: [],
                },
                overrides: [],
            },
            isCustomRedirectionButton: false,
            redirectButton: {
                title: {
                    defaultValue: { defaultValue: 'Continue Browsing', overrides: [] },
                    overrides: [],
                },
                url: 'https://www.porsche.com/singapore/en/models/911/',
            },
        },
    },
];

export const eventInput: GraphQlEventInput = {
    displayName: 'Test Form',
    name: {
        defaultValue: 'Test Form',
        overrides: [],
    },
    isActive: true,
    period: {
        start: dayjs().startOf('year').toDate(),
        end: dayjs().endOf('year').toDate(),
    },
    privateAccess: true,
    isAllowTestDrive: false,
    isAllowTradeIn: false,
    scenarios: [],
    skipForDeposit: false,
    urlSlug: 'test',
    dealerIds: [],
    dealerVehicles: [],
    showLiveChat: false,
    showDealership: true,
    displayAppointmentDatepicker: false,
    displayVisitAppointmentDatepicker: false,
    enableDynamicUtmTracking: false,
    utmParametersSettings: {
        defaultValue: {
            capLeadSource: GraphQlEventLeadOriginType.Dealer,
            capLeadOrigin: GraphQlEventMediumType.Internet,
            capCampaignId: 'LOREMIPSUM',
        },
        overrides: [],
    },
    depositAmount: {
        defaultValue: 0,
        overrides: [],
    },
    kycExtraSettings: {
        minimumAge: 18,
        ageCalculationMethod: GraphQlAgeCalculationMethod.BirthdayBased,
        mobileVerification: false,
    },
    isCustomerDataRetreivalByPorscheId: false,
    isPorscheIdLoginMandatory: null,
    isSearchCapCustomerOptional: true,
    customizedFields: [],
    hasCustomiseEmail: false,
    hasVehicleIntegration: true,
    salesConsultantAutoAssignmentEnabled: false,
    hasCustomiseThankYouPage: false,
    thankYouPageContent: {
        introTitle: {
            defaultValue: { defaultValue: 'Your application has been submitted successfully.', overrides: [] },
            overrides: [],
        },
        contentText: {
            defaultValue: {
                defaultValue: 'Our sales consultant will be in touch with you shortly.',
                overrides: [],
            },
            overrides: [],
        },
        isCustomRedirectionButton: false,
        redirectButton: {
            title: {
                defaultValue: { defaultValue: 'Continue Browsing', overrides: [] },
                overrides: [],
            },
            url: 'https://www.porsche.com/singapore/en/models/911/',
        },
    },
};

export const kycPresets: GraphQlEventKycPresetInput[] = [
    {
        settings: {
            displayName: 'KYC Fields',
            isActive: true,
        },
        fields: [
            {
                key: GraphQlLocalCustomerFieldKey.Title,
                isRequired: true,
                purpose: [GraphQlKycFieldPurpose.Kyc],
            },
            {
                key: GraphQlLocalCustomerFieldKey.FullName,
                isRequired: true,
                purpose: [GraphQlKycFieldPurpose.Kyc],
            },
            {
                key: GraphQlLocalCustomerFieldKey.Phone,
                isRequired: true,
                purpose: [GraphQlKycFieldPurpose.Kyc],
            },
        ],
        conditions: [
            {
                type: GraphQlConditionType.IsApplicant,
                contextualConditionSettings: { context: GraphQlConditionType.IsApplicant },
            },
        ],
    },
];
