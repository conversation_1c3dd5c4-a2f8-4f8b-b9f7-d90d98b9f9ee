import { ObjectId } from 'mongodb';
import { CounterMethod, LaunchPadModule, ModuleType } from '../../../server/database/documents';
import companies from '../companies';
import { commonVersioning } from '../shared';
import appointmentModule from './appointmentModule';
import { consentModule, localCustomerManagementModule, simpleVehicleManagementModule } from './shared';
import visitAppointmentModule from './visitAppointmentModule';

const launchPadModule: LaunchPadModule = {
    _id: new ObjectId(),
    _type: ModuleType.LaunchPadModule,
    companyId: companies[0]._id,
    agreementsModuleId: consentModule._id,
    appointmentModuleId: appointmentModule._id,
    visitAppointmentModuleId: visitAppointmentModule._id,
    customerModuleId: localCustomerManagementModule._id,
    displayName: 'Test LauchPad Module',
    capCampaignIds: [],
    leadCounter: {
        method: CounterMethod.Global,
        padding: 5,
        prefix: 'LEAD-{YY}-{MM}',
    },
    appointmentCounter: {
        method: CounterMethod.Global,
        padding: 5,
        prefix: 'APPO-{YY}-{MM}',
    },
    vehicleModuleId: simpleVehicleManagementModule._id,
    dealerVehicles: [],
    hasTradeInRequest: false,
    _versioning: commonVersioning,
};

export default launchPadModule;
