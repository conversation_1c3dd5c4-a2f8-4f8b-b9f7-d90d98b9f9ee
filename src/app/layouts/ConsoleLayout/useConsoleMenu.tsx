import {
    ApiOutlined,
    A<PERSON>toreAddOutlined,
    <PERSON>Outlined,
    <PERSON><PERSON><PERSON>Outlined,
    <PERSON>codeOutlined,
    <PERSON>Outlined,
    CalendarOutlined,
    CarFilled,
    CarOutlined,
    CheckSquareOutlined,
    ControlOutlined,
    DashboardOutlined,
    DollarCircleOutlined,
    FileProtectOutlined,
    FileSyncOutlined,
    FileTextOutlined,
    GlobalOutlined,
    LayoutOutlined,
    PropertySafetyOutlined,
    SafetyCertificateOutlined,
    SettingOutlined,
    ShopOutlined,
    ShoppingCartOutlined,
    SmileOutlined,
    SoundOutlined,
    TagOutlined,
    TeamOutlined,
    ToolOutlined,
    TruckOutlined,
    LineChartOutlined,
} from '@ant-design/icons';
import { Route } from '@ant-design/pro-layout/lib/typings';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import * as permissionKind from '../../../shared/permissions';
import { useAccount } from '../../components/contexts/AccountContextManager';
import hasPermissions from '../../utilities/hasPermissions';
import useConsolePermissions from './useConsolePermissions';

const useConsoleMenu = (isCollapsed?: boolean) => {
    const { t } = useTranslation('consoleLayout');
    const { permissions: accountPermissions } = useAccount();

    const consolePermissions = useConsolePermissions();

    return useMemo((): Route => {
        // spread the permissions object
        const {
            accessFinanceProducts,
            hasModuleManagement,
            hasRouterManagement,
            accessEvents,
            accessConfigurators,
            accessCts,
            accessInventories,
            accessGiftVoucher,
            accessInsuranceProduct,
            accessInsurers,
            accessLabels,
            accessMaintenances,
            accessMobility,
            accessPorscheRetain,
            porscheRetainLink,
            accessPromotions,
            accessWebpages,
            accessAgreements,
            accessBanks,
            hasBookings,
            hasContacts,
            hasAppointments,
            hasVisitAppointments,
            hasDealerManagement,
            hasRolesManagement,
            hasFinderVehicleManagement,
            hasApplicationTradeIn,
            hasUserGroupManagement,
            hasCustomerModules,
            hasLeads,
            hasLeadsAndContacts,
            hasVehicleManagement,
            hasReservation,
            hasUserManagement,
            hasTradeIn,
            hasInsurance,
            hasFinancing,
            viewMarketingDashboard,
            viewSalesControlBoard,
            viewDashboard,
            viewCampaigns,
        } = consolePermissions;

        const systemRoutes: Route[] = [
            hasModuleManagement && {
                name: t('consoleLayout:menuItems.modules'),
                path: '/admin/system/modules',
                key: 'modules',
            },
            hasRouterManagement && {
                name: t('consoleLayout:menuItems.routers'),
                path: '/admin/system/routers',
                key: 'routers',
            },
            hasPermissions(accountPermissions, [permissionKind.viewLanguagePacks]) && {
                name: t('consoleLayout:menuItems.languages'),
                path: '/admin/system/languages',
                key: 'languages',
            },
        ].filter(Boolean);

        const accessRoutes: Route[] = [
            hasUserManagement && {
                name: t('consoleLayout:menuItems.users'),
                path: '/admin/accesses/users',
                key: 'users',
            },
            hasRolesManagement && {
                name: t('consoleLayout:menuItems.roles'),
                path: '/admin/accesses/roles',
                key: 'roles',
            },
            hasUserGroupManagement && {
                name: t('consoleLayout:menuItems.userGroups'),
                path: '/admin/accesses/userGroups',
                key: 'userGroups',
            },
        ].filter(Boolean);

        const miscMenus = [
            viewDashboard && {
                name: t('consoleLayout:menuItems.dashboard'),
                path: '/admin/dashboard',
                key: 'overview',
                icon: <DashboardOutlined />,
            },
            viewMarketingDashboard && {
                name: t('consoleLayout:menuItems.marketingDashboard'),
                path: '/admin/marketingdashboard',
                key: 'marketingDashboard',
                icon: <BarChartOutlined />,
            },
            viewSalesControlBoard && {
                name: t('consoleLayout:menuItems.salesControlBoard'),
                path: '/admin/salesControlBoard',
                key: 'salesControlBoard',
                icon: <LineChartOutlined />,
            },
        ].filter(Boolean);

        const applicationMenus = [
            {
                name: t('consoleLayout:console.applications'),
                path: '/',
                key: 'console-applications',
                isSubMenu: true,
                // replicate icon style
                ...(isCollapsed && {
                    icon: (
                        <span className="anticon" style={{ transition: 'none' }}>
                            {t('consoleLayout:console.applications')?.[0]}
                        </span>
                    ),
                }),
            },
            hasFinancing && {
                name: t('consoleLayout:menuItems.applications'),
                path: '/admin/applications',
                key: 'applications',
                icon: <FileTextOutlined />,
            },
            hasReservation && {
                name: t('consoleLayout:menuItems.reservations'),
                path: '/admin/reservations',
                key: 'reservations',
                icon: <ShoppingCartOutlined />,
            },
            hasInsurance && {
                name: t('consoleLayout:menuItems.insurances'),
                path: '/admin/insurances',
                key: 'insurances',
                icon: <FileProtectOutlined />,
            },
            hasLeads && {
                name: t('consoleLayout:menuItems.leads'),
                path: '/admin/leads',
                key: 'leads',
                icon: <SoundOutlined />,
            },
            hasContacts && {
                name: t('consoleLayout:menuItems.contacts'),
                path: '/admin/contacts',
                key: 'contacts',
                icon: <SoundOutlined />,
            },
            hasLeadsAndContacts && {
                name: t('consoleLayout:menuItems.leadsAndContacts'),
                path: '/admin/leadsAndContacts',
                key: 'leadsAndContacts',
                icon: <SoundOutlined />,
            },
            hasAppointments && {
                name: t('consoleLayout:menuItems.appointments'),
                path: '/admin/appointments',
                key: 'appointments',
                icon: <BellOutlined />,
            },
            hasVisitAppointments && {
                name: t('consoleLayout:menuItems.visitAppointments'),
                path: '/admin/showroomvisits',
                key: 'visitAppointments',
                icon: <BellOutlined />,
            },
            hasBookings && {
                name: t('consoleLayout:menuItems.mobilitybookings'),
                path: '/admin/mobilitybookings',
                key: 'mobilitybookings',
                icon: <CalendarOutlined />,
            },
            hasTradeIn && {
                name: t('consoleLayout:menuItems.tradeIns'),
                path: '/admin/tradeIns',
                key: 'tradeIns',
                icon: <TruckOutlined />,
            },
            hasApplicationTradeIn && {
                name: t('consoleLayout:menuItems.applicationTradeIns'),
                path: '/admin/tradeInRequests',
                key: 'applicationTradeIns',
                icon: <TruckOutlined />,
            },
            hasCustomerModules && {
                name: t('consoleLayout:menuItems.customers'),
                path: '/admin/customers',
                key: 'customers',
                icon: <SmileOutlined />,
            },
            accessGiftVoucher && {
                name: t('consoleLayout:menuItems.giftVouchers'),
                path: '/admin/giftCodes',
                key: 'giftVouchers',
                icon: <TagOutlined />,
            },
        ].filter(Boolean);

        const contentMenus = [
            {
                name: t('consoleLayout:console.contents'),
                path: '/',
                key: 'contents',
                isSubMenu: true, // replicate icon style
                ...(isCollapsed && {
                    icon: (
                        <span className="anticon" style={{ transition: 'none' }}>
                            {t('consoleLayout:console.contents')?.[0]}
                        </span>
                    ),
                }),
            },
            accessPromotions && {
                name: t('consoleLayout:menuItems.promoCodes'),
                path: '/admin/promoCodes',
                key: 'promoCodes',
                icon: <DollarCircleOutlined />,
            },
            accessLabels && {
                name: t('consoleLayout:menuItems.labels'),
                path: '/admin/labels',
                key: 'labels',
                icon: <TagOutlined />,
            },
            accessEvents && {
                name: t('consoleLayout:menuItems.events'),
                path: '/admin/events',
                key: 'events',
                icon: <CalendarOutlined />,
            },
            accessConfigurators && {
                name: t('consoleLayout:menuItems.configurators'),
                path: '/admin/configurators',
                key: 'configuratorsMenu',
                icon: <ControlOutlined />,
                routes: [
                    {
                        name: t('consoleLayout:menuItems.configurators'),
                        path: '/admin/configurators',
                        key: 'configurators',
                    },
                    {
                        name: t('consoleLayout:menuItems.banners'),
                        path: '/admin/banners',
                        key: 'banners',
                    },
                ],
            },
            accessWebpages && {
                name: t('consoleLayout:menuItems.webpages'),
                path: '/admin/webpages',
                key: 'webpages',
                icon: <LayoutOutlined />,
            },
            accessMobility && {
                name: t('consoleLayout:menuItems.mobilities'),
                path: '/admin/mobilityoptions',
                key: 'mobilities',
                icon: <AppstoreAddOutlined />,
            },
            hasVehicleManagement && {
                name: t('consoleLayout:menuItems.vehicles'),
                key: 'vehicles',
                path: '/admin/vehicles',
                icon: <CarOutlined />,
                routes: [
                    {
                        name: t('consoleLayout:menuItems.makes'),
                        path: '/admin/vehicles/makes',
                        key: 'makes',
                    },
                    {
                        name: t('consoleLayout:menuItems.models'),
                        path: '/admin/vehicles/models',
                        key: 'models',
                    },
                    {
                        name: t('consoleLayout:menuItems.subModels'),
                        path: '/admin/vehicles/subModels',
                        key: 'subModels',
                    },
                    {
                        name: t('consoleLayout:menuItems.variants'),
                        path: '/admin/vehicles/variants',
                        key: 'variants',
                    },
                ],
            },
            accessInventories && {
                name: t('consoleLayout:menuItems.inventory'),
                path: '/admin/inventories',
                key: 'inventories',
                icon: <BarcodeOutlined />,
            },
            hasFinderVehicleManagement && {
                name: t('consoleLayout:menuItems.finderVehicles'),
                key: 'finderVehicles',
                path: '/admin/finderVehicles',
                icon: <CarFilled />,
            },
            accessFinanceProducts && {
                name: t('consoleLayout:menuItems.financeProducts'),
                path: '/admin/financeProducts',
                key: 'financeProducts',
                icon: <DollarCircleOutlined />,
            },
            accessInsuranceProduct && {
                name: t('consoleLayout:menuItems.insuranceProducts'),
                path: '/admin/insuranceProducts',
                key: 'insuranceProducts',
                icon: <PropertySafetyOutlined />,
            },
            accessCts && {
                name: t('consoleLayout:menuItems.cts'),
                path: '/admin/cts',
                key: 'cts',
                icon: <ApiOutlined />,
            },
            accessPorscheRetain &&
                porscheRetainLink && {
                    name: t('consoleLayout:menuItems.porscheRetain'),
                    path: porscheRetainLink,
                    key: 'porscheRetain',
                    icon: <FileSyncOutlined />,
                },
            accessPorscheRetain &&
                !porscheRetainLink && {
                    name: t('consoleLayout:menuItems.porscheRetain'),
                    path: '/admin/porscheRetain',
                    key: 'porscheRetain',
                    icon: <FileSyncOutlined />,
                },
            accessAgreements && {
                name: t('consoleLayout:menuItems.consents'),
                path: '/admin/consents',
                key: 'consents',
                icon: <CheckSquareOutlined />,
            },
            accessMaintenances && {
                name: t('consoleLayout:menuItems.maintenance'),
                path: '/admin/maintenance',
                key: 'maintenance',
                icon: <ToolOutlined />,
            },
            viewCampaigns && {
                name: t('consoleLayout:menuItems.capCampaignManager'),
                path: '/admin/campaigns',
                key: 'campaigns',
                icon: <ApiOutlined />,
            },
        ].filter(Boolean);

        const settingMenu = [
            {
                name: t('consoleLayout:console.settings'),
                path: '/',
                key: 'settings',
                isSubMenu: true,
                ...(isCollapsed && {
                    icon: (
                        <span className="anticon" style={{ transition: 'none' }}>
                            {t('consoleLayout:console.settings')?.[0]}
                        </span>
                    ),
                }),
            },
            accessRoutes.length > 0 && {
                name: t('consoleLayout:menuItems.usersandroles'),
                key: 'userandrole',
                path: '/admin/accesses',
                icon: <TeamOutlined />,
                routes: accessRoutes,
            },
            hasDealerManagement && {
                name: t('consoleLayout:menuItems.dealers'),
                path: '/admin/dealers',
                key: 'dealers',
                icon: <ShopOutlined />,
            },
            hasPermissions(accountPermissions, [permissionKind.viewCompanies]) && {
                name: t('consoleLayout:menuItems.companyandcountry'),
                path: '/admin/companies',
                key: 'companies',
                icon: <GlobalOutlined />,
            },
            accessBanks && {
                name: t('consoleLayout:menuItems.banks'),
                path: '/admin/banks',
                key: 'banks',
                icon: <BankOutlined />,
            },
            accessInsurers && {
                name: t('consoleLayout:menuItems.insurers'),
                path: '/admin/insurers',
                key: 'insurers',
                icon: <SafetyCertificateOutlined />,
            },
            systemRoutes.length > 0 && {
                name: t('consoleLayout:menuItems.system'),
                key: 'system',
                path: '/admin/system',
                icon: <SettingOutlined />,
                routes: systemRoutes,
            },
        ].filter(Boolean);

        const mapIsLast = arrayLength => (menu, index) => {
            if (index !== arrayLength - 1) {
                return menu;
            }

            return { ...menu, isLast: true };
        };

        // if do not have sub menu under section, will hidden whole section
        const mapMenu = array => (array.length <= 1 ? [] : array);

        return {
            routes: [
                ...miscMenus.map(mapIsLast(miscMenus.length)),
                ...mapMenu(applicationMenus).map(mapIsLast(applicationMenus.length)),
                ...mapMenu(contentMenus).map(mapIsLast(contentMenus.length)),
                ...mapMenu(settingMenu),
            ].filter(Boolean),
        };
    }, [consolePermissions, t, accountPermissions, isCollapsed]);
};

export default useConsoleMenu;
