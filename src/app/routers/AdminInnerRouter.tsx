/* eslint-disable max-len */
import { Route, Routes } from 'react-router-dom';
import { CompanyTheme, LeadStageOption } from '../api/types';
import NotFoundResult from '../components/results/NotFoundResult';
import ConsoleLayout from '../layouts/ConsoleLayout';
import useConsolePermissions from '../layouts/ConsoleLayout/useConsolePermissions';
import AddBankPage from '../pages/admin/AddBankPage';
import AddBannerPage from '../pages/admin/AddBannerPage';
import AddCampaignPage from '../pages/admin/AddCampaignPage';
import AddCompanyPage from '../pages/admin/AddCompanyPage';
import AddConsentsAndDeclarations from '../pages/admin/AddConsentsAndDeclarations';
import AddDealerPage from '../pages/admin/AddDealerPage';
import AddEndpointPage from '../pages/admin/AddEndpointPage';
import AddEventPage from '../pages/admin/AddEventPage';
import AddFinanceProductPage from '../pages/admin/AddFinanceProductPage';
import AddInsuranceProductPage from '../pages/admin/AddInsuranceProductPage';
import AddInsurerPage from '../pages/admin/AddInsurerPage';
import AddInventoryPage from '../pages/admin/AddInventoryPage';
import AddLabelsPage from '../pages/admin/AddLabelsPage';
import AddMobilityPage from '../pages/admin/AddMobilityPage';
import AddModelConfiguratorPage from '../pages/admin/AddModelConfiguratorPage';
import AddModulePage from '../pages/admin/AddModulePage';
import AddPromoCodePage from '../pages/admin/AddPromoCodePage';
import AddUserPage from '../pages/admin/AddUserPage';
import AddMakePage from '../pages/admin/AddVehiclePage/AddMakePage';
import AddModel from '../pages/admin/AddVehiclePage/AddModelPage';
import AddSubmodel from '../pages/admin/AddVehiclePage/AddSubmodelPage';
import AddVariant from '../pages/admin/AddVehiclePage/AddVariantPage';
import AddWebpagePage from '../pages/admin/AddWebpagePage';
import ApplicationDetailsPage from '../pages/admin/ApplicationDetailsPage';
import ApplicationListPage from '../pages/admin/ApplicationListPage';
import ApplicationTradeInListPage from '../pages/admin/ApplicationTradeInListPage';
import AppointmentDetailsPage from '../pages/admin/AppointmentDetailsPage';
import AppointmentListPage from '../pages/admin/AppointmentListPage';
import BankDetailsPage from '../pages/admin/BankDetailsPage';
import BankListPage from '../pages/admin/BankListPage';
import BannerDetailsPage from '../pages/admin/BannerDetailsPage';
import BannerListPage from '../pages/admin/BannerListPage';
import CampaignDetailsPage from '../pages/admin/CampaignDetailsPage';
import CampaignListPage from '../pages/admin/CampaignListPage';
import CompanyDetailsPage from '../pages/admin/CompanyDetailsPage';
import CompanyListPage from '../pages/admin/CompanyListPage';
import ConfiguratorListPage from '../pages/admin/ConfiguratorListPage';
import ConsentsAndDeclarationsDetailsPage from '../pages/admin/ConsentsAndDeclarationsDetailsPage';
import ConsentsAndDeclarationsPage from '../pages/admin/ConsentsAndDeclarationsListPage';
import CtsDetailsPage from '../pages/admin/CtsDetailsPage';
import CustomerDetailsPage from '../pages/admin/CustomerDetailsPage';
import DashboardPage from '../pages/admin/DashboardPage';
import DealerDetailsPage from '../pages/admin/DealerDetailsPage';
import DealerListPage from '../pages/admin/DealerListPage';
import EventDetailsPage from '../pages/admin/EventDetailsPage';
import EventList from '../pages/admin/EventListPage';
import FinanceProductDetailsPage from '../pages/admin/FinanceProductDetailsPage';
import FinanceProductListPage from '../pages/admin/FinanceProductListPage';
import FinderVehicleDetailsPage from '../pages/admin/FinderVehicleDetailsPage';
import FinderVehicleListPage from '../pages/admin/FinderVehicleListPage';
import GiftVoucherDetails from '../pages/admin/GiftVoucherDetails';
import GiftVoucherListPage from '../pages/admin/GiftVoucherListPage';
import InsuranceDetailsPage from '../pages/admin/InsuranceDetailsPage';
import InsuranceListPage from '../pages/admin/InsuranceListPage';
import InsuranceProductDetails from '../pages/admin/InsuranceProductDetails';
import InsuranceProductListPage from '../pages/admin/InsuranceProductListPage';
import InsurerDetailsPage from '../pages/admin/InsurerDetailsPage';
import InsurerListPage from '../pages/admin/InsurerListPage';
import InventoryDetailsPage from '../pages/admin/InventoryDetailsPage';
import InventoryListPage from '../pages/admin/InventoryListPage';
import InventoryStockDetailsPage from '../pages/admin/InventoryStockDetailsPage';
import LabelsDetailsPage from '../pages/admin/LabelsDetailsPage';
import LabelsListPage from '../pages/admin/LabelsListPage';
import LanguagePackDetailsPage from '../pages/admin/LanguagePackDetailsPage';
import LanguagePackListPage from '../pages/admin/LanguagePackListPage';
import LeadDetailsPage from '../pages/admin/LeadDetailsPage';
import LeadListPage from '../pages/admin/LeadListPage';
import MaintenanceDetailsPage from '../pages/admin/MaintenanceDetailsPage';
import MakeDetailsPage from '../pages/admin/MakeDetailsPage';
import MakeListPage from '../pages/admin/MakeListPage';
import MarketingDashboardPage from '../pages/admin/MarketingDashboardPage';
import MobilityBookingDetailsPage from '../pages/admin/MobilityBookingDetailsPage';
import MobilityBookingListPage from '../pages/admin/MobilityBookingListPage';
import MobilityDetailPage from '../pages/admin/MobilityDetailPage';
import MobilityListPage from '../pages/admin/MobilityListPage';
import ModelConfiguratorDetailsPage from '../pages/admin/ModelConfiguratorDetailsPage';
import ModelDetails from '../pages/admin/ModelDetailsPage';
import ModelListPage from '../pages/admin/ModelListPage';
import ModuleDetailsPage from '../pages/admin/ModuleDetailsPage';
import UpdateAdyenPaymentSettingPage from '../pages/admin/ModuleDetailsPage/AdyenPaymentModulePage/UpdateAdyenPaymentSettingPage';
import UpdateAutoplaySettingPage from '../pages/admin/ModuleDetailsPage/AutoplayModulePage/UpdateAutoplaySettingPage';
import UpdateCtsSettingPage from '../pages/admin/ModuleDetailsPage/CtsModulePage/CtsSetting/UpdateCtsSettingPage';
import UpdateFiservPaymentSettingPage from '../pages/admin/ModuleDetailsPage/FiservPaymentModulePage/UpdateFiservPaymentSettingPage';
import UpdateMyInfoSettingPage from '../pages/admin/ModuleDetailsPage/MyInfoModule/UpdateMyInfoSettingPage';
import UpdatePayGatePaymentSettingPage from '../pages/admin/ModuleDetailsPage/PayGatePaymentModulePage/UpdatePayGatePaymentSettingPage';
import UpdatePorschePaymentSettingPage from '../pages/admin/ModuleDetailsPage/PorschePaymentModulePage/UpdatePorschePaymentSettingPage';
import UpdateTtbPaymentSettingPage from '../pages/admin/ModuleDetailsPage/TtbPaymentModulePage/UpdateTtbPaymentSettingPage';
import UpdateUserlikeChatbotSettingPage from '../pages/admin/ModuleDetailsPage/UserlikeChatbotModulePage/UpdateUserlikeChatbotSettingPage';
import UpdateWhatsappLiveChatSettingPage from '../pages/admin/ModuleDetailsPage/WhatsappLiveChatModulePage/UpdateWhatsappLiveChatSettingPage';
import ModuleListPage from '../pages/admin/ModuleListPage';
import PorscheRetainPage from '../pages/admin/PorscheRetainPage';
import PromoCodeDetailsPage from '../pages/admin/PromoCodeDetailsPage';
import PromoCodeListPage from '../pages/admin/PromoCodeListing';
import ReservationDetailsPage from '../pages/admin/ReservationDetailsPage';
import ReservationListPage from '../pages/admin/ReservationListPage';
import RoleDetailsPage from '../pages/admin/RoleDetailsPage';
import RoleListPage from '../pages/admin/RoleListPage';
import RouterDetailsPage from '../pages/admin/RouterDetailsPage';
import RouterListPage from '../pages/admin/RouterListPage';
import SalesControlBoard from '../pages/admin/SalesControlBoard';
import SubModelListPage from '../pages/admin/SubModelListPage';
import SubmodelDetails from '../pages/admin/SubmodelDetailsPage';
import TradeInDetailsPage from '../pages/admin/TradeInDetailsPage';
import TradeInListPage from '../pages/admin/TradeInListPage';
import UpdateKYCPresetPage from '../pages/admin/UpdateKYCPresetPage';
import UserDetailsPage from '../pages/admin/UserDetailsPage';
import UserGroupDetailsPage from '../pages/admin/UserGroupDetailPage';
import UserGroupListPage from '../pages/admin/UserGroupListPage';
import UserListPage from '../pages/admin/UserListPage';
import UserSelfPage from '../pages/admin/UserSelfPage';
import UserViewPage from '../pages/admin/UserViewPage';
import VariantConfiguratorDetailsPage from '../pages/admin/VariantConfiguratorDetailsPage';
import VariantDetails from '../pages/admin/VariantDetailsPage';
import VariantListPage from '../pages/admin/VariantListPage';
import VisitAppointmentDetailsPage from '../pages/admin/VisitAppointmentDetailsPage';
import VisitAppointmentListPage from '../pages/admin/VisitAppointmentListPage';
import WebpageDetailsPage from '../pages/admin/WebpageDetailsPage';
import WebpageList from '../pages/admin/WebpageListPage';
import ThemeComponentsProvider from '../themes';

const adminApplicationsRouter = (
    <Routes>
        <Route element={<ApplicationListPage />} path="" />
        <Route element={<ApplicationDetailsPage />} path=":id" />
        <Route element={<NotFoundResult />} path="*" />
    </Routes>
);

const AdminInnerRouter = () => {
    const { hasLeads, hasContacts, hasLeadsAndContacts } = useConsolePermissions();

    return (
        <ThemeComponentsProvider overrideTheme={CompanyTheme.Admin}>
            <ConsoleLayout>
                <Routes>
                    <Route element={<DashboardPage />} path="" />
                    <Route element={<MarketingDashboardPage />} path="marketingdashboard" />
                    <Route element={<SalesControlBoard />} path="salesControlBoard" />

                    {/* modules */}
                    <Route element={<ModuleListPage />} path="system/modules" />
                    <Route element={<AddModulePage />} path="system/modules/add" />
                    <Route element={<ModuleDetailsPage />} path="system/modules/:moduleId" />

                    {/* customer module (kyc management) */}
                    <Route element={<UpdateKYCPresetPage />} path="system/modules/:moduleId/kyc/:kycPresetId" />

                    {/* adyen payment module (payment settings) */}
                    <Route
                        element={<UpdateAdyenPaymentSettingPage />}
                        path="system/modules/:moduleId/adyen/:paymentSettingId"
                    />

                    {/* porsche payment module (payment settings) */}
                    <Route
                        element={<UpdatePorschePaymentSettingPage />}
                        path="system/modules/:moduleId/porschePayment/:paymentSettingId"
                    />

                    {/* fiserv payment module (payment settings) */}
                    <Route
                        element={<UpdateFiservPaymentSettingPage />}
                        path="system/modules/:moduleId/fiservPayment/:paymentSettingId"
                    />

                    {/* paygate payment module (payment settings) */}
                    <Route
                        element={<UpdatePayGatePaymentSettingPage />}
                        path="system/modules/:moduleId/paygatePayment/:paymentSettingId"
                    />

                    {/* TTB payment module (payment settings) */}
                    <Route
                        element={<UpdateTtbPaymentSettingPage />}
                        path="system/modules/:moduleId/ttbPayment/:paymentSettingId"
                    />

                    {/* whatsapp live chat module */}
                    <Route
                        element={<UpdateWhatsappLiveChatSettingPage />}
                        path="system/modules/:moduleId/whatsapp/:settingsId"
                    />

                    {/* userlike chatbot module */}
                    <Route
                        element={<UpdateUserlikeChatbotSettingPage />}
                        path="system/modules/:moduleId/userlike/:settingsId"
                    />

                    {/* MyInfo module (MyInfo settings) */}
                    <Route
                        element={<UpdateMyInfoSettingPage />}
                        path="system/modules/:moduleId/myInfo/:myInfoSettingId"
                    />

                    {/* CTS module setting */}
                    <Route element={<UpdateCtsSettingPage />} path="system/modules/:moduleId/cts/:settingId" />

                    {/* Autoplay module setting */}
                    <Route
                        element={<UpdateAutoplaySettingPage />}
                        path="system/modules/:moduleId/autoplay/:settingId"
                    />

                    {/* routers */}
                    <Route element={<RouterListPage />} path="system/routers" />
                    <Route element={<RouterDetailsPage />} path="system/routers/:routerId" />
                    <Route element={<AddEndpointPage />} path="system/routers/:routerId/addEndpoint" />

                    {/* languages */}
                    <Route element={<LanguagePackListPage />} path="system/languages" />
                    <Route element={<LanguagePackDetailsPage />} path="system/languages/:languagePackId" />

                    {/* user management */}
                    <Route element={<UserListPage />} path="accesses/users" />
                    <Route element={<UserViewPage />} path="accesses/users/:userId" />
                    <Route element={<RoleListPage />} path="accesses/roles" />
                    <Route element={<RoleDetailsPage />} path="accesses/roles/:roleId" />
                    <Route element={<AddUserPage />} path="accesses/users/add" />
                    <Route element={<UserDetailsPage />} path="accesses/users/:userId/update" />
                    <Route element={<UserGroupListPage />} path="accesses/userGroups" />
                    <Route element={<UserGroupDetailsPage />} path="accesses/userGroups/:userGroupId" />

                    {/* vehicle management */}
                    <Route element={<MakeListPage />} path="vehicles/makes" />
                    <Route element={<AddMakePage />} path="vehicles/makes/add" />
                    <Route element={<MakeDetailsPage />} path="vehicles/makes/:makeId" />

                    <Route element={<ModelListPage />} path="vehicles/models" />
                    <Route element={<AddModel />} path="vehicles/models/add" />
                    <Route element={<ModelDetails />} path="vehicles/models/:modelId" />

                    <Route element={<SubModelListPage />} path="vehicles/submodels" />
                    <Route element={<AddSubmodel />} path="vehicles/submodels/add" />
                    <Route element={<SubmodelDetails />} path="vehicles/submodels/:submodelId" />

                    <Route element={<VariantListPage />} path="vehicles/variants" />
                    <Route element={<AddVariant />} path="vehicles/variants/add" />
                    <Route element={<VariantDetails />} path="vehicles/variants/:variantId" />

                    {/* company */}
                    <Route element={<CompanyListPage />} path="companies" />
                    <Route element={<AddCompanyPage />} path="companies/add" />
                    <Route element={<CompanyDetailsPage />} path="companies/:companyId" />

                    {/* campaigns */}
                    <Route element={<CampaignListPage />} path="campaigns" />
                    <Route element={<CampaignDetailsPage />} path="campaigns/:campaignId" />
                    <Route element={<AddCampaignPage />} path="campaigns/add" />

                    {/* consents and declarations */}
                    <Route element={<ConsentsAndDeclarationsPage />} path="consents" />
                    <Route
                        element={<ConsentsAndDeclarationsDetailsPage />}
                        path="consents/:consentsAndDeclarationsId"
                    />
                    <Route element={<AddConsentsAndDeclarations />} path="consents/add" />

                    {/* banks */}
                    <Route element={<BankListPage />} path="banks" />
                    <Route element={<AddBankPage />} path="banks/add" />
                    <Route element={<BankDetailsPage />} path="banks/:bankId" />

                    {/* maintenance */}
                    <Route element={<MaintenanceDetailsPage />} path="maintenance" />

                    {/* finance products */}
                    <Route element={<FinanceProductListPage />} path="financeProducts" />
                    <Route element={<FinanceProductDetailsPage />} path="financeProducts/:productId" />
                    <Route element={<AddFinanceProductPage />} path="/financeProducts/add" />

                    {/* Insurance Products */}
                    <Route element={<InsuranceProductListPage />} path="insuranceProducts" />
                    <Route element={<InsuranceProductDetails />} path="insuranceProducts/:insuranceProductId" />
                    <Route element={<AddInsuranceProductPage />} path="/insuranceProducts/add" />

                    {/* events */}
                    <Route element={<EventList />} path="events" />
                    <Route element={<EventDetailsPage />} path="events/:eventId" />
                    <Route element={<AddEventPage />} path="/events/add" />

                    {/* dealer */}
                    <Route element={<AddDealerPage />} path="dealers/add" />
                    <Route element={<DealerDetailsPage />} path="dealers/:dealerId" />
                    <Route element={<DealerListPage />} path="dealers" />

                    {/* configurator */}
                    <Route element={<AddModelConfiguratorPage />} path="configurators/add" />
                    <Route
                        element={<VariantConfiguratorDetailsPage />}
                        path="configurators/:modelConfiguratorId/:variantConfiguratorId"
                    />
                    <Route element={<ModelConfiguratorDetailsPage />} path="configurators/:modelConfiguratorId" />
                    <Route element={<ConfiguratorListPage />} path="configurators" />
                    <Route element={<ApplicationDetailsPage />} path="configurators/:modelConfiguratorId/l/:id" />
                    <Route
                        element={<InventoryDetailsPage />}
                        path="configurators/:modelConfiguratorId/inventory/:inventoryId"
                    />
                    <Route
                        element={<InventoryStockDetailsPage />}
                        path="configurators/:modelConfiguratorId/inventory/:inventoryId/:stockId"
                    />

                    {/* Inventories */}
                    <Route element={<InventoryListPage />} path="inventories" />
                    <Route element={<AddInventoryPage />} path="inventories/add" />
                    <Route element={<InventoryDetailsPage />} path="inventories/:inventoryId" />
                    <Route element={<InventoryStockDetailsPage />} path="inventories/:inventoryId/:stockId" />

                    {/* Insurances */}
                    <Route element={<InsuranceListPage />} path="insurances" />
                    <Route element={<InsuranceDetailsPage />} path="insurances/:id" />

                    {/* Reservations and leads/contacts */}
                    <Route element={<ReservationListPage />} path="reservations" />
                    <Route element={<ReservationDetailsPage />} path="reservations/:id" />
                    {hasLeads && (
                        <>
                            <Route element={<LeadListPage leadStage={LeadStageOption.Lead} />} path="leads" />
                            <Route element={<LeadDetailsPage />} path="leads/:id" />
                        </>
                    )}
                    {hasContacts && (
                        <>
                            <Route element={<LeadListPage leadStage={LeadStageOption.Contact} />} path="contacts" />
                            <Route element={<LeadDetailsPage />} path="contacts/:id" />
                        </>
                    )}
                    {hasLeadsAndContacts && (
                        <>
                            <Route
                                element={<LeadListPage leadStage={LeadStageOption.LeadAndContact} />}
                                path="leadsAndContacts"
                            />
                            <Route element={<LeadDetailsPage />} path="leadsAndContacts/:id" />
                        </>
                    )}

                    {/* Customers */}
                    {/* <Route element={<CustomerListPage />} path="customers" /> */}
                    <Route element={<CustomerDetailsPage />} path="customers/:id" />

                    {/* others */}
                    <Route element={<DashboardPage />} path="dashboard" />
                    <Route element={<PorscheRetainPage />} path="porscheRetain" />
                    <Route element={adminApplicationsRouter} path="applications/*" />
                    <Route element={<UserSelfPage />} path="self" />
                    <Route element={<NotFoundResult />} path="*" />

                    {/* promo codes */}
                    <Route element={<PromoCodeListPage />} path="promoCodes" />
                    <Route element={<AddPromoCodePage />} path="promoCodes/add" />
                    <Route element={<PromoCodeDetailsPage />} path="promoCodes/:promoCodeId" />

                    {/* banners */}
                    <Route element={<BannerListPage />} path="banners" />
                    <Route element={<AddBannerPage />} path="banners/add" />
                    <Route element={<BannerDetailsPage />} path="banners/:bannerId" />

                    {/* labels */}
                    <Route element={<LabelsListPage />} path="labels" />
                    <Route element={<AddLabelsPage />} path="labels/add" />
                    <Route element={<LabelsDetailsPage />} path="labels/:labelId" />

                    {/* webpages */}
                    <Route element={<WebpageList />} path="webpages" />
                    <Route element={<AddWebpagePage />} path="webpages/add" />
                    <Route element={<WebpageDetailsPage />} path="webpages/:webpageId" />

                    {/* mobilities */}
                    <Route element={<MobilityListPage />} path="mobilityoptions" />
                    <Route element={<AddMobilityPage />} path="mobilityoptions/add" />
                    <Route element={<MobilityDetailPage />} path="mobilityoptions/:mobilityId" />
                    <Route element={<MobilityBookingListPage />} path="mobilitybookings" />
                    <Route element={<MobilityBookingDetailsPage />} path="mobilitybookings/:id" />

                    {/* porsche cts */}
                    <Route element={<CtsDetailsPage />} path="cts" />

                    {/* Finder vehicle */}
                    <Route element={<FinderVehicleListPage />} path="finderVehicles" />
                    <Route element={<FinderVehicleDetailsPage />} path="finderVehicles/:id" />

                    {/* Appointments */}
                    <Route element={<AppointmentListPage />} path="appointments" />
                    <Route element={<AppointmentDetailsPage />} path="appointments/:id" />
                    <Route element={<VisitAppointmentListPage />} path="showroomvisits" />
                    <Route element={<VisitAppointmentDetailsPage />} path="showroomvisits/:id" />

                    {/* Insurers */}
                    <Route element={<InsurerListPage />} path="insurers" />
                    <Route element={<AddInsurerPage />} path="insurers/add" />
                    <Route element={<InsurerDetailsPage />} path="insurers/:insurerId" />

                    {/* Gift Vouchers */}
                    <Route element={<GiftVoucherListPage />} path="giftCodes" />
                    <Route element={<GiftVoucherDetails />} path="giftCodes/:id" />

                    {/* Trade-in */}
                    <Route element={<TradeInListPage />} path="tradeIns" />
                    <Route element={<TradeInDetailsPage />} path="tradeIns/:id" />

                    {/* Launch Pad Trade-in */}
                    <Route element={<ApplicationTradeInListPage />} path="tradeInRequests" />
                </Routes>
            </ConsoleLayout>
        </ThemeComponentsProvider>
    );
};

export default AdminInnerRouter;
