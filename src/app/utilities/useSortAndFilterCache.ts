import PubSub from 'pubsub-js';
import { useCallback, useEffect, useRef, useSyncExternalStore } from 'react';

export enum SortAndFilterCacheKey {
    Financing = 'financing',
    Reservation = 'reservation',
    Insurance = 'insurance',
    Lead = 'lead',
    Appointment = 'appointment',
    VisitAppointment = 'visitAppointment',
    MobilityBooking = 'mobilityBooking',
    Inventory = 'inventory',
    Customer = 'customer',
    User = 'user',
    Role = 'role',
    UserGroup = 'userGroup',
    Bank = 'bank',
    Banner = 'banner',
    Campaign = 'campaign',
    Company = 'company',
    Configurator = 'configurator',
    Consents = 'consents',
    Dealer = 'dealer',
    FinanceProduct = 'financeProduct',
    FinderVehicle = 'finderVehicle',
    Insurer = 'insurer',
    Label = 'label',
    Make = 'make',
    Mobility = 'mobility',
    Model = 'model',
    Module = 'module',
    PromoCode = 'promoCode',
    Router = 'router',
    SubModel = 'subModel',
    Variant = 'variant',
    Webpage = 'webpage',
    Event = 'event',
    GiftVoucher = 'giftVoucher',
    TradeIn = 'tradeIn',
    InsuranceProduct = 'insuranceProduct',
    ApplicationTradeIn = 'applicationTradeIn',
    FollowUp = 'followUp',
}

export enum TableSortingOrder {
    /** Ascending order */
    Asc = 'ascend',
    /** Descending order */
    Desc = 'descend',
}

export type SortOrderValue = { orderValue: TableSortingOrder | undefined };

let store: Record<string, any> = {};

export const clearAllSortAndFilter = () => {
    store = {};
};

const useSortAndFilterCache = <Value = any>(key: string, defaultValue?: Value) => {
    const subscribe = useCallback(
        (onStoreChange: () => void) => {
            const token = PubSub.subscribe(`appvantage.memoryStorage.${key}`, onStoreChange);

            return () => {
                PubSub.unsubscribe(token);
            };
        },
        [key]
    );

    const getSnapshot = useCallback(() => store[key] as Value, [key]);

    const value = useSyncExternalStore(subscribe, getSnapshot);

    const setValue = useCallback(
        (nextValue: Value) => {
            // update store value
            store[key] = nextValue;
            // then publish the change
            PubSub.publish(`appvantage.memoryStorage.${key}`);
        },
        [key]
    );

    const isInitializedFor = useRef<string | null>(null);
    const shouldInitialize = isInitializedFor.current !== key;
    const shouldSetValue = shouldInitialize && value === undefined && defaultValue !== undefined;

    useEffect(() => {
        if (shouldInitialize) {
            // ensure to set the latest key
            isInitializedFor.current = key;

            if (shouldSetValue) {
                // we can set the default value instead
                setValue(defaultValue);
            }
        }
    }, [key, defaultValue, shouldSetValue, shouldInitialize, isInitializedFor, setValue]);

    const currentValue = shouldSetValue ? defaultValue : value;

    return [currentValue, setValue] as const;
};

export default useSortAndFilterCache;
