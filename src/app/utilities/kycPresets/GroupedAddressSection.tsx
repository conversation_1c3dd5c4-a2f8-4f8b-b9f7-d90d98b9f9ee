import { Col, Row, Typography } from 'antd';
import type { ReactNode } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { AddressAutofillType } from '../../api/types';
import SearchAddressButton from '../../components/address/SearchAddressButton';

const SubHeader = styled(Typography.Paragraph)`
    font-size: 18px;
    font-weight: bold;
    line-height: 1;
    margin: 0;
`;

type GroupedAddressSectionProps = {
    children?: ReactNode;
    addressAutofillType?: AddressAutofillType;
};

const GroupedAddressSection = ({ children, addressAutofillType }: GroupedAddressSectionProps) => {
    const { t } = useTranslation('customerDetails');

    if (!children) {
        return null;
    }

    return (
        <Row gutter={[24, 16]}>
            <Col span={24}>
                <Row align="middle" gutter={[8, 8]}>
                    <Col>
                        <SubHeader style={{ margin: 0 }}>{t('customerDetails:subHeaders.addressDetails')}</SubHeader>
                    </Col>
                    {addressAutofillType === AddressAutofillType.Partial && (
                        <Col>
                            <SearchAddressButton />
                        </Col>
                    )}
                </Row>
            </Col>
            <Col span={24}>{children}</Col>
        </Row>
    );
};

export default GroupedAddressSection;
