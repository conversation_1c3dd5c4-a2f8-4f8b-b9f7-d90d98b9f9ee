import { PText } from '@porsche-design-system/components-react';
import { Col, Row, Space } from 'antd';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import type { DealerJourneyDataFragment } from '../../../../../api/fragments/DealerJourneyData';
import renderMarkdown from '../../../../../utilities/renderMarkdown';
import useTranslatedString from '../../../../../utilities/useTranslatedString';

export type DealerInfoProps = {
    dealer: DealerJourneyDataFragment;
    textAlign?: 'left' | 'center' | 'right';
};

const DealerInfo = ({ dealer, textAlign }: DealerInfoProps) => {
    const { t } = useTranslation('configuratorJourney');
    const translatedString = useTranslatedString();
    const dealerDetailContents = useMemo(
        () =>
            [
                dealer.contact.address?.defaultValue ? translatedString(dealer.contact.address) : null,
                (dealer.contact.telephone.prefix && dealer.contact.telephone.value) ??
                    t('configuratorJourney:thankyou.contents.dealerInfo.phone', {
                        value: dealer.contact.telephone.value,
                        prefix: dealer.contact.telephone.prefix,
                    }),
                dealer.contact.email ??
                    t('configuratorJourney:thankyou.contents.dealerInfo.email', {
                        email: dealer.contact.email,
                    }),
                dealer.contact.additionalInfo?.defaultValue
                    ? renderMarkdown(translatedString(dealer.contact.additionalInfo))
                    : null,
            ].filter(Boolean),
        [
            dealer.contact.additionalInfo,
            dealer.contact.address,
            dealer.contact.email,
            dealer.contact.telephone.prefix,
            dealer.contact.telephone.value,
            t,
            translatedString,
        ]
    );

    return (
        <Space direction="vertical" size={16} style={{ width: '100%', textAlign }}>
            <PText size="medium" weight="bold">
                {translatedString(dealer.legalName)}
            </PText>
            {dealerDetailContents.length > 0 && (
                <Row gutter={[4, 4]}>
                    {dealerDetailContents.map(dealerDetailContent => (
                        <Col span={24}>
                            <PText>{dealerDetailContent}</PText>
                        </Col>
                    ))}
                </Row>
            )}
        </Space>
    );
};

export default DealerInfo;
