import { useApolloClient } from '@apollo/client';
import { PHeading } from '@porsche-design-system/components-react';
import { Col, Row } from 'antd';
import { useFormikContext } from 'formik';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import styled, { css } from 'styled-components';
import {
    GetCustomerFieldsFromFilesDocument,
    type GetCustomerFieldsFromFilesQuery,
    type GetCustomerFieldsFromFilesQueryVariables,
} from '../../../../../api/queries/getCustomerFieldsFromFiles';
import useFetchPorscheIdAuthorizedUrl from '../../../../../components/PorscheID/useFetchPorscheIdAuthorizedUrl';
import { useCompany } from '../../../../../components/contexts/CompanyContextManager';
import { useRouter } from '../../../../../components/contexts/shared';
import { useOcrDetectedHandler } from '../../../../../components/ocr';
import { useOcrFilesManagerContext } from '../../../../../components/ocr/OcrFilesManager';
import OcrModal from '../../../../../components/ocr/OcrModal';
import Button from '../../../../../themes/porscheV3/Button';
import notification from '../../../../../themes/porscheV3/notification';
import breakpoints from '../../../../../utilities/breakpoints';
import getApolloErrors from '../../../../../utilities/getApolloErrors';
import useDealershipSettingId from '../../../../../utilities/useDealershipSettingId';
import { useSearchCapCustomerContext } from '../../../../shared/JourneyPage/C@P/SearchCapCustomerOnKYC/shared';
import { useEventJourneyKycAndAgreementContext } from '../../Entrypoint/EventJourneyKycAndAgreement';
import { useEventJourneySetupContext } from '../../Entrypoint/EventJourneySetup';
import { usePersistEventJourneyValues } from '../../Journey/usePersistEventJourneyValues';
import OcrAndMyinfo from '../CustomerDetails/OcrAndMyInfo';
import type { ApplicantFormValues } from '../shared';
import type { InnerType } from './types';

const ApplicantIntegrationSectionContainer = styled.div`
    width: 100%;
    background-color: #eeeff2;
    padding: 16px;
    border-radius: 8px;

    @media (min-width: ${breakpoints.sm}) {
        padding: 24px;
    }
`;

const defaultMyinfoScale = css`
    svg {
        transform: scale(1) translate(0px, 0px);
    }
`;

const fillSpanButton = css`
    justify-content: flex-start;
    & div:has(> p-button) {
        min-width: 0;
        max-width: 100%;
    }
`;

// All button have the same width as base myinfo button
const uniformButtonSpan = css`
    & > * {
        flex: 0 0 318px;
        width: 318px;
    }

    ${defaultMyinfoScale}
    ${fillSpanButton}
`;

// All button adjusted so it can fill just half of container so it can have 2 column
const halfButtonSpan = css`
    & > * {
        flex: 0 0 290px;
        width: 290px;
    }

    ${fillSpanButton}
`;

// Default button span. Let the width of the button is auto following the content within the button
const defaultButtonSpan = css`
    & > * {
        flex: none;
        width: auto;
    }
    ${defaultMyinfoScale}
    ${fillSpanButton}
`;

const ButtonContainer = styled.div<{ buttonCount: number }>`
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    gap: 16px;
    margin-top: 16px;
    justify-content: center;
    align-content: flex-start;

    // Mobile view start
    svg {
        transform: scale(0.95) translate(-8px, 0px);
    }

    & > * {
        display: block;
        flex: 1 1 100%;
        max-width: 100%;
    }

    & div:has(> p-button) {
        min-width: 100%;

        & > p-button {
            width: 100%;
        }
    }

    // Mobile view end

    @media (min-width: 420px) {
        svg {
            transform: scale(1.15) translate(16px, 0px);
        }
    }

    @media (min-width: ${breakpoints.sm}) {
        ${({ buttonCount }) =>
            buttonCount > 1
                ? css`
                      svg {
                          transform: scale(1.12) translate(15px, 0px);
                      }
                      ${halfButtonSpan}
                  `
                : defaultButtonSpan}

    @media (min-width: ${breakpoints.md}) {
        ${({ buttonCount }) =>
            buttonCount > 2
                ? css`
                      svg {
                          transform: scale(0.9) translate(-18px, 0px);
                      }
                      ${halfButtonSpan}
                  `
                : defaultButtonSpan}
    }

    @media (min-width: ${breakpoints.lg}) {
        ${({ buttonCount }) => (buttonCount > 3 ? uniformButtonSpan : defaultButtonSpan)}
    }

    @media (min-width: ${breakpoints.xl}) {
        ${({ buttonCount }) => (buttonCount > 4 ? uniformButtonSpan : defaultButtonSpan)}
    }

    @media (min-width: ${breakpoints.xxl}) {
        ${defaultButtonSpan}
    }
`;

const ApplicantIntegrationSection = ({
    capIntegration,
    myInfoIntegration,
    porscheIdIntegration,
    state,
}: Pick<InnerType, 'capIntegration' | 'myInfoIntegration' | 'porscheIdIntegration' | 'state'>) => {
    const { t } = useTranslation([
        'eventApplicantForm',

        // Load these first, when there are components that call these translation
        // It won't trigger portal loading
        'capApplication',
        'launchpadLeadDetails',
        'applicationDetails',
    ]);
    const company = useCompany();
    const router = useRouter();

    const { save: persistEventJourneyValue, persistedValue: persistedEventValue } = usePersistEventJourneyValues();
    const { endpoint, event } = useEventJourneySetupContext();
    const { kycState } = useEventJourneyKycAndAgreementContext();
    const { values } = useFormikContext<ApplicantFormValues>();

    // C@P Integration functions
    const { showSearchCapCustomerButton } = capIntegration;
    const searchCapCustomerManager = useSearchCapCustomerContext();
    const searchCapCustomerAction = useCallback(
        (e: React.MouseEvent<HTMLButtonElement>) => {
            e.stopPropagation();
            searchCapCustomerManager.showSearchComponent(true);
        },
        [searchCapCustomerManager]
    );

    // MyInfo Integration functions
    const dealershipSettingId = useDealershipSettingId(values.dealerId);
    const { setWithMyInfo, withMyInfo } = myInfoIntegration;
    const myInfoEnabled = !!dealershipSettingId(event.myInfoSetting);

    // OCR Integration functions
    const [ocrModalVisible, setOcrModalVisible] = useState(false);
    const ocrModalHandlers = useMemo(
        () => ({ hideModal: () => setOcrModalVisible(false), showModal: () => setOcrModalVisible(true) }),
        [setOcrModalVisible]
    );
    const onOcrDetected = useOcrDetectedHandler(kycState.active);
    const { files } = useOcrFilesManagerContext();
    const client = useApolloClient();
    const onOcrConfirm = useCallback(async () => {
        try {
            const response = await client.query<
                GetCustomerFieldsFromFilesQuery,
                GetCustomerFieldsFromFilesQueryVariables
            >({
                query: GetCustomerFieldsFromFilesDocument,
                variables: {
                    countryCode: company?.countryCode,
                    files: Object.entries(files).map(([kind, file]) => ({ kind, file })),
                },
                fetchPolicy: 'no-cache',
            });

            if (response.data?.fields) {
                onOcrDetected(response.data.fields);
            }
        } catch (error) {
            const apolloErrors = getApolloErrors(error);

            if (apolloErrors !== null) {
                const { $root: rootError } = apolloErrors;

                if (rootError) {
                    notification.error(rootError);
                }
            } else {
                console.error(error);
            }
        }
    }, [client, company?.countryCode, files, onOcrDetected]);

    // Porsche ID integration functions
    const { porscheIdIntegrationEnabled, submitDraftWithPorscheId } = porscheIdIntegration;
    const fetchPorscheIdAuthorizedUrl = useFetchPorscheIdAuthorizedUrl();

    const redirectToPorscheIDPortal = useCallback(
        async (e: React.MouseEvent<HTMLButtonElement>) => {
            e.stopPropagation();

            // Set appointment values inside temporary storage, that will be retrieved later
            persistEventJourneyValue({
                ...persistedEventValue,
                appointment: values.appointment,
                visitAppointment: values.visitAppointment,
            });

            if (state?.application) {
                fetchPorscheIdAuthorizedUrl.requestForAuthorizedUrl({
                    applicationId: state?.application.id,
                    routerId: router.id,
                    endpointId: endpoint.id,
                });
            } else {
                notification.loading({
                    content: t('eventApplicantForm:messages.creationSubmitting'),
                    duration: 0,
                    key: 'primary',
                });

                const newApplicationId = await submitDraftWithPorscheId();
                if (newApplicationId) {
                    fetchPorscheIdAuthorizedUrl.requestForAuthorizedUrl({
                        applicationId: newApplicationId,
                        routerId: router.id,
                        endpointId: endpoint.id,
                    });
                }
            }
        },
        [
            endpoint.id,
            fetchPorscheIdAuthorizedUrl,
            persistEventJourneyValue,
            persistedEventValue,
            router.id,
            state?.application,
            submitDraftWithPorscheId,
            t,
            values.appointment,
            values.visitAppointment,
        ]
    );

    const actionButtons = useMemo(
        () =>
            [
                company?.countryCode === 'SG' && !withMyInfo && myInfoEnabled && (
                    <div className="myinfo-button">
                        <OcrAndMyinfo
                            dealerId={values.dealerId}
                            endpoint={endpoint}
                            event={event}
                            kycPresets={kycState.active}
                            setWithMyInfo={setWithMyInfo}
                            state={state}
                            withMyInfo={withMyInfo}
                            singpassButtonOnly
                        />
                    </div>
                ),
                porscheIdIntegrationEnabled && !values.customerCiamId && (
                    <Button onClick={redirectToPorscheIDPortal} porscheFallbackIcon="user" type="secondary">
                        {t('eventApplicantForm:applicantIntegrationSection.buttons.retrieveUsingPorscheId')}
                    </Button>
                ),
                showSearchCapCustomerButton && !values.capValues?.businessPartnerGuid && (
                    <Button onClick={searchCapCustomerAction} porscheFallbackIcon="search" type="secondary">
                        {t('eventApplicantForm:applicantIntegrationSection.buttons.retrieveFromCap')}
                    </Button>
                ),
                !withMyInfo && endpoint.eventApplicationModule.isOcrEnabled && (
                    <>
                        <Button porscheFallbackIcon="camera" type="secondary">
                            {t('eventApplicantForm:applicantIntegrationSection.buttons.retrieveUsingId')}
                        </Button>
                        <OcrModal
                            hide={ocrModalHandlers.hideModal}
                            onConfirm={onOcrConfirm}
                            visible={ocrModalVisible}
                        />
                    </>
                ),
            ].filter(Boolean),
        [
            company?.countryCode,
            endpoint,
            event,
            kycState.active,
            myInfoEnabled,
            ocrModalHandlers.hideModal,
            ocrModalVisible,
            onOcrConfirm,
            porscheIdIntegrationEnabled,
            redirectToPorscheIDPortal,
            searchCapCustomerAction,
            setWithMyInfo,
            showSearchCapCustomerButton,
            state,
            t,
            values.capValues?.businessPartnerGuid,
            values.customerCiamId,
            values.dealerId,
            withMyInfo,
        ]
    );

    if (!actionButtons.length) {
        return null;
    }

    return (
        <ApplicantIntegrationSectionContainer>
            <Col span={24}>
                <Row>
                    <PHeading size="small">{t('eventApplicantForm:applicantIntegrationSection.title')}</PHeading>
                </Row>
                <ButtonContainer buttonCount={actionButtons.length}>
                    {actionButtons.map(button => button)}
                </ButtonContainer>
            </Col>
        </ApplicantIntegrationSectionContainer>
    );
};

export default ApplicantIntegrationSection;
