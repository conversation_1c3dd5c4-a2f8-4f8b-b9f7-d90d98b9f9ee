import dayjs from 'dayjs';
import { UploadFileWithPreviewFormDataFragment } from '../../../../api/fragments';
import { UpdateAppointmentModuleMutationVariables } from '../../../../api/mutations/updateAppointmentModule';
import {
    AppointmentModuleEmailContentInput,
    AppointmentModuleEmailContentCustomerInput,
    AppointmentModuleEmailContentFinderReservationInput,
} from '../../../../api/types';

export enum HeaderTabs {
    MainDetails = 'mainDetails',
    Email = 'email',
}

type EmailContentWithIntroImage = AppointmentModuleEmailContentInput & {
    introImage?: File | UploadFileWithPreviewFormDataFragment;
};

type EmailContentFinderReservationWithIntroImage = AppointmentModuleEmailContentFinderReservationInput & {
    introImage?: File | UploadFileWithPreviewFormDataFragment;
};

export type AppointmentEmailFormValues = {
    customer: {
        [key in keyof AppointmentModuleEmailContentCustomerInput]: EmailContentWithIntroImage;
    };
    salesPerson: {
        submitConfirmation: EmailContentWithIntroImage;
        bookingCancellation: EmailContentWithIntroImage;
        finderReservation: EmailContentFinderReservationWithIntroImage;
        endTestDriveReminder: EmailContentWithIntroImage;
    };
};

export type FormValues = Omit<UpdateAppointmentModuleMutationVariables['settings'], 'bookingTimeSlot'> & {
    bookingTimeSlot: Array<
        Omit<UpdateAppointmentModuleMutationVariables['settings']['bookingTimeSlot'][number], 'slot'> & {
            slot: dayjs.Dayjs;
        }
    >;
};
