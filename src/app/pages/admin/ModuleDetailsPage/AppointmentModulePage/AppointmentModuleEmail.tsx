/* eslint-disable max-len */
import { useApolloClient } from '@apollo/client';
import { message } from 'antd';
import { Formik } from 'formik';
import { omit } from 'lodash/fp';
import { useTranslation } from 'react-i18next';
import { AppointmentModuleSpecsFragment } from '../../../../api/fragments';
import {
    UpdateAppointmentModuleEmailContentDocument,
    UpdateAppointmentModuleEmailContentMutation,
    UpdateAppointmentModuleEmailContentMutationVariables,
} from '../../../../api/mutations/updateAppointmentModuleEmailContent';
import { EmailContentUpdateType } from '../../../../api/types';
import Form from '../../../../components/fields/Form';
import useHandleError from '../../../../utilities/useHandleError';
import useValidator from '../../../../utilities/useValidator';
import validators from '../../../../utilities/validators';
import AppointmentModuleEmailForm from './AppointmentModuleEmailForm';
import { AppointmentEmailFormValues } from './shared';
import useHandleAppointmentModuleAssets from './useHandleAppointmentModuleAssets';

const validatorEmailContentItem = validators.compose(
    validators.requiredString('subject.defaultValue.defaultValue'),
    validators.requiredString('introTitle.defaultValue.defaultValue'),
    validators.requiredString('contentText.defaultValue.defaultValue')
);

const validator = validators.compose(
    validators.nest(
        'customer',
        validators.compose(
            validators.nest('submitConfirmation', validatorEmailContentItem),
            validators.nest('bookingAmendment', validatorEmailContentItem),
            validators.nest('bookingConfirmation', validatorEmailContentItem),
            validators.nest('endTestDriveWithProcess', validatorEmailContentItem),
            validators.nest('completeTestDriveWithoutProcess', validatorEmailContentItem)
        )
    ),
    validators.nest(
        'salesPerson',
        validators.compose(
            validators.nest('submitConfirmation', validatorEmailContentItem),
            validators.nest('finderReservation', validatorEmailContentItem)
        )
    )
);

type AppointmentModuleEmailProps = {
    module: AppointmentModuleSpecsFragment;
};

const AppointmentModuleEmail = ({ module }: AppointmentModuleEmailProps) => {
    const { t } = useTranslation(['appointmentModuleDetails']);
    const apolloClient = useApolloClient();
    const validate = useValidator(validator);

    const handleAppointmentModuleAssets = useHandleAppointmentModuleAssets();

    // TO DO VF-1474: Make sure the functionality with booking cancellation is working fine
    const onSubmit = useHandleError(
        async ({ customer, salesPerson }: AppointmentEmailFormValues) => {
            message.loading({
                content: t('appointmentModuleDetails:messages.updateEmailContentsSubmitting'),
                key: 'primary',
                duration: 0,
            });

            await apolloClient.mutate<
                UpdateAppointmentModuleEmailContentMutation,
                UpdateAppointmentModuleEmailContentMutationVariables
            >({
                mutation: UpdateAppointmentModuleEmailContentDocument,
                variables: {
                    moduleId: module.id,
                    settings: {
                        emailContentUpdateType: EmailContentUpdateType.Module,
                        customer: {
                            endTestDriveWithProcess: omit(['introImage'], customer.endTestDriveWithProcess),
                            submitConfirmation: omit(['introImage'], customer.submitConfirmation),
                            bookingConfirmation: omit(['introImage'], customer.bookingConfirmation),
                            bookingCancellation: omit(['introImage'], customer.bookingCancellation),
                            bookingAmendment: omit(['introImage'], customer.bookingAmendment),
                            completeTestDriveWithoutProcess: omit(
                                ['introImage'],
                                customer.completeTestDriveWithoutProcess
                            ),
                        },
                        salesPerson: {
                            submitConfirmation: omit(['introImage'], salesPerson.submitConfirmation),
                            bookingCancellation: omit(['introImage'], salesPerson.bookingCancellation),
                            finderReservation: omit(['introImage'], salesPerson.finderReservation),
                            endTestDriveReminder: omit(['introImage'], salesPerson.endTestDriveReminder),
                        },
                    },
                },
            });

            await handleAppointmentModuleAssets(module.id, { customer, salesPerson }, module.emailContents);

            await message.success({
                content: t('appointmentModuleDetails:messages.updateEmailContentsSuccessful'),
                key: 'primary',
            });
        },
        [apolloClient, handleAppointmentModuleAssets, module.emailContents, module.id, t]
    );

    return (
        <Formik initialValues={module.emailContents} onSubmit={onSubmit} validate={validate} enableReinitialize>
            {({ handleSubmit }) => (
                <Form id="updateEmailContent" name="updateEmailContent" onSubmitCapture={handleSubmit}>
                    <AppointmentModuleEmailForm module={module} />
                </Form>
            )}
        </Formik>
    );
};

export default AppointmentModuleEmail;
