/* eslint-disable max-len */
import { useApolloClient } from '@apollo/client';
import { message } from 'antd';
import { Formik } from 'formik';
import { omit } from 'lodash/fp';
import { useTranslation } from 'react-i18next';
import { VisitAppointmentModuleSpecsFragment } from '../../../../api/fragments';
import {
    UpdateVisitAppointmentModuleEmailContentDocument,
    UpdateVisitAppointmentModuleEmailContentMutation,
    UpdateVisitAppointmentModuleEmailContentMutationVariables,
} from '../../../../api/mutations/updateVisitAppointmentModuleEmailContent';
import { EmailContentUpdateType } from '../../../../api/types';
import Form from '../../../../components/fields/Form';
import useHandleError from '../../../../utilities/useHandleError';
import useValidator from '../../../../utilities/useValidator';
import validators from '../../../../utilities/validators';
import VisitAppointmentModuleEmailForm from './VisitAppointmentModuleEmailForm';
import { ShowroomVisitAppointmentEmailFormValues } from './shared';
import useHandleVisitAppointmentModuleAssets from './useHandleVisitAppointmentModuleAssets';

const validatorEmailContentItem = validators.compose(
    validators.requiredString('subject.defaultValue.defaultValue'),
    validators.requiredString('introTitle.defaultValue.defaultValue'),
    validators.requiredString('contentText.defaultValue.defaultValue')
);

const validator = validators.compose(
    validators.nest(
        'customer',
        validators.compose(
            validators.nest('submitConfirmation', validatorEmailContentItem),
            validators.nest('bookingAmendment', validatorEmailContentItem),
            validators.nest('bookingConfirmation', validatorEmailContentItem)
        )
    ),
    validators.nest('salesPerson', validators.compose(validators.nest('submitConfirmation', validatorEmailContentItem)))
);

type VisitAppointmentModuleEmailProps = {
    module: VisitAppointmentModuleSpecsFragment;
};

const VisitAppointmentModuleEmail = ({ module }: VisitAppointmentModuleEmailProps) => {
    const { t } = useTranslation(['visitAppointmentModuleDetails']);
    const apolloClient = useApolloClient();
    const validate = useValidator(validator);

    const handleVisitAppointmentModuleAssets = useHandleVisitAppointmentModuleAssets();

    const onSubmit = useHandleError(
        async ({ customer, salesPerson }: ShowroomVisitAppointmentEmailFormValues) => {
            message.loading({
                content: t('visitAppointmentModuleDetails:messages.updateEmailContentsSubmitting'),
                key: 'primary',
                duration: 0,
            });

            // TO DO VF-1474: Make sure the functionality with booking cancellation is working fine
            await apolloClient.mutate<
                UpdateVisitAppointmentModuleEmailContentMutation,
                UpdateVisitAppointmentModuleEmailContentMutationVariables
            >({
                mutation: UpdateVisitAppointmentModuleEmailContentDocument,
                variables: {
                    moduleId: module.id,
                    settings: {
                        emailContentUpdateType: EmailContentUpdateType.Module,
                        customer: {
                            submitConfirmation: omit(['introImage'], customer.submitConfirmation),
                            bookingConfirmation: omit(['introImage'], customer.bookingConfirmation),
                            bookingAmendment: omit(['introImage'], customer.bookingAmendment),
                            bookingComplete: omit(['introImage'], customer.bookingComplete),
                            bookingCancellation: omit(['introImage'], customer.bookingCancellation),
                        },
                        salesPerson: {
                            submitConfirmation: omit(['introImage'], salesPerson.submitConfirmation),
                            bookingCancellation: omit(['introImage'], salesPerson.bookingCancellation),
                        },
                    },
                },
            });

            await handleVisitAppointmentModuleAssets(module.id, { customer, salesPerson }, module.emailContents);

            await message.success({
                content: t('visitAppointmentModuleDetails:messages.updateEmailContentsSuccessful'),
                key: 'primary',
            });
        },
        [apolloClient, handleVisitAppointmentModuleAssets, module.emailContents, module.id, t]
    );

    return (
        <Formik initialValues={module.emailContents} onSubmit={onSubmit} validate={validate} enableReinitialize>
            {({ handleSubmit }) => (
                <Form id="updateEmailContent" name="updateEmailContent" onSubmitCapture={handleSubmit}>
                    <VisitAppointmentModuleEmailForm module={module} />
                </Form>
            )}
        </Formik>
    );
};

export default VisitAppointmentModuleEmail;
