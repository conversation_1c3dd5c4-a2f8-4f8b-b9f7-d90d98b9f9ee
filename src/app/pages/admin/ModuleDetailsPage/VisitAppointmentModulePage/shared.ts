import { UploadFileWithPreviewFormDataFragment } from '../../../../api/fragments';
import {
    VisitAppointmentModuleEmailContentCustomerInput,
    VisitAppointmentModuleEmailContentInput,
} from '../../../../api/types';

export enum HeaderTabs {
    MainDetails = 'mainDetails',
    Email = 'email',
}

type EmailContentWithIntroImage = VisitAppointmentModuleEmailContentInput & {
    introImage?: File | UploadFileWithPreviewFormDataFragment;
};

export type ShowroomVisitAppointmentEmailFormValues = {
    customer: {
        [key in keyof VisitAppointmentModuleEmailContentCustomerInput]: EmailContentWithIntroImage;
    };
    salesPerson: {
        submitConfirmation: EmailContentWithIntroImage;
        bookingCancellation: EmailContentWithIntroImage;
    };
};
