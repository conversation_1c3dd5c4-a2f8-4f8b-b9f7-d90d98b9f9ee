import { Col, Row } from 'antd';
import { Formik } from 'formik';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import * as permissionKind from '../../../../../shared/permissions';
import {
    DealerAssignmentObjectId,
    DealerFloatDataFragment,
    DealerIntDataFragment,
    useUpdateSalesControlBoardModuleByDealerMutation,
} from '../../../../api';
import { DealerWithPermissionsFragmentFragment } from '../../../../api/fragments/DealerWithPermissionsFragment';
import { SalesControlBoardModuleInDealerSpecsFragment } from '../../../../api/fragments/SalesControlBoardModuleInDealerSpecs';
import Form from '../../../../components/fields/Form';
import TransferField from '../../../../components/fields/TransferField';
import CollapsibleWrapper, { Panel } from '../../../../components/wrappers/CollapsibleWrapper';
import { useThemeComponents } from '../../../../themes/hooks';
import hasPermissions from '../../../../utilities/hasPermissions';
import { withCommasForNumberString } from '../../../../utilities/useFormats';
import useHandleError from '../../../../utilities/useHandleError';
import useCompanyUsers from '../../ModuleDetailsPage/modules/implementations/shared/useCompanyUsers';
import { getDealerSpecificUser } from './shared';

type FormValues = {
    testDriveMonthlyTarget: number;
    orderIntakesMonthlyTarget: number;
    retailsMonthlyTarget: number;
    financeCommissionMonthlyTarget: number;
    insuranceCommissionMonthlyTarget: number;
    salesConsultantsAssignments: string[];
};

type DealerSalesControlBoardFormProps = {
    module: SalesControlBoardModuleInDealerSpecsFragment;
    dealer: DealerWithPermissionsFragmentFragment;
};

const retrieveSalesConsultant = (salesConsultantAssignments: DealerAssignmentObjectId, dealerId: string) =>
    salesConsultantAssignments.overrides.find(override => override.dealerId === dealerId)?.value;

const retrieveDealerIntValue = (feature: DealerIntDataFragment, dealerId: string) =>
    feature.overrides.find(override => override.dealerId === dealerId)?.value;

const retrieveDealerFloatValue = (feature: DealerFloatDataFragment, dealerId: string) =>
    feature.overrides.find(override => override.dealerId === dealerId)?.value;

const DealerSalesControlBoardForm = ({ dealer, module }: DealerSalesControlBoardFormProps) => {
    const { t } = useTranslation(['salesControlBoardModuleDetails', 'moduleDetails', 'common', 'dealershipManagement']);
    const { FormFields, notification } = useThemeComponents();
    const formId = `dealerForm-${module.id}`;
    const userOptions = useCompanyUsers(module.company);

    const filteredUsersBasedOnDealerOptions = getDealerSpecificUser(dealer, userOptions);
    const [mutation] = useUpdateSalesControlBoardModuleByDealerMutation();

    const initialValues: FormValues = useMemo(() => {
        const financeCommission = retrieveDealerFloatValue(module.financeCommissionMonthlyTarget, dealer.id);
        const insuranceCommission = retrieveDealerFloatValue(module.insuranceCommissionMonthlyTarget, dealer.id);
        const orderIntakes = retrieveDealerIntValue(module.orderIntakesMonthlyTarget, dealer.id);
        const retails = retrieveDealerIntValue(module.retailsMonthlyTarget, dealer.id);
        const testDrives = retrieveDealerIntValue(module.testDriveMonthlyTarget, dealer.id);

        return {
            testDriveMonthlyTarget: testDrives || 1,
            orderIntakesMonthlyTarget: orderIntakes || 1,
            retailsMonthlyTarget: retails || 1,
            financeCommissionMonthlyTarget: financeCommission || 1,
            insuranceCommissionMonthlyTarget: insuranceCommission || 1,
            salesConsultantsAssignments: retrieveSalesConsultant(module.salesConsultantsAssignments, dealer.id) || [],
        };
    }, [
        dealer.id,
        module.financeCommissionMonthlyTarget,
        module.insuranceCommissionMonthlyTarget,
        module.orderIntakesMonthlyTarget,
        module.retailsMonthlyTarget,
        module.salesConsultantsAssignments,
        module.testDriveMonthlyTarget,
    ]);

    const onSubmit = useHandleError<FormValues>(
        async values => {
            const {
                testDriveMonthlyTarget,
                orderIntakesMonthlyTarget,
                retailsMonthlyTarget,
                financeCommissionMonthlyTarget,
                insuranceCommissionMonthlyTarget,
                salesConsultantsAssignments,
            } = values;

            // submitting message
            notification.loading({
                content: t('dealershipManagement:messages.updateSubmitting'),
                key: 'primary',
                duration: 0,
            });

            await mutation({
                variables: {
                    moduleId: module.id,

                    dealerId: dealer.id,
                    testDriveMonthlyTarget,
                    orderIntakesMonthlyTarget,
                    retailsMonthlyTarget,
                    financeCommissionMonthlyTarget,
                    insuranceCommissionMonthlyTarget,
                    salesConsultantsAssignments,
                },
            });

            // show successful message
            notification.success({
                content: t('dealershipManagement:messages.updateSuccessful'),
                key: 'primary',
            });
        },
        [dealer.id, module.id, mutation, notification, t]
    );

    const hasUpdatePermission = hasPermissions(dealer.permissions, [permissionKind.updateDealer]);

    return (
        <Formik<FormValues> initialValues={initialValues} onSubmit={onSubmit} enableReinitialize>
            {({ handleSubmit, values }) => (
                <Form id={formId} name={formId} onSubmitCapture={handleSubmit}>
                    <CollapsibleWrapper defaultActiveKey={['mainDetails']}>
                        <Panel key="mainDetails" header={t('salesControlBoardModuleDetails:mainDetails.title')}>
                            <Row gutter={10}>
                                <Col md={8} xs={24}>
                                    <FormFields.InputNumberField
                                        {...t(
                                            'salesControlBoardModuleDetails:mainDetails.fields.testDriveMonthlyTarget',
                                            { returnObjects: true }
                                        )}
                                        disabled={!hasUpdatePermission}
                                        formatter={value => withCommasForNumberString(value as string)}
                                        name="testDriveMonthlyTarget"
                                        required
                                    />
                                </Col>

                                <Col md={8} xs={24}>
                                    <FormFields.InputNumberField
                                        {...t(
                                            // eslint-disable-next-line max-len
                                            'salesControlBoardModuleDetails:mainDetails.fields.orderIntakesMonthlyTarget',
                                            { returnObjects: true }
                                        )}
                                        disabled={!hasUpdatePermission}
                                        formatter={value => withCommasForNumberString(value as string)}
                                        name="orderIntakesMonthlyTarget"
                                        required
                                    />
                                </Col>

                                <Col md={8} xs={24}>
                                    <FormFields.InputNumberField
                                        {...t(
                                            'salesControlBoardModuleDetails:mainDetails.fields.retailsMonthlyTarget',
                                            { returnObjects: true }
                                        )}
                                        disabled={!hasUpdatePermission}
                                        formatter={value => withCommasForNumberString(value as string)}
                                        name="retailsMonthlyTarget"
                                        required
                                    />
                                </Col>

                                <Col md={8} xs={24}>
                                    <FormFields.InputNumberField
                                        {...t(
                                            // eslint-disable-next-line max-len
                                            'salesControlBoardModuleDetails:mainDetails.fields.financeCommissionMonthlyTarget',
                                            { returnObjects: true }
                                        )}
                                        addonAfter="%"
                                        disabled={!hasUpdatePermission}
                                        formatter={value => withCommasForNumberString(value as string)}
                                        max={100}
                                        min={1}
                                        name="financeCommissionMonthlyTarget"
                                        required
                                    />
                                </Col>
                                <Col md={8} xs={24}>
                                    <FormFields.InputNumberField
                                        {...t(
                                            // eslint-disable-next-line max-len
                                            'salesControlBoardModuleDetails:mainDetails.fields.insuranceCommissionMonthlyTarget',
                                            { returnObjects: true }
                                        )}
                                        addonAfter="%"
                                        disabled={!hasUpdatePermission}
                                        formatter={value => withCommasForNumberString(value as string)}
                                        max={100}
                                        min={1}
                                        name="insuranceCommissionMonthlyTarget"
                                        required
                                    />
                                </Col>
                            </Row>
                            <TransferField
                                {...t(
                                    // eslint-disable-next-line max-len
                                    'salesControlBoardModuleDetails:mainDetails.fields.salesConsultantAssignments',
                                    { returnObjects: true }
                                )}
                                dataSource={filteredUsersBasedOnDealerOptions}
                                disabled={!hasUpdatePermission}
                                listStyle={{ width: '100%', height: 400 }}
                                name="salesConsultantsAssignments"
                                render={item => item.title}
                            />
                        </Panel>
                    </CollapsibleWrapper>
                </Form>
            )}
        </Formik>
    );
};

export default DealerSalesControlBoardForm;
