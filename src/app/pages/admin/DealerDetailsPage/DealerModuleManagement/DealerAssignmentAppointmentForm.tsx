/* eslint-disable max-len */
import { Col, Row, message } from 'antd';
import { Formik } from 'formik';
import { omit } from 'lodash/fp';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import * as permissionKind from '../../../../../shared/permissions';
import { AppointmentModuleInDealerSpecsFragment } from '../../../../api/fragments';
import { DealerWithPermissionsFragmentFragment } from '../../../../api/fragments/DealerWithPermissionsFragment';
import { useUpdateAppointmentModuleEmailContentMutation } from '../../../../api/mutations/updateAppointmentModuleEmailContent';
import {
    AppointmentModuleEmailContentCustomerInput,
    EmailContentUpdateType,
    TranslatedStringInput,
} from '../../../../api/types';
import Form from '../../../../components/fields/Form';
import SwitchField from '../../../../components/fields/SwitchField';
import TranslatedTextAreaField from '../../../../components/fields/TranslatedTextAreaField';
import CollapsibleWrapper, { Panel } from '../../../../components/wrappers/CollapsibleWrapper';
import hasPermissions from '../../../../utilities/hasPermissions';
import useHandleError from '../../../../utilities/useHandleError';
import useSystemSwitchData from '../../../../utilities/useSystemSwitchData';
import { addInputToOverride, getEmailContent, getEmailContentItem, setEmailItem } from './shared';

export type DealerAssignmentAppointmentFormProps = {
    module: AppointmentModuleInDealerSpecsFragment;
    dealer: DealerWithPermissionsFragmentFragment;
};

export type DealerAppointmentEmailInput = {
    subject?: TranslatedStringInput;
    introTitle?: TranslatedStringInput;
    contentText?: TranslatedStringInput;
    isSummaryVehicleVisible?: boolean;
};

type DealerAppointmentEmailFinderReservationInput = DealerAppointmentEmailInput & {
    listTestDriveTitle?: TranslatedStringInput;
    listTestDriveItem?: TranslatedStringInput;
};

type FormValues = {
    customer: {
        [key in keyof AppointmentModuleEmailContentCustomerInput]: DealerAppointmentEmailInput;
    };
    salesPerson: {
        submitConfirmation: DealerAppointmentEmailInput;
        bookingCancellation: DealerAppointmentEmailInput;
        finderReservation: DealerAppointmentEmailFinderReservationInput;
        endTestDriveReminder: DealerAppointmentEmailInput;
    };
};

type DealerAppointmentEmailFieldProps = {
    path:
        | 'customer.submitConfirmation'
        | 'customer.endTestDriveWithProcess'
        | 'customer.completeTestDriveWithoutProcess'
        | 'customer.bookingAmendment'
        | 'customer.bookingConfirmation'
        | 'customer.bookingComplete'
        | 'salesPerson.submitConfirmation'
        | 'salesPerson.finderReservation'
        | 'salesPerson.endTestDriveReminder';
    children?: React.ReactNode;
    disabled?: boolean;
};

export const DealerAppointmentEmailFields = ({ path, children, disabled }: DealerAppointmentEmailFieldProps) => {
    const { t } = useTranslation('appointmentModuleDetails');
    const { yesNoSwitch } = useSystemSwitchData();

    const getName = useCallback((name: string) => `${path}.${name}`, [path]);
    const getTranslationKey = useCallback((name: string) => `appointmentModuleDetails:fields.emails.${name}`, []);

    return (
        <Row gutter={10}>
            <Col lg={8} xs={24}>
                <TranslatedTextAreaField
                    {...t(getTranslationKey('subject'), { returnObjects: true })}
                    autoSize={{ minRows: 2, maxRows: 6 }}
                    disabled={disabled}
                    name={getName('subject')}
                />
            </Col>
            <Col lg={8} xs={24}>
                <TranslatedTextAreaField
                    {...t(getTranslationKey('introTitle'), { returnObjects: true })}
                    autoSize={{ minRows: 2, maxRows: 6 }}
                    disabled={disabled}
                    name={getName('introTitle')}
                />
            </Col>
            <Col lg={8} xs={24}>
                <TranslatedTextAreaField
                    {...t(getTranslationKey('contentText'), { returnObjects: true })}
                    autoSize={{ minRows: 2, maxRows: 6 }}
                    disabled={disabled}
                    name={getName('contentText')}
                    withContentRefinement
                />
            </Col>
            {children}
            <Col lg={8} xs={24}>
                <SwitchField
                    {...yesNoSwitch}
                    {...t(getTranslationKey('isSummaryVehicleVisible'), { returnObjects: true })}
                    name={getName('isSummaryVehicleVisible')}
                />
            </Col>
        </Row>
    );
};

const DealerAssignmentAppointmentForm = ({ module, dealer }: DealerAssignmentAppointmentFormProps) => {
    const { t } = useTranslation(['dealershipManagement', 'appointmentModuleDetails', 'moduleDetails']);

    const [updateAppointmentModuleEmailContent] = useUpdateAppointmentModuleEmailContentMutation();

    // initialize finance products and dealers for the dealer
    const initialValues: FormValues = useMemo(
        () => ({
            customer: {
                submitConfirmation: getEmailContent(module.emailContents.customer.submitConfirmation, dealer.id),
                endTestDriveWithProcess: getEmailContent(
                    module.emailContents.customer.endTestDriveWithProcess,
                    dealer.id
                ),
                completeTestDriveWithoutProcess: getEmailContent(
                    module.emailContents.customer.completeTestDriveWithoutProcess,
                    dealer.id
                ),
                bookingAmendment: getEmailContent(module.emailContents.customer.bookingAmendment, dealer.id),
                bookingConfirmation: getEmailContent(module.emailContents.customer.bookingConfirmation, dealer.id),
                bookingCancellation: getEmailContent(module.emailContents.customer.bookingCancellation, dealer.id),
            },
            salesPerson: {
                submitConfirmation: {
                    ...getEmailContent(module.emailContents.salesPerson.submitConfirmation, dealer.id),
                },
                bookingCancellation: getEmailContent(module.emailContents.salesPerson.bookingCancellation, dealer.id),
                finderReservation: {
                    ...getEmailContent(module.emailContents.salesPerson.finderReservation, dealer.id),
                    listTestDriveTitle: getEmailContentItem<TranslatedStringInput>(
                        module.emailContents.salesPerson.finderReservation.listTestDriveTitle,
                        dealer.id
                    ),
                    listTestDriveItem: getEmailContentItem<TranslatedStringInput>(
                        module.emailContents.salesPerson.finderReservation.listTestDriveItem,
                        dealer.id
                    ),
                },
                endTestDriveReminder: {
                    ...getEmailContent(module.emailContents.salesPerson.endTestDriveReminder, dealer.id),
                },
            },
        }),
        [dealer.id, module.emailContents]
    );

    const onSubmit = useHandleError<FormValues>(
        async values => {
            const { customer, salesPerson } = values;

            // submitting message
            message.loading({
                content: t('dealershipManagement:messages.updateSubmitting'),
                key: 'primary',
                duration: 0,
            });

            // TO DO VF-1474: Make sure the functionality with booking cancellation is working fine
            await updateAppointmentModuleEmailContent({
                variables: {
                    settings: {
                        emailContentUpdateType: EmailContentUpdateType.Dealer,
                        dealerId: dealer.id,
                        customer: {
                            submitConfirmation: setEmailItem(
                                dealer.id,
                                customer.submitConfirmation,
                                omit(['__typename', 'introImage'], module.emailContents.customer.submitConfirmation)
                            ),
                            endTestDriveWithProcess: setEmailItem(
                                dealer.id,
                                customer.endTestDriveWithProcess,
                                omit(
                                    ['__typename', 'introImage'],
                                    module.emailContents.customer.endTestDriveWithProcess
                                )
                            ),
                            completeTestDriveWithoutProcess: setEmailItem(
                                dealer.id,
                                customer.completeTestDriveWithoutProcess,
                                omit(
                                    ['__typename', 'introImage'],
                                    module.emailContents.customer.completeTestDriveWithoutProcess
                                )
                            ),
                            bookingAmendment: setEmailItem(
                                dealer.id,
                                customer.bookingAmendment,
                                omit(['__typename', 'introImage'], module.emailContents.customer.bookingAmendment)
                            ),
                            bookingConfirmation: setEmailItem(
                                dealer.id,
                                customer.bookingConfirmation,
                                omit(['__typename', 'introImage'], module.emailContents.customer.bookingConfirmation)
                            ),
                            bookingCancellation: setEmailItem(
                                dealer.id,
                                customer.bookingCancellation,
                                omit(['__typename', 'introImage'], module.emailContents.customer.bookingCancellation)
                            ),
                        },
                        salesPerson: {
                            submitConfirmation: setEmailItem(
                                dealer.id,
                                salesPerson.submitConfirmation,
                                omit(['__typename', 'introImage'], module.emailContents.salesPerson.submitConfirmation)
                            ),
                            bookingCancellation: setEmailItem(
                                dealer.id,
                                salesPerson.bookingCancellation,
                                omit(['__typename', 'introImage'], module.emailContents.salesPerson.bookingCancellation)
                            ),
                            finderReservation: {
                                ...setEmailItem(
                                    dealer.id,
                                    salesPerson.finderReservation,
                                    omit(
                                        ['__typename', 'introImage'],
                                        module.emailContents.salesPerson.finderReservation
                                    )
                                ),
                                listTestDriveTitle: addInputToOverride(
                                    dealer.id,
                                    module.emailContents.salesPerson.finderReservation.listTestDriveTitle,
                                    salesPerson.finderReservation.listTestDriveTitle
                                ),
                                listTestDriveItem: addInputToOverride(
                                    dealer.id,
                                    module.emailContents.salesPerson.finderReservation.listTestDriveItem,
                                    salesPerson.finderReservation.listTestDriveItem
                                ),
                            },
                            endTestDriveReminder: setEmailItem(
                                dealer.id,
                                salesPerson.endTestDriveReminder,
                                omit(
                                    ['__typename', 'introImage'],
                                    module.emailContents.salesPerson.endTestDriveReminder
                                )
                            ),
                        },
                    },
                    moduleId: module.id,
                },
            });

            // show successful message
            message.success({
                content: t('dealershipManagement:messages.updateSuccessful'),
                key: 'primary',
            });
        },
        [t, module.emailContents, module.id, updateAppointmentModuleEmailContent, dealer.id]
    );

    const formId = `dealerForm-${module.id}`;
    const hasUpdatePermission = hasPermissions(dealer.permissions, [permissionKind.updateDealer]);

    return (
        <Formik<FormValues> initialValues={initialValues} onSubmit={onSubmit} enableReinitialize>
            {({ handleSubmit }) => (
                <Form id={formId} name={formId} onSubmitCapture={handleSubmit}>
                    <CollapsibleWrapper defaultActiveKey={['emailContent']}>
                        <Panel
                            key="emailContent"
                            className="added-bottom-padding"
                            header={t('appointmentModuleDetails:tabs.email.title')}
                        >
                            <CollapsibleWrapper
                                defaultActiveKey={[
                                    'emailContentsCustomerSubmitConfirmation',
                                    'emailContentsCustomerEndTestDrive',
                                    'emailContentsCustomerCompleteTestDrive',
                                    'emailContentsCustomerBookingConfirmation',
                                    'emailContentsCustomerBookingAmendment',
                                    'emailContentsSalesPersonSubmitConfirmation',
                                    'emailContentsSalesPersonFinderReservation',
                                    'emailContentsSalesEndTestDriveReminder',
                                ]}
                            >
                                <Panel
                                    key="emailContentsCustomerSubmitConfirmation"
                                    header={t('appointmentModuleDetails:sections.emails.customer.submitConfirmation')}
                                >
                                    <DealerAppointmentEmailFields
                                        disabled={!hasUpdatePermission}
                                        path="customer.submitConfirmation"
                                    />
                                </Panel>
                                <Panel
                                    key="emailContentsCustomerBookingConfirmation"
                                    header={t('appointmentModuleDetails:sections.emails.customer.bookingConfirmation')}
                                >
                                    <DealerAppointmentEmailFields
                                        disabled={!hasUpdatePermission}
                                        path="customer.bookingConfirmation"
                                    />
                                </Panel>
                                <Panel
                                    key="emailContentsCustomerBookingAmendment"
                                    header={t('appointmentModuleDetails:sections.emails.customer.bookingAmendment')}
                                >
                                    <DealerAppointmentEmailFields
                                        disabled={!hasUpdatePermission}
                                        path="customer.bookingAmendment"
                                    />
                                </Panel>
                                {module.hasTestDriveProcess && (
                                    <Panel
                                        key="emailContentsCustomerEndTestDrive"
                                        header={t('appointmentModuleDetails:sections.emails.customer.endTestDrive')}
                                    >
                                        <DealerAppointmentEmailFields
                                            disabled={!hasUpdatePermission}
                                            path="customer.endTestDriveWithProcess"
                                        />
                                    </Panel>
                                )}
                                {!module.hasTestDriveProcess && (
                                    <Panel
                                        key="emailContentsCustomerCompleteTestDrive"
                                        header={t(
                                            'appointmentModuleDetails:sections.emails.customer.completeTestDrive'
                                        )}
                                    >
                                        <DealerAppointmentEmailFields
                                            disabled={!hasUpdatePermission}
                                            path="customer.completeTestDriveWithoutProcess"
                                        />
                                    </Panel>
                                )}
                                <Panel
                                    key="emailContentsSalesPersonSubmitConfirmation"
                                    header={t(
                                        'appointmentModuleDetails:sections.emails.salesPerson.submitConfirmation'
                                    )}
                                >
                                    <DealerAppointmentEmailFields
                                        disabled={!hasUpdatePermission}
                                        path="salesPerson.submitConfirmation"
                                    />
                                </Panel>
                                {module.hasTestDriveProcess && (
                                    <Panel
                                        key="emailContentsSalesPersonFinderReservation"
                                        header={t(
                                            'appointmentModuleDetails:sections.emails.salesPerson.finderReservation'
                                        )}
                                    >
                                        <DealerAppointmentEmailFields
                                            disabled={!hasUpdatePermission}
                                            path="salesPerson.finderReservation"
                                        >
                                            <Col lg={8} xs={24}>
                                                <TranslatedTextAreaField
                                                    {...t('appointmentModuleDetails:fields.emails.listTestDriveTitle', {
                                                        returnObjects: true,
                                                    })}
                                                    autoSize={{ minRows: 2, maxRows: 6 }}
                                                    disabled={!hasUpdatePermission}
                                                    name="salesPerson.finderReservation.listTestDriveTitle"
                                                />
                                            </Col>
                                            <Col lg={8} xs={24}>
                                                <TranslatedTextAreaField
                                                    {...t('appointmentModuleDetails:fields.emails.listTestDriveItem', {
                                                        returnObjects: true,
                                                    })}
                                                    autoSize={{ minRows: 2, maxRows: 6 }}
                                                    disabled={!hasUpdatePermission}
                                                    name="salesPerson.finderReservation.listTestDriveItem"
                                                />
                                            </Col>
                                        </DealerAppointmentEmailFields>
                                    </Panel>
                                )}
                                {module?.isReminderTimeEnabled && (
                                    <Panel
                                        key="emailContentsSalesEndTestDriveReminder"
                                        header={t(
                                            'appointmentModuleDetails:sections.emails.salesPerson.endTestDriveReminder'
                                        )}
                                    >
                                        <DealerAppointmentEmailFields
                                            disabled={!hasUpdatePermission}
                                            path="salesPerson.endTestDriveReminder"
                                        />
                                    </Panel>
                                )}
                            </CollapsibleWrapper>
                        </Panel>
                    </CollapsibleWrapper>
                </Form>
            )}
        </Formik>
    );
};

export default DealerAssignmentAppointmentForm;
