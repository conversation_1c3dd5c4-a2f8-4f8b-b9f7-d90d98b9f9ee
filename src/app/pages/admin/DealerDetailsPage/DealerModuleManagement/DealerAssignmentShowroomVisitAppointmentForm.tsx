/* eslint-disable max-len */
import { message } from 'antd';
import { Formik } from 'formik';
import { omit } from 'lodash/fp';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import * as permissionKind from '../../../../../shared/permissions';
import { DealerWithPermissionsFragmentFragment } from '../../../../api/fragments/DealerWithPermissionsFragment';
import { VisitAppointmentModuleInDealerSpecsFragment } from '../../../../api/fragments/VisitAppointmentModuleInDealerSpecs';
import { useUpdateVisitAppointmentModuleEmailContentMutation } from '../../../../api/mutations/updateVisitAppointmentModuleEmailContent';
import { EmailContentUpdateType, VisitAppointmentModuleEmailContentCustomerInput } from '../../../../api/types';
import Form from '../../../../components/fields/Form';
import CollapsibleWrapper, { Panel } from '../../../../components/wrappers/CollapsibleWrapper';
import hasPermissions from '../../../../utilities/hasPermissions';
import useHandleError from '../../../../utilities/useHandleError';
import { DealerAppointmentEmailFields, DealerAppointmentEmailInput } from './DealerAssignmentAppointmentForm';
import { getEmailContent, setEmailItem } from './shared';

export type DealerAssignmentShowroomVisitAppointmentFormProps = {
    module: VisitAppointmentModuleInDealerSpecsFragment;
    dealer: DealerWithPermissionsFragmentFragment;
};

type FormValues = {
    customer: {
        [key in keyof VisitAppointmentModuleEmailContentCustomerInput]: DealerAppointmentEmailInput;
    };
    salesPerson: {
        submitConfirmation: DealerAppointmentEmailInput;
        bookingCancellation: DealerAppointmentEmailInput;
    };
};

const DealerAssignmentShowroomVisitAppointmentForm = ({
    module,
    dealer,
}: DealerAssignmentShowroomVisitAppointmentFormProps) => {
    const { t } = useTranslation(['dealershipManagement', 'appointmentModuleDetails', 'moduleDetails']);

    const [updateVisitAppointmentModuleEmailContent] = useUpdateVisitAppointmentModuleEmailContentMutation();

    // initialize finance products and dealers for the dealer
    const initialValues: FormValues = useMemo(
        () => ({
            customer: {
                submitConfirmation: getEmailContent(module.emailContents.customer.submitConfirmation, dealer.id),
                bookingAmendment: getEmailContent(module.emailContents.customer.bookingAmendment, dealer.id),
                bookingConfirmation: getEmailContent(module.emailContents.customer.bookingConfirmation, dealer.id),
                bookingComplete: getEmailContent(module.emailContents.customer.bookingComplete, dealer.id),
                bookingCancellation: getEmailContent(module.emailContents.customer.bookingCancellation, dealer.id),
            },
            salesPerson: {
                submitConfirmation: {
                    ...getEmailContent(module.emailContents.salesPerson.submitConfirmation, dealer.id),
                },
                bookingCancellation: getEmailContent(module.emailContents.salesPerson.bookingCancellation, dealer.id),
            },
        }),
        [dealer.id, module.emailContents]
    );

    const onSubmit = useHandleError<FormValues>(
        async values => {
            const { customer, salesPerson } = values;

            // submitting message
            message.loading({
                content: t('dealershipManagement:messages.updateSubmitting'),
                key: 'primary',
                duration: 0,
            });

            // TO DO VF-1474: Make sure the functionality with booking cancellation is working fine
            await updateVisitAppointmentModuleEmailContent({
                variables: {
                    settings: {
                        emailContentUpdateType: EmailContentUpdateType.Dealer,
                        dealerId: dealer.id,
                        customer: {
                            submitConfirmation: setEmailItem(
                                dealer.id,
                                customer.submitConfirmation,
                                omit(['__typename', 'introImage'], module.emailContents.customer.submitConfirmation)
                            ),
                            bookingAmendment: setEmailItem(
                                dealer.id,
                                customer.bookingAmendment,
                                omit(['__typename', 'introImage'], module.emailContents.customer.bookingAmendment)
                            ),
                            bookingConfirmation: setEmailItem(
                                dealer.id,
                                customer.bookingConfirmation,
                                omit(['__typename', 'introImage'], module.emailContents.customer.bookingConfirmation)
                            ),
                            bookingComplete: setEmailItem(
                                dealer.id,
                                customer.bookingComplete,
                                omit(['__typename', 'introImage'], module.emailContents.customer.bookingComplete)
                            ),
                            bookingCancellation: setEmailItem(
                                dealer.id,
                                customer.bookingCancellation,
                                omit(['__typename', 'introImage'], module.emailContents.customer.bookingCancellation)
                            ),
                        },
                        salesPerson: {
                            submitConfirmation: setEmailItem(
                                dealer.id,
                                salesPerson.submitConfirmation,
                                omit(['__typename', 'introImage'], module.emailContents.salesPerson.submitConfirmation)
                            ),
                            bookingCancellation: setEmailItem(
                                dealer.id,
                                salesPerson.bookingCancellation,
                                omit(['__typename', 'introImage'], module.emailContents.salesPerson.bookingCancellation)
                            ),
                        },
                    },
                    moduleId: module.id,
                },
            });

            // show successful message
            message.success({
                content: t('dealershipManagement:messages.updateSuccessful'),
                key: 'primary',
            });
        },
        [
            t,
            updateVisitAppointmentModuleEmailContent,
            dealer.id,
            module.emailContents.customer.submitConfirmation,
            module.emailContents.customer.bookingAmendment,
            module.emailContents.customer.bookingConfirmation,
            module.emailContents.customer.bookingComplete,
            module.emailContents.customer.bookingCancellation,
            module.emailContents.salesPerson.submitConfirmation,
            module.emailContents.salesPerson.bookingCancellation,
            module.id,
        ]
    );

    const formId = `dealerForm-${module.id}`;
    const hasUpdatePermission = hasPermissions(dealer.permissions, [permissionKind.updateDealer]);

    return (
        <Formik<FormValues> initialValues={initialValues} onSubmit={onSubmit} enableReinitialize>
            {({ handleSubmit }) => (
                <Form id={formId} name={formId} onSubmitCapture={handleSubmit}>
                    <CollapsibleWrapper defaultActiveKey={['emailContent']}>
                        <Panel
                            key="emailContent"
                            className="added-bottom-padding"
                            header={t('appointmentModuleDetails:tabs.email.title')}
                        >
                            <CollapsibleWrapper
                                defaultActiveKey={[
                                    'emailContentsCustomerSubmitConfirmation',
                                    'emailContentsCustomerBookingConfirmation',
                                    'emailContentsCustomerBookingAmendment',
                                    'emailContentsCustomerBookingComplete',
                                    'emailContentsSalesPersonSubmitConfirmation',
                                ]}
                            >
                                <Panel
                                    key="emailContentsCustomerSubmitConfirmation"
                                    header={t('appointmentModuleDetails:sections.emails.customer.submitConfirmation')}
                                >
                                    <DealerAppointmentEmailFields
                                        disabled={!hasUpdatePermission}
                                        path="customer.submitConfirmation"
                                    />
                                </Panel>
                                <Panel
                                    key="emailContentsCustomerBookingConfirmation"
                                    header={t('appointmentModuleDetails:sections.emails.customer.bookingConfirmation')}
                                >
                                    <DealerAppointmentEmailFields
                                        disabled={!hasUpdatePermission}
                                        path="customer.bookingConfirmation"
                                    />
                                </Panel>
                                <Panel
                                    key="emailContentsCustomerBookingAmendment"
                                    header={t('appointmentModuleDetails:sections.emails.customer.bookingAmendment')}
                                >
                                    <DealerAppointmentEmailFields
                                        disabled={!hasUpdatePermission}
                                        path="customer.bookingAmendment"
                                    />
                                </Panel>
                                <Panel
                                    key="emailContentsCustomerBookingComplete"
                                    header={t('appointmentModuleDetails:sections.emails.customer.bookingComplete')}
                                >
                                    <DealerAppointmentEmailFields
                                        disabled={!hasUpdatePermission}
                                        path="customer.bookingComplete"
                                    />
                                </Panel>
                                <Panel
                                    key="emailContentsSalesPersonSubmitConfirmation"
                                    header={t(
                                        'appointmentModuleDetails:sections.emails.salesPerson.submitConfirmation'
                                    )}
                                >
                                    <DealerAppointmentEmailFields
                                        disabled={!hasUpdatePermission}
                                        path="salesPerson.submitConfirmation"
                                    />
                                </Panel>
                            </CollapsibleWrapper>
                        </Panel>
                    </CollapsibleWrapper>
                </Form>
            )}
        </Formik>
    );
};

export default DealerAssignmentShowroomVisitAppointmentForm;
