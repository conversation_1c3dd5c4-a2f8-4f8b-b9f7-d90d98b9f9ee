import { ExclamationCircleOutlined } from '@ant-design/icons';
import { useApolloClient } from '@apollo/client';
import { Button, message, Modal } from 'antd';
import { Formik } from 'formik';
import { pick } from 'lodash/fp';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import getApolloErrors from '../../../../server/utils/getApolloErrors';
import * as permissionKind from '../../../../shared/permissions';
import type { CampaignDataFragment } from '../../../api/fragments/CampaignData';
import {
    DeleteCampaignDocument,
    type DeleteCampaignMutation,
    type DeleteCampaignMutationVariables,
} from '../../../api/mutations/deleteCampaign';
import {
    UpdateCampaignDocument,
    type UpdateCampaignMutation,
    type UpdateCampaignMutationVariables,
} from '../../../api/mutations/updateCampaign';
import FormAutoTouch from '../../../components/FormAutoTouch';
import { useCompany } from '../../../components/contexts/CompanyContextManager';
import Form from '../../../components/fields/Form';
import ConsolePageWithHeader from '../../../layouts/ConsoleLayout/ConsolePageWithHeader';
import hasPermissions from '../../../utilities/hasPermissions';
import useHandleError from '../../../utilities/useHandleError';
import CampaignForm from './CampaignForm';
import type { CampaignFormValues } from './shared/types';
import useCampaignFormValidator from './shared/useCampaignFormValidator';

type CampaignDetailsInnerPageProps = {
    campaign: CampaignDataFragment;
};

const CampaignDetailsInnerPage = ({ campaign }: CampaignDetailsInnerPageProps) => {
    const { t } = useTranslation('campaignDetails');
    const navigate = useNavigate();
    const company = useCompany(true);
    const apolloClient = useApolloClient();
    const validate = useCampaignFormValidator();

    const hasManagePermission = company ? hasPermissions(company.permissions, [permissionKind.manageCampaigns]) : false;

    const initialValues: CampaignFormValues = useMemo(
        () => ({
            ...pick(['description', 'isActive', 'campaignId'], campaign),
        }),
        [campaign]
    );

    const onSubmit = useHandleError<CampaignFormValues>(
        async values => {
            message.loading({
                content: t('campaignDetails:messages.updateSubmitting'),
                key: 'primary',
                duration: 0,
            });

            await apolloClient.mutate<UpdateCampaignMutation, UpdateCampaignMutationVariables>({
                mutation: UpdateCampaignDocument,
                variables: {
                    id: campaign.id,
                    settings: values,
                },
            });

            message.success({
                content: t('campaignDetails:messages.updateSuccessful'),
                key: 'primary',
            });
        },
        [apolloClient, campaign.id, t]
    );

    const onDeleteCampaign = useCallback(async () => {
        Modal.confirm({
            className: 'static-modal',
            title: t('campaignDetails:deleteModal.title'),
            icon: <ExclamationCircleOutlined />,
            content: t('campaignDetails:deleteModal.content'),
            okText: t('campaignDetails:deleteModal.okText'),
            okType: 'danger',
            cancelText: t('campaignDetails:deleteModal.cancelText'),
            async onOk() {
                try {
                    message.loading({
                        content: t('campaignDetails:messages.deleteSubmitting'),
                        key: 'primary',
                        duration: 0,
                    });

                    await apolloClient.mutate<DeleteCampaignMutation, DeleteCampaignMutationVariables>({
                        mutation: DeleteCampaignDocument,
                        variables: {
                            id: campaign.id,
                        },
                    });

                    message.success({
                        content: t('campaignDetails:messages.deleteSuccessful'),
                        key: 'primary',
                    });

                    navigate('/admin/campaigns');
                } catch (error) {
                    const apolloErrors = getApolloErrors(error);

                    if (apolloErrors?.$root) {
                        message.error(apolloErrors?.$root);
                    }
                }
            },
        });
    }, [apolloClient, campaign.id, navigate, t]);

    return (
        <Formik initialValues={initialValues} onSubmit={onSubmit} validate={validate}>
            {({ handleSubmit }) => (
                <ConsolePageWithHeader
                    extra={
                        hasManagePermission && (
                            <Button onClick={onDeleteCampaign} danger>
                                {t('campaignDetails:actions.delete')}
                            </Button>
                        )
                    }
                    footer={[
                        hasManagePermission && (
                            <Button key="campaignUpdate" form="campaignUpdate" htmlType="submit" type="primary">
                                {t('campaignDetails:actions.update')}
                            </Button>
                        ),
                    ]}
                    onBack={() => navigate('/admin/campaigns')}
                    title={t('campaignDetails:title')}
                >
                    <Form id="campaignUpdate" name="campaignUpdate" onSubmitCapture={handleSubmit}>
                        <FormAutoTouch />
                        <CampaignForm disabled={!hasManagePermission} />
                    </Form>
                </ConsolePageWithHeader>
            )}
        </Formik>
    );
};

export default CampaignDetailsInnerPage;
