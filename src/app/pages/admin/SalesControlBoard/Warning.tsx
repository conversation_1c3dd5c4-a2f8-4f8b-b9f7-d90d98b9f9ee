import { PInlineNotification } from '@porsche-design-system/components-react';
import dayjs from 'dayjs';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useSalesControlBoardContext } from './SalesControlBoardManager';

const Warning = () => {
    const { t } = useTranslation('salesControlBoard');
    const { monthOfImportOptions, state } = useSalesControlBoardContext();
    // todo: Replace with actual logic to determine if there is no data
    const noDataType = [];

    const info = useMemo(() => {
        if (monthOfImportOptions.length === 0) {
            return t('salesControlBoard:noDataWarning');
        }

        return t('salesControlBoard:noDataWarningOnMonth', {
            types: noDataType.map(i => `‘${i}’`).join(' and '),
            month: dayjs(state.filters.monthOfImport).format('MMM YYYY'),
        });
    }, [monthOfImportOptions.length, state.filters.monthOfImport, t, noDataType]);

    if (noDataType.length === 0 && monthOfImportOptions.length > 0) {
        return null;
    }

    return <PInlineNotification dismissButton={false} heading={info} headingTag="h2" state="warning" />;
};

export default Warning;
