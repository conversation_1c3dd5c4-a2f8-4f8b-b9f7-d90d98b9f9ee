import { getMediaQueryMax, getMediaQueryMin } from '@porsche-design-system/components-react/styles';
import styled, { css } from 'styled-components';
import RadioGroupField from '../../../components/fields/RadioGroupField';

export const Card = styled.div`
    padding: 16px;
    border-radius: 12px;
    background: white;
`;

export const HeaderWrapper = styled.div({
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    [getMediaQueryMax('s')]: {
        flexDirection: 'column',
        gap: '16px',
        alignItems: 'flex-start',
    },
});

export const ActionWrapper = styled.div`
    display: flex;
    gap: 8px;
`;

export const ContentWrapper = styled.div<{ isDesktop: boolean }>`
    display: flex;
    width: 100%;

    & > .ant-space {
        width: 100%;
        padding: 24px;
        ${({ isDesktop }) =>
            isDesktop &&
            css`
                width: calc(100% - 368px + 16px); // plus 16px to overwrite the ant-row margin left and right
            `},
    }
`;

export const StyledFilterSideMenu = styled.div`
    width: 368px;
    padding: 24px;
    border-left: 1px solid #d8d8db;
    background-color: #f5f5f7;
`;

export const StyledRadioGroupField = styled(RadioGroupField)`
    & {
        color: red;
    }
    &.ant-form-item-label label {
        font-size: 16px;
    }
    &.ant-radio-group {
        display: flex;
        gap: 8px;

        & label.ant-radio-button-wrapper {
            width: 200px;
            font-size: 16px;
            border-radius: 4px;
            height: 54px;
            border: 2px solid #d9d9d9;
            box-shadow: none;
            display: flex;
            justify-content: center;
            align-items: center;
            background: transparent;

            &:before {
                border-radius: 4px;
            }

            &.ant-radio-button-wrapper-checked {
                border: 2px solid #010205;

                &:hover {
                    border: 2px solid #a3a3a3;
                }
            }
        }
    }
`;

export const Badge = styled.div<{ color: string; bgColor: string }>`
    display: inline-block;
    padding: 2px 4px;
    border-radius: 4px;
    background-color: ${props => props.bgColor};
    color: ${props => props.color};
    font-size: 12px;
`;

export const ProgressWrapper = styled.div`
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: flex-end;
`;

export const OverviewWrapper = styled.div({
    display: 'grid',
    gap: '24px',
    [getMediaQueryMin('s')]: {
        gridTemplateColumns: 'repeat(3, minmax(0, 1fr))',
    },
    [getMediaQueryMin('m')]: {
        gridTemplateColumns: 'repeat(3, minmax(0, 1fr))',
    },
    [getMediaQueryMin('l')]: {
        gridTemplateColumns: 'repeat(5, minmax(0, 1fr))',
    },
    [getMediaQueryMin('xl')]: {
        gridTemplateColumns: 'repeat(5, minmax(0, 1fr))',
    },
    [getMediaQueryMin('xxl')]: {
        gridTemplateColumns: 'repeat(5, minmax(0, 1fr))',
    },
});

export const OverviewBlock = styled.div<{ color: string }>`
    background-color: ${props => props.color};
    border-radius: 12px;
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 8px;
`;
