import { createContext, useContext, useMemo, useReducer, type ReactNode } from 'react';
import { GetSalesControlBoardQuery, useGetSalesControlBoardQuery } from '../../../../api/queries/getSalesControlBoard';
import LoadingElement from '../../../../components/LoadingElement';
import { useMultipleDealerIds } from '../../../../components/contexts/DealerContextManager';
import useOptions from './useOptions';
import {
    type SalesControlBoardState,
    type SalesControlBoardAction,
    initialSalesControlBoardState,
    reducer,
    ReportingViewType,
} from './useSalesControlBoardReducer';

type SalesControlBoardContextData = {
    state: SalesControlBoardState;
    dispatch: React.Dispatch<SalesControlBoardAction>;
    refetchMonthOfImport: Function;
    refetchSalesControlBoard: Function;
    defaultFilters: SalesControlBoardState['filters'];
    monthOfImportOptions: { label: string; value: string }[];
    salesConsultantOptions?: { label: string; value: string }[];
    vehicleModelOptions?: { label: string; value: string }[];
    data: GetSalesControlBoardQuery['salesControlBoard'] | undefined;
};

const SalesControlBoardContext = createContext<SalesControlBoardContextData | null>(null);

const SalesControlBoardManager = ({ children }: { children: JSX.Element | ReactNode }) => {
    const [state, dispatch] = useReducer(reducer, initialSalesControlBoardState);

    const { dealerIds } = useMultipleDealerIds();

    const {
        data: salesControlBoardData,
        loading: salesControlBoardLoading,
        refetch: refetchSalesControlBoard,
    } = useGetSalesControlBoardQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            filter: {
                dealerId: dealerIds[0],
                monthOfImport: state.filters.monthOfImport,
                isYtd: state.filters.reportingView === ReportingViewType.YTD,
                salesConsultant: state.filters.salesConsultantIds,
                vehicleModel: state.filters.vehicleModelIds,
            },
        },
        skip: !dealerIds.length || dealerIds.length > 1 || !state.filters.monthOfImport,
    });

    const {
        monthOfImportOptions,
        salesConsultantOptions,
        vehicleModelOptions,
        monthOfImportLoading,
        refetchMonthOfImport,
        filterOptionsDataLoading,
    } = useOptions(state, dispatch);

    const defaultFilters = useMemo(
        () => ({
            monthOfImport: monthOfImportOptions?.[0]?.value,
            reportingView: ReportingViewType.Month,
            salesConsultantIds: [],
            vehicleModelIds: [],
        }),
        [monthOfImportOptions]
    );

    const context = useMemo(
        (): SalesControlBoardContextData => ({
            state,
            dispatch,
            monthOfImportOptions,
            salesConsultantOptions,
            vehicleModelOptions,
            data: salesControlBoardData?.salesControlBoard,
            defaultFilters,
            refetchMonthOfImport,
            refetchSalesControlBoard,
        }),
        [
            state,
            salesControlBoardData,
            monthOfImportOptions,
            salesConsultantOptions,
            vehicleModelOptions,
            defaultFilters,
            refetchMonthOfImport,
            refetchSalesControlBoard,
        ]
    );

    if (salesControlBoardLoading || monthOfImportLoading || filterOptionsDataLoading) {
        return <LoadingElement />;
    }

    return <SalesControlBoardContext.Provider value={context}>{children}</SalesControlBoardContext.Provider>;
};

export default SalesControlBoardManager;

export const useSalesControlBoardContext = () => {
    const context = useContext(SalesControlBoardContext);

    if (!context) {
        throw new Error('Sales Control Board context missing');
    }

    return context;
};
