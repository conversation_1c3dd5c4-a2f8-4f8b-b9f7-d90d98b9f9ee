import * as echarts from 'echarts';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { SizeMe } from 'react-sizeme';
import styled from 'styled-components';

const Container = styled.div`
    width: 100%;
    height: 100%;
    min-height: 284px;
    max-height: 500px;
`;

type ChartData = {
    leadsCreated: number;
    testDrives: number;
    salesOffers: number;
    orderIntakes: number;
    retails: number;
};
type InnerProps = { data: ChartData; size: { width: number; height: number } };

const Inner = ({ size, data }: InnerProps) => {
    const { t } = useTranslation('salesControlBoard');

    const element = useRef<HTMLElement | null>(null);
    const [mounted, setMounted] = useState(false);
    const [chart, setChart] = useState<echarts.ECharts | null>(null);

    useEffect(() => {
        if (mounted) {
            setChart(lastChart => {
                lastChart?.dispose();

                if (size.width > 0 && size.height > 0) {
                    return echarts.init(element.current, null, { width: size.width, height: size.height });
                }

                return null;
            });
        }
    }, [mounted, size]);

    const categories = useMemo(
        () => [
            t('salesControlBoard:performanceOverview.leadsCreated'),
            t('salesControlBoard:performanceOverview.testDrives'),
            t('salesControlBoard:performanceOverview.salesOffers'),
            t('salesControlBoard:performanceOverview.orderIntakes'),
            t('salesControlBoard:performanceOverview.retails'),
        ],
        [t]
    );

    const values = useMemo(
        () => [data.leadsCreated, data.testDrives, data.salesOffers, data.orderIntakes, data.retails],
        [data.leadsCreated, data.orderIntakes, data.retails, data.salesOffers, data.testDrives]
    );

    const option = useMemo(
        () => ({
            series: [
                {
                    data: values,
                    type: 'bar',
                    itemStyle: {
                        color(params) {
                            const colorList = ['#5885E9', '#9B80F1', '#FEBD38', '#E377AC', '#71B375'];

                            return colorList[params.dataIndex];
                        },
                        borderRadius: [4, 4, 4, 4],
                    },

                    barWidth: '40%',
                    label: {
                        show: true,
                        position: 'top',
                        color: 'black',
                        fontSize: 14,
                    },
                },
            ],
            xAxis: {
                type: 'category',
                data: categories,
                axisLabel: {
                    show: true,
                },
                axisLine: {
                    show: false,
                },
                axisTick: {
                    show: false,
                },
            },
            yAxis: {
                type: 'value',
                axisLabel: {
                    show: false,
                },
                axisLine: {
                    show: false,
                },
                axisTick: {
                    show: false,
                },
                splitLine: {
                    show: false,
                },
            },
            grid: {
                right: 0,
                bottom: 16,
                left: 0,
                containLabel: true,
            },
        }),
        [categories, values]
    );

    useEffect(() => {
        chart?.setOption(option, true);
    }, [option, chart]);

    // dispose the chart when umount
    useEffect(() => {
        setMounted(true);

        return () => {
            chart?.dispose();
        };
    }, []);

    return (
        <Container>
            <div
                ref={ref => {
                    element.current = ref;
                }}
            />
        </Container>
    );
};

type ChartContentProps = {
    data: ChartData;
};
const ChartContent = ({ data }: ChartContentProps) => (
    <SizeMe refreshRate={1000} monitorHeight>
        {({ size }) => <Inner data={data} size={size} />}
    </SizeMe>
);

export default ChartContent;
