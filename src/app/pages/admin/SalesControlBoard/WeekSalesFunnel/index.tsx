import { PHeading } from '@porsche-design-system/components-react';
import { Space, Row, Col } from 'antd';
import dayjs from 'dayjs';
import { pick } from 'lodash/fp';
import { useTranslation } from 'react-i18next';
import { useSalesControlBoardContext } from '../SalesControlBoardManager';
import { Card } from '../ui';
import usePermission from '../usePermission';
import Chart from './Chart';
import Table from './Table';

const WeekSalesFunnel = () => {
    const { t } = useTranslation('salesControlBoard');
    const { hasSalesManagerPermission } = usePermission();

    const { data, monthOfImportOptions } = useSalesControlBoardContext();

    if (!hasSalesManagerPermission || monthOfImportOptions.length === 0) {
        return null;
    }

    return (
        <Space direction="vertical" size={36} style={{ width: '100%' }}>
            {(data?.weekFunnels || []).map((week, index) => (
                // eslint-disable-next-line react/no-array-index-key
                <Card key={index.toString()}>
                    <PHeading size="large" tag="h2">
                        {t('salesControlBoard:weekSalesFunnel.title', {
                            count: index + 1,
                            dateRange: dayjs(week.start).isSame(dayjs(week.end), 'day')
                                ? dayjs(week.start).format('D MMMM YYYY')
                                : `${dayjs(week.start).format('D')} - ${dayjs(week.end).format('D MMMM YYYY')}`,
                        })}
                    </PHeading>
                    <Row gutter={24}>
                        <Col lg={12} md={24} sm={24} xs={24}>
                            <Chart
                                data={pick(
                                    ['leadsCreated', 'testDrives', 'salesOffers', 'orderIntakes', 'retails'],
                                    week
                                )}
                            />
                        </Col>
                        <Col lg={12} md={24} sm={24} xs={24}>
                            <Table
                                data={pick(
                                    [
                                        'leadToTestDriveRate',
                                        'testDriveToSalesOfferRate',
                                        'salesOfferToOrderIntakeRate',
                                        'orderIntakeToRetailRate',
                                    ],
                                    week
                                )}
                            />
                        </Col>
                    </Row>
                </Card>
            ))}
        </Space>
    );
};

export default WeekSalesFunnel;
