import { <PERSON>Icon, PText } from '@porsche-design-system/components-react';
import { Col, Row } from 'antd';
import dayjs from 'dayjs';
import { Formik } from 'formik';
import { isNil } from 'lodash/fp';
import { Dispatch, SetStateAction, useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { useImportSalesControlBoardDataMutation } from '../../../../api';
import { SalesControlBoardDataType } from '../../../../api/types';
import { useSingleDealerId } from '../../../../components/contexts/DealerContextManager';
import Form from '../../../../components/fields/Form';
import { useThemeComponents } from '../../../../themes/hooks';
import Button from '../../../../themes/porscheV3/Button';
import DatePickerField from '../../../../themes/porscheV3/Fields/DatePickerField';
import SelectField from '../../../../themes/porscheV3/Fields/SelectField';
import Modal from '../../../../themes/porscheV3/Modal';
import { allowedExtensions } from '../../../../utilities/extensions';
import useHandleError from '../../../../utilities/useHandleError';
import useSystemOptions from '../../../../utilities/useSystemOptions';
import { useSalesControlBoardContext } from '../SalesControlBoardManager';

const StyledDisplayField = styled.div`
    padding-top: 16px;
    padding-left: 16px;
    text-align: left;
    border-style: dashed;
    height: 54px;
    border-color: #d8d8db;
    border-radius: 4px;
`;

const StyledSpan = styled.span`
    padding-left: 15px;
`;
type ImportModalProps = {
    open: boolean;
    setOpen: Dispatch<SetStateAction<boolean>>;
};

type FormValues = {
    dataType: SalesControlBoardDataType;
    reportingMonth: Date | null;
    file: File[] | null;
};

const ImportModal = ({ open, setOpen }: ImportModalProps) => {
    const {
        FormFields: { MultipleDraggerField },
        notification,
    } = useThemeComponents();
    const { refetchMonthOfImport, refetchSalesControlBoard } = useSalesControlBoardContext();

    const [isSubmitting, setIsSubmitting] = useState(false);

    const { t } = useTranslation('salesControlBoard');
    const [mutation] = useImportSalesControlBoardDataMutation();

    const [errorRows, setErrorRows] = useState<{
        isAPICalled: boolean;
        errors: string[];
        fileName?: string;
    }>({
        isAPICalled: false,
        errors: [],
        fileName: '',
    });
    const handleClose = useCallback(() => {
        setOpen(false);
        setErrorRows({
            isAPICalled: false,
            errors: [],
        });
        setIsSubmitting(false);
    }, [setOpen]);

    const { dealerId } = useSingleDealerId();

    const { salesControlBoardDataTypes } = useSystemOptions();

    const initialValues: FormValues = useMemo(
        () => ({
            dataType: null,
            reportingMonth: dayjs().toDate(),
            file: null,
        }),
        []
    );

    const footerButton = useMemo(() => {
        if (errorRows.isAPICalled && errorRows.errors.length) {
            return [
                <Button key="cancel" onClick={handleClose} type="primary" block>
                    {t('salesControlBoard:modal.buttons.understood')}
                </Button>,
            ];
        }

        return [
            <Button
                key="submit"
                disabled={isSubmitting}
                form="importModal"
                htmlType="submit"
                loading={isSubmitting}
                type="primary"
                block
            >
                {t('salesControlBoard:modal.buttons.submit')}
            </Button>,
            <Button key="cancel" disabled={isSubmitting} onClick={handleClose} type="tertiary" block>
                {t('salesControlBoard:modal.buttons.cancel')}
            </Button>,
        ];
    }, [errorRows.errors.length, errorRows.isAPICalled, handleClose, isSubmitting, t]);

    const onSubmit = useHandleError<FormValues>(
        async (values, helpers) => {
            try {
                if (values.file[0] instanceof File) {
                    setIsSubmitting(true);
                    notification.loading({
                        content: t('salesControlBoard:messages.importing'),
                        duration: 0,
                        key: 'primary',
                    });
                    const { data } = await mutation({
                        variables: {
                            dataType: values.dataType,
                            file: values.file[0],
                            reportingPeriod: dayjs(values.reportingMonth).format('YYYY-MM'),
                            dealerId,
                        },
                    });

                    if (data.importSalesControlBoardData.length) {
                        setErrorRows({
                            isAPICalled: true,
                            errors: data.importSalesControlBoardData,
                            fileName: values.file[0].name,
                        });
                        notification.destroy('primary');
                    } else {
                        helpers.resetForm();

                        handleClose();

                        notification.destroy('primary');

                        notification.success({
                            content: t('salesControlBoard:messages.importSuccess'),
                            key: 'primary',
                        });
                    }

                    refetchMonthOfImport();
                    refetchSalesControlBoard();
                }
            } catch (err) {
                console.error(err);
                notification.destroy('primary');
            }
        },
        [dealerId, handleClose, mutation, notification, t, refetchMonthOfImport, refetchSalesControlBoard]
    );

    const renderModal = useMemo(() => {
        if (errorRows.isAPICalled && errorRows.errors.length) {
            return (
                <>
                    <PText size="x-large" style={{ paddingBottom: '8px' }} weight="semibold">
                        {t('salesControlBoard:modal.error.title')}
                    </PText>
                    <PText>{t('salesControlBoard:modal.error.description', { fileName: errorRows.fileName })}</PText>
                    <PText style={{ paddingBottom: '24px' }}>{t('salesControlBoard:modal.error.subDescription')}</PText>
                    {errorRows.errors.map((error, index) => (
                        <PText>{error}</PText>
                    ))}
                </>
            );
        }

        return (
            <Formik<FormValues> initialValues={initialValues} onSubmit={onSubmit} validateOnMount>
                {({ handleSubmit, values, setFieldValue }) => (
                    <Form id="importModal" name="importModal" onSubmitCapture={handleSubmit}>
                        <PText size="x-large" style={{ paddingBottom: '10px' }} weight="semibold">
                            {t('salesControlBoard:modal.title')}
                        </PText>
                        <PText style={{ paddingBottom: '24px' }}>{t('salesControlBoard:modal.description')}</PText>
                        <DatePickerField
                            defaultValue={dayjs().startOf('month')}
                            format="MMM YYYY"
                            label={t('salesControlBoard:modal.fields.reportingMonth.label')}
                            name="reportingMonth"
                            picker="month"
                            required
                        />

                        <SelectField
                            label={t('salesControlBoard:modal.fields.dataType.label')}
                            name="dataType"
                            options={salesControlBoardDataTypes}
                            placeholder={t('common:pleaseSelect')}
                            required
                        />
                        <MultipleDraggerField
                            extensions={allowedExtensions.excel}
                            maxCount={1}
                            maxFiles={1}
                            name="file"
                            showUploadList={false}
                            style={{
                                width: '100%',
                                backgroundColor: 'transparent',
                                border: 'transparent',
                                padding: '0px',
                            }}
                            required
                        >
                            <Row gutter={[16, 16]} style={{ padding: '0px;' }}>
                                <Col span={18}>
                                    <StyledDisplayField>
                                        <Row>
                                            <div>
                                                {isNil(values?.file)
                                                    ? t('salesControlBoard:modal.file.placeholder')
                                                    : values.file[0].name}
                                            </div>
                                            {!isNil(values?.file) && (
                                                <div>
                                                    <PIcon
                                                        aria={{ 'aria-label': 'Close icon' }}
                                                        name="close"
                                                        onClick={event => {
                                                            event.preventDefault();
                                                            event.stopPropagation();
                                                            setFieldValue('file', undefined);
                                                        }}
                                                        style={{
                                                            position: 'absolute',
                                                            right: '20px',
                                                            cursor: 'pointer',
                                                            top: '16px',
                                                        }}
                                                    />
                                                </div>
                                            )}
                                        </Row>
                                    </StyledDisplayField>
                                </Col>
                                <Col span={6}>
                                    <Button style={{ width: '100%' }} type="primary">
                                        {t('salesControlBoard:modal.file.button')}
                                    </Button>
                                </Col>
                                <StyledSpan>{t('salesControlBoard:modal.file.allowExtension')}</StyledSpan>
                            </Row>
                        </MultipleDraggerField>
                    </Form>
                )}
            </Formik>
        );
    }, [
        MultipleDraggerField,
        errorRows.errors,
        errorRows.fileName,
        errorRows.isAPICalled,
        initialValues,
        onSubmit,
        salesControlBoardDataTypes,
        t,
    ]);

    return (
        <Modal
            className="salesControlBoard-importModal"
            closable={false}
            footer={footerButton}
            onCancel={() => setOpen(false)}
            open={open}
            title=""
            width={800}
            centered
            destroyOnClose
        >
            {renderModal}
        </Modal>
    );
};

export default ImportModal;
