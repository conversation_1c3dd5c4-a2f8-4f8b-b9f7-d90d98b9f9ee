import { PButton } from '@porsche-design-system/components-react';
import { useTranslation } from 'react-i18next';
import usePermission from '../usePermission';
import useImportModal from './useImportModal';

const ImportAction = () => {
    const { t } = useTranslation('salesControlBoard');
    const importModal = useImportModal();
    const { hasSalesManagerPermission } = usePermission();

    if (!hasSalesManagerPermission) {
        return null;
    }

    return (
        <>
            <PButton onClick={() => importModal.open()} type="button" variant="secondary">
                {t('salesControlBoard:actions.import')}
            </PButton>
            {importModal.render()}
        </>
    );
};

export default ImportAction;
