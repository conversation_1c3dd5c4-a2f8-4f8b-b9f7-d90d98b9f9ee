import { useTranslation } from 'react-i18next';
import usePermission from '../usePermission';

const Overview = () => {
    const { t } = useTranslation('salesControlBoard');
    const { hasSalesManagerPermission, hasSalesConsultantPermission } = usePermission();

    return (
        <div style={!hasSalesManagerPermission && hasSalesConsultantPermission ? { marginTop: '24px' } : {}}>
            Overview
        </div>
    );
};

export default Overview;
