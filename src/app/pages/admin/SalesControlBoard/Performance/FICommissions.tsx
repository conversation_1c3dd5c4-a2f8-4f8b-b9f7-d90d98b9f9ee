import {
    PTable,
    PTableBody,
    PTableCell,
    PTableHead,
    PTableHeadCell,
    PTableHeadRow,
    PTableRow,
    PText,
} from '@porsche-design-system/components-react';
import { get } from 'lodash/fp';
import { useCallback, useEffect, useMemo, useReducer } from 'react';
import { useTranslation } from 'react-i18next';
import { useSalesControlBoardContext } from '../SalesControlBoardManager';
import usePermission from '../usePermission';
import Pagination from './Pagination';
import Sorter, { type Column, type SortOrder, type SortValue, FICommissionColumnKey } from './Sorter';
import { sourceReducer, initialSourceState } from './useSourceReducer';

const getStyles = index => {
    if (index === 5) {
        return { borderLeft: '1px solid gray', minWidth: '100px', maxWidth: '150px' };
    }

    if ([1, 2, 3, 4, 6, 7, 8].includes(index)) {
        return { minWidth: '100px', maxWidth: '150px' };
    }

    return {};
};

const useHeaders = (sortValue: SortValue) => {
    const { t } = useTranslation('salesControlBoard');
    const firstHeaders = [
        t('salesControlBoard:performance.fiCommissions.consultant'),
        t('salesControlBoard:performance.fiCommissions.inHouseFinance'),
        '',
        '',
        '',
        t('salesControlBoard:performance.fiCommissions.inHouseInsurance'),
        '',
        '',
        '',
    ];

    const columns: Column[] = useMemo(
        () => [
            { label: '', key: FICommissionColumnKey.SalesConsultantName },
            {
                label: t('salesControlBoard:performance.fiCommissions.target'),
                key: FICommissionColumnKey.InHouseFinanceTarget,
            },
            {
                label: t('salesControlBoard:performance.fiCommissions.mtd'),
                key: FICommissionColumnKey.InHouseFinanceMtd,
                sorter: true,
                sortOrder:
                    sortValue.columnKey === FICommissionColumnKey.InHouseFinanceMtd ? sortValue.sortOrder : undefined,
            },
            {
                label: t('salesControlBoard:performance.fiCommissions.ytd'),
                key: FICommissionColumnKey.InHouseFinanceYtd,
                sorter: true,
                sortOrder:
                    sortValue.columnKey === FICommissionColumnKey.InHouseFinanceYtd ? sortValue.sortOrder : undefined,
            },
            {
                label: t('salesControlBoard:performance.fiCommissions.3MAvg'),
                key: FICommissionColumnKey.InHouseFinance3MAvg,
                sorter: true,
                sortOrder:
                    sortValue.columnKey === FICommissionColumnKey.InHouseFinance3MAvg ? sortValue.sortOrder : undefined,
            },
            {
                label: t('salesControlBoard:performance.fiCommissions.target'),
                key: FICommissionColumnKey.InHouseInsuranceTarget,
            },
            {
                label: t('salesControlBoard:performance.fiCommissions.mtd'),
                key: FICommissionColumnKey.InHouseInsuranceMtd,
                sorter: true,
                sortOrder:
                    sortValue.columnKey === FICommissionColumnKey.InHouseInsuranceMtd ? sortValue.sortOrder : undefined,
            },
            {
                label: t('salesControlBoard:performance.fiCommissions.ytd'),
                key: FICommissionColumnKey.InHouseInsuranceYtd,
                sorter: true,
                sortOrder:
                    sortValue.columnKey === FICommissionColumnKey.InHouseInsuranceYtd ? sortValue.sortOrder : undefined,
            },
            {
                label: t('salesControlBoard:performance.fiCommissions.3MAvg'),
                key: FICommissionColumnKey.InHouseInsurance3MAvg,
                sorter: true,
                sortOrder:
                    sortValue.columnKey === FICommissionColumnKey.InHouseInsurance3MAvg
                        ? sortValue.sortOrder
                        : undefined,
            },
        ],
        [sortValue.columnKey, sortValue.sortOrder, t]
    );

    return { firstHeaders, columns };
};

const FICommissions = () => {
    const { hasSalesManagerPermission } = usePermission();
    const { data } = useSalesControlBoardContext();

    const [state, dispatch] = useReducer(sourceReducer, {
        ...initialSourceState,
        dataSource: [...(data?.fiCommissions || [])].sort(
            (a, b) => get(initialSourceState.sortValue.columnKey, b) - get(initialSourceState.sortValue.columnKey, a)
        ),
    });

    useEffect(() => {
        dispatch({ type: 'setDataSource', dataSource: data?.fiCommissions || [] });
    }, [JSON.stringify(data?.fiCommissions), dispatch]);

    const { firstHeaders, columns } = useHeaders(state.sortValue);
    const onSortOrderChange = useCallback(
        (columnKey: string, currentSortOrder?: SortOrder) => {
            dispatch({ type: 'setSortValue', columnKey, currentSortOrder });
        },
        [dispatch]
    );

    if (!hasSalesManagerPermission) {
        return null;
    }

    return (
        <>
            <PTable caption="Some caption">
                <PTableHead>
                    <PTableHeadRow>
                        {firstHeaders.map((item, i) => (
                            // eslint-disable-next-line react/no-array-index-key
                            <PTableHeadCell key={i.toString()}>
                                <PText size="x-small" weight="semi-bold">
                                    {item}
                                </PText>
                            </PTableHeadCell>
                        ))}
                    </PTableHeadRow>
                    <PTableHeadRow>
                        {columns.map((column, index) => (
                            <PTableHeadCell key={column.key} style={getStyles(index)}>
                                <div
                                    style={Object.assign(
                                        column.sorter
                                            ? { display: 'flex', flexDirection: 'row', alignItems: 'center' }
                                            : {}
                                    )}
                                >
                                    <PText size="x-small" weight="semi-bold">
                                        {column.label}
                                    </PText>
                                    <Sorter column={column} index={index} onSortOrderChange={onSortOrderChange} />
                                </div>
                            </PTableHeadCell>
                        ))}
                    </PTableHeadRow>
                </PTableHead>
                <PTableBody>
                    {state.currentPageData.map(item => (
                        <PTableRow key={item.salesConsultantName}>
                            {columns.map((column, index) => (
                                <PTableCell key={column.key} style={getStyles(index)}>
                                    <PText size="x-small" weight={index === 0 ? 'semi-bold' : 'regular'}>
                                        {index > 0 ? `${item[column.key]}%` : item[column.key]}
                                    </PText>
                                </PTableCell>
                            ))}
                        </PTableRow>
                    ))}
                </PTableBody>
            </PTable>
            <Pagination dispatch={dispatch} state={state} />
        </>
    );
};

export default FICommissions;
