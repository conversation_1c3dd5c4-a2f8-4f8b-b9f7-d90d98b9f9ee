import { useCallback, Dispatch } from 'react';
import Pagination from '../../../../themes/porscheV3/Pagination';
import { SourceState, SourceAction } from './useSourceReducer';

type SPaginationProps = {
    state: SourceState;
    dispatch: Dispatch<SourceAction>;
};
const SPagination = ({ state, dispatch }: SPaginationProps) => {
    const total = state.dataSource.length;
    const onPageChange = useCallback(
        (current: number) => {
            dispatch({ type: 'setPage', page: current });
        },
        [dispatch]
    );

    if (total <= 10) {
        return null;
    }

    return (
        <Pagination
            current={state.page}
            onChange={onPageChange}
            pageSize={state.pageSize}
            total={total}
            alignPaginationCenter
        />
    );
};

export default SPagination;
