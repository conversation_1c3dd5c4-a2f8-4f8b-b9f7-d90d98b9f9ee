import { PHeading } from '@porsche-design-system/components-react';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useSalesControlBoardContext } from '../SalesControlBoardManager';
import useSelectedMonth from '../SalesControlBoardManager/useSelectedMonth';
import { Card } from '../ui';
import usePermission from '../usePermission';
import Overview from './Overview';
import SectionTabs from './SectionTabs';

const Performance = () => {
    const { t } = useTranslation('salesControlBoard');
    const { hasSalesManagerPermission, hasSalesConsultantPermission } = usePermission();
    const { monthOfImportOptions } = useSalesControlBoardContext();
    const selectedMonth = useSelectedMonth();

    const title = useMemo(() => {
        if (hasSalesManagerPermission) {
            return t('salesControlBoard:performance.titleForManager', {
                selectedMonth,
            });
        }

        if (hasSalesConsultantPermission) {
            return t('salesControlBoard:performance.titleForConsultant', {
                selectedMonth,
            });
        }

        return '';
    }, [t, hasSalesManagerPermission, hasSalesConsultantPermission, selectedMonth]);

    if (monthOfImportOptions.length === 0) {
        return null;
    }

    return (
        <Card>
            <PHeading size="large" tag="h2">
                {title}
            </PHeading>
            {hasSalesManagerPermission && <SectionTabs />}
            {!hasSalesManagerPermission && hasSalesConsultantPermission && <Overview />}
        </Card>
    );
};

export default Performance;
