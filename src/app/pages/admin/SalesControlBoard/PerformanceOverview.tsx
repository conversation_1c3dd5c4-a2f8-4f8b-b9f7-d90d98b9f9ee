import { PHeading } from '@porsche-design-system/components-react';
import { themeLightBackgroundSurface } from '@porsche-design-system/components-react/styles';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useSalesControlBoardContext } from './SalesControlBoardManager';
import useSelectedMonth from './SalesControlBoardManager/useSelectedMonth';
import { Card, OverviewBlock, OverviewWrapper } from './ui';
import usePermission from './usePermission';

const PerformanceOverview = () => {
    const { t } = useTranslation('salesControlBoard');
    const { hasSalesManagerPermission, hasSalesConsultantPermission } = usePermission();
    const { monthOfImportOptions, data } = useSalesControlBoardContext();
    const selectedMonth = useSelectedMonth();

    const overviewData = useMemo(
        () => [
            {
                label: t('salesControlBoard:performanceOverview.leadsCreated'),
                value: data?.performanceOverview?.leadsCreated ?? 0,
                color: themeLightBackgroundSurface,
            },
            {
                label: t('salesControlBoard:performanceOverview.testDrives'),
                value: data?.performanceOverview?.testDrives ?? 0,
                color: '#FFE2E4',
            },
            {
                label: t('salesControlBoard:performanceOverview.salesOffers'),
                value: data?.performanceOverview?.salesOffers ?? 0,
                color: '#FFF4D2',
            },
            {
                label: t('salesControlBoard:performanceOverview.orderIntakes'),
                value: data?.performanceOverview?.orderIntakes ?? 0,
                color: '#D3E1FF',
            },
            {
                label: t('salesControlBoard:performanceOverview.retails'),
                value: data?.performanceOverview?.retails ?? 0,
                color: '#FFE2D2',
            },
        ],
        [
            data?.performanceOverview?.leadsCreated,
            data?.performanceOverview?.orderIntakes,
            data?.performanceOverview?.retails,
            data?.performanceOverview?.salesOffers,
            data?.performanceOverview?.testDrives,
            t,
        ]
    );

    const title = useMemo(() => {
        if (hasSalesManagerPermission) {
            return t('salesControlBoard:performanceOverview.titleForManager', {
                selectedMonth,
            });
        }

        if (hasSalesConsultantPermission) {
            return t('salesControlBoard:performanceOverview.titleForConsultant', {
                selectedMonth,
            });
        }

        return '';
    }, [hasSalesManagerPermission, hasSalesConsultantPermission, t, selectedMonth]);

    if (monthOfImportOptions.length === 0) {
        return null;
    }

    return (
        <Card>
            <PHeading size="large" style={{ marginBottom: '8px' }} tag="h2">
                {title}
            </PHeading>
            <OverviewWrapper>
                {overviewData.map(item => (
                    <OverviewBlock key={item.label} color={item.color}>
                        <PHeading size="small">{item.label}</PHeading>
                        <PHeading size="large">{item.value}</PHeading>
                    </OverviewBlock>
                ))}
            </OverviewWrapper>
        </Card>
    );
};

export default PerformanceOverview;
