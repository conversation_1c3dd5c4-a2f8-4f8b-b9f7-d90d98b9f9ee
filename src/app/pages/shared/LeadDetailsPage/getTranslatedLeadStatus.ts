import type { TFunction } from 'i18next';
import type { LeadDataFragment } from '../../../api/fragments/LeadData';
import { LeadStatus } from '../../../api/types';
import renderMarkdown from '../../../utilities/renderMarkdown';

const getTranslatedLeadStatus = (lead: LeadDataFragment, t: TFunction) => {
    if (!lead) {
        return '';
    }

    const translatedStatus = t(`leadListPage:status.${lead.status}`);

    if (lead.status === LeadStatus.Merged) {
        const linkElement = `<a style="color: inherit; text-decoration: underline;" 
                href="${lead.mergedToLeadSuiteId}">${lead.mergedToLeadIdentifier}</a>`;

        return renderMarkdown(`${translatedStatus} ${linkElement}`, false, false);
    }

    return translatedStatus;
};

export default getTranslatedLeadStatus;
