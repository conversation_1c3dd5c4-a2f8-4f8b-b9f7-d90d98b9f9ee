fragment AppointmentModuleEmailContentSpecs on AppointmentModuleEmailContent {
    subject {
        ...DealerTranslatedStringSettingData
    }
    introTitle {
        ...DealerTranslatedStringSettingData
    }
    introImage {
        ...UploadFileWithPreviewFormData
    }
    contentText {
        ...DealerTranslatedStringSettingData
    }
    isSummaryVehicleVisible {
        ...DealerBooleanSettingData
    }
}

fragment AppointmentModuleEmailContentFinderReservationSpecs on AppointmentModuleEmailContentFinderReservation {
    subject {
        ...DealerTranslatedStringSettingData
    }
    introTitle {
        ...DealerTranslatedStringSettingData
    }
    introImage {
        ...UploadFileWithPreviewFormData
    }
    contentText {
        ...DealerTranslatedStringSettingData
    }
    listTestDriveTitle {
        ...DealerTranslatedStringSettingData
    }
    listTestDriveItem {
        ...DealerTranslatedStringSettingData
    }
    isSummaryVehicleVisible {
        ...DealerBooleanSettingData
    }
}

fragment AppointmentModuleEmailContentCustomerSpecs on AppointmentModuleEmailContentCustomer {
    submitConfirmation {
        ...AppointmentModuleEmailContentSpecs
    }
    bookingConfirmation {
        ...AppointmentModuleEmailContentSpecs
    }
    bookingCancellation {
        ...AppointmentModuleEmailContentSpecs
    }
    bookingAmendment {
        ...AppointmentModuleEmailContentSpecs
    }
    endTestDriveWithProcess {
        ...AppointmentModuleEmailContentSpecs
    }
    completeTestDriveWithoutProcess {
        ...AppointmentModuleEmailContentSpecs
    }
}

fragment AppointmentModuleEmailContentSalesPersonSpecs on AppointmentModuleEmailContentSalesPerson {
    submitConfirmation {
        ...AppointmentModuleEmailContentSpecs
    }
    bookingCancellation {
        ...AppointmentModuleEmailContentSpecs
    }
    bookingAmendment {
        ...AppointmentModuleEmailContentSpecs
    }
    finderReservation {
        ...AppointmentModuleEmailContentFinderReservationSpecs
    }
    endTestDriveReminder {
        ...AppointmentModuleEmailContentSpecs
    }
}

fragment AppointmentModuleEmailContentsSpecs on AppointmentModuleEmailContents {
    customer {
        ...AppointmentModuleEmailContentCustomerSpecs
    }

    salesPerson {
        ...AppointmentModuleEmailContentSalesPersonSpecs
    }
}
