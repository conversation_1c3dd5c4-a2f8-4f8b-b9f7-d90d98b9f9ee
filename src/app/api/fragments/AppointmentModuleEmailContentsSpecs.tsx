import type * as SchemaTypes from '../types';

import type { DealerTranslatedStringSettingDataFragment } from './DealerTranslatedStringData';
import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { UploadFileWithPreviewFormDataFragment } from './UploadFileWithPreviewFormData';
import type { DealerBooleanSettingDataFragment } from './DealerBooleanSettingData';
import { gql } from '@apollo/client';
import { DealerTranslatedStringSettingDataFragmentDoc } from './DealerTranslatedStringData';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { UploadFileWithPreviewFormDataFragmentDoc } from './UploadFileWithPreviewFormData';
import { DealerBooleanSettingDataFragmentDoc } from './DealerBooleanSettingData';
export type AppointmentModuleEmailContentSpecsFragment = (
  { __typename: 'AppointmentModuleEmailContent' }
  & { subject: (
    { __typename: 'DealerTranslatedStringSetting' }
    & DealerTranslatedStringSettingDataFragment
  ), introTitle: (
    { __typename: 'DealerTranslatedStringSetting' }
    & DealerTranslatedStringSettingDataFragment
  ), introImage?: SchemaTypes.Maybe<(
    { __typename: 'UploadedFileWithPreview' }
    & UploadFileWithPreviewFormDataFragment
  )>, contentText: (
    { __typename: 'DealerTranslatedStringSetting' }
    & DealerTranslatedStringSettingDataFragment
  ), isSummaryVehicleVisible: (
    { __typename: 'DealerBooleanSetting' }
    & DealerBooleanSettingDataFragment
  ) }
);

export type AppointmentModuleEmailContentFinderReservationSpecsFragment = (
  { __typename: 'AppointmentModuleEmailContentFinderReservation' }
  & { subject: (
    { __typename: 'DealerTranslatedStringSetting' }
    & DealerTranslatedStringSettingDataFragment
  ), introTitle: (
    { __typename: 'DealerTranslatedStringSetting' }
    & DealerTranslatedStringSettingDataFragment
  ), introImage?: SchemaTypes.Maybe<(
    { __typename: 'UploadedFileWithPreview' }
    & UploadFileWithPreviewFormDataFragment
  )>, contentText: (
    { __typename: 'DealerTranslatedStringSetting' }
    & DealerTranslatedStringSettingDataFragment
  ), listTestDriveTitle?: SchemaTypes.Maybe<(
    { __typename: 'DealerTranslatedStringSetting' }
    & DealerTranslatedStringSettingDataFragment
  )>, listTestDriveItem?: SchemaTypes.Maybe<(
    { __typename: 'DealerTranslatedStringSetting' }
    & DealerTranslatedStringSettingDataFragment
  )>, isSummaryVehicleVisible: (
    { __typename: 'DealerBooleanSetting' }
    & DealerBooleanSettingDataFragment
  ) }
);

export type AppointmentModuleEmailContentCustomerSpecsFragment = (
  { __typename: 'AppointmentModuleEmailContentCustomer' }
  & { submitConfirmation: (
    { __typename: 'AppointmentModuleEmailContent' }
    & AppointmentModuleEmailContentSpecsFragment
  ), bookingConfirmation: (
    { __typename: 'AppointmentModuleEmailContent' }
    & AppointmentModuleEmailContentSpecsFragment
  ), bookingCancellation: (
    { __typename: 'AppointmentModuleEmailContent' }
    & AppointmentModuleEmailContentSpecsFragment
  ), bookingAmendment: (
    { __typename: 'AppointmentModuleEmailContent' }
    & AppointmentModuleEmailContentSpecsFragment
  ), endTestDriveWithProcess: (
    { __typename: 'AppointmentModuleEmailContent' }
    & AppointmentModuleEmailContentSpecsFragment
  ), completeTestDriveWithoutProcess: (
    { __typename: 'AppointmentModuleEmailContent' }
    & AppointmentModuleEmailContentSpecsFragment
  ) }
);

export type AppointmentModuleEmailContentSalesPersonSpecsFragment = (
  { __typename: 'AppointmentModuleEmailContentSalesPerson' }
  & { submitConfirmation: (
    { __typename: 'AppointmentModuleEmailContent' }
    & AppointmentModuleEmailContentSpecsFragment
  ), bookingCancellation: (
    { __typename: 'AppointmentModuleEmailContent' }
    & AppointmentModuleEmailContentSpecsFragment
  ), bookingAmendment: (
    { __typename: 'AppointmentModuleEmailContent' }
    & AppointmentModuleEmailContentSpecsFragment
  ), finderReservation: (
    { __typename: 'AppointmentModuleEmailContentFinderReservation' }
    & AppointmentModuleEmailContentFinderReservationSpecsFragment
  ), endTestDriveReminder: (
    { __typename: 'AppointmentModuleEmailContent' }
    & AppointmentModuleEmailContentSpecsFragment
  ) }
);

export type AppointmentModuleEmailContentsSpecsFragment = (
  { __typename: 'AppointmentModuleEmailContents' }
  & { customer: (
    { __typename: 'AppointmentModuleEmailContentCustomer' }
    & AppointmentModuleEmailContentCustomerSpecsFragment
  ), salesPerson: (
    { __typename: 'AppointmentModuleEmailContentSalesPerson' }
    & AppointmentModuleEmailContentSalesPersonSpecsFragment
  ) }
);

export const AppointmentModuleEmailContentSpecsFragmentDoc = /*#__PURE__*/ gql`
    fragment AppointmentModuleEmailContentSpecs on AppointmentModuleEmailContent {
  subject {
    ...DealerTranslatedStringSettingData
  }
  introTitle {
    ...DealerTranslatedStringSettingData
  }
  introImage {
    ...UploadFileWithPreviewFormData
  }
  contentText {
    ...DealerTranslatedStringSettingData
  }
  isSummaryVehicleVisible {
    ...DealerBooleanSettingData
  }
}
    `;
export const AppointmentModuleEmailContentCustomerSpecsFragmentDoc = /*#__PURE__*/ gql`
    fragment AppointmentModuleEmailContentCustomerSpecs on AppointmentModuleEmailContentCustomer {
  submitConfirmation {
    ...AppointmentModuleEmailContentSpecs
  }
  bookingConfirmation {
    ...AppointmentModuleEmailContentSpecs
  }
  bookingCancellation {
    ...AppointmentModuleEmailContentSpecs
  }
  bookingAmendment {
    ...AppointmentModuleEmailContentSpecs
  }
  endTestDriveWithProcess {
    ...AppointmentModuleEmailContentSpecs
  }
  completeTestDriveWithoutProcess {
    ...AppointmentModuleEmailContentSpecs
  }
}
    `;
export const AppointmentModuleEmailContentFinderReservationSpecsFragmentDoc = /*#__PURE__*/ gql`
    fragment AppointmentModuleEmailContentFinderReservationSpecs on AppointmentModuleEmailContentFinderReservation {
  subject {
    ...DealerTranslatedStringSettingData
  }
  introTitle {
    ...DealerTranslatedStringSettingData
  }
  introImage {
    ...UploadFileWithPreviewFormData
  }
  contentText {
    ...DealerTranslatedStringSettingData
  }
  listTestDriveTitle {
    ...DealerTranslatedStringSettingData
  }
  listTestDriveItem {
    ...DealerTranslatedStringSettingData
  }
  isSummaryVehicleVisible {
    ...DealerBooleanSettingData
  }
}
    `;
export const AppointmentModuleEmailContentSalesPersonSpecsFragmentDoc = /*#__PURE__*/ gql`
    fragment AppointmentModuleEmailContentSalesPersonSpecs on AppointmentModuleEmailContentSalesPerson {
  submitConfirmation {
    ...AppointmentModuleEmailContentSpecs
  }
  bookingCancellation {
    ...AppointmentModuleEmailContentSpecs
  }
  bookingAmendment {
    ...AppointmentModuleEmailContentSpecs
  }
  finderReservation {
    ...AppointmentModuleEmailContentFinderReservationSpecs
  }
  endTestDriveReminder {
    ...AppointmentModuleEmailContentSpecs
  }
}
    `;
export const AppointmentModuleEmailContentsSpecsFragmentDoc = /*#__PURE__*/ gql`
    fragment AppointmentModuleEmailContentsSpecs on AppointmentModuleEmailContents {
  customer {
    ...AppointmentModuleEmailContentCustomerSpecs
  }
  salesPerson {
    ...AppointmentModuleEmailContentSalesPersonSpecs
  }
}
    `;