import type * as SchemaTypes from '../types';

import type { TranslatedTextDataFragment } from './TranslationTextData';
import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { UploadFileWithPreviewFormDataFragment } from './UploadFileWithPreviewFormData';
import { gql } from '@apollo/client';
import { TranslatedTextDataFragmentDoc } from './TranslationTextData';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { UploadFileWithPreviewFormDataFragmentDoc } from './UploadFileWithPreviewFormData';
export type FinderApplicationModuleEmailContentSpecsFragment = (
  { __typename: 'FinderApplicationModuleEmailContents' }
  & { submitOrder: (
    { __typename: 'FinderApplicationEmailWithScenarioOverridesSubmitOrderContent' }
    & { defaultValue: (
      { __typename: 'FinderApplicationModuleSubmitOrderContent' }
      & Pick<SchemaTypes.FinderApplicationModuleSubmitOrderContent, 'isSummaryVehicleVisible'>
      & { subject: (
        { __typename: 'DealerTranslationText' }
        & TranslatedTextDataFragment
      ), introTitle: (
        { __typename: 'DealerTranslationText' }
        & TranslatedTextDataFragment
      ), introImage?: SchemaTypes.Maybe<(
        { __typename: 'UploadedFileWithPreview' }
        & UploadFileWithPreviewFormDataFragment
      )>, contentText: (
        { __typename: 'DealerTranslationText' }
        & TranslatedTextDataFragment
      ) }
    ), overrides: Array<(
      { __typename: 'FinderApplicationModuleSubmitOrderScenarioOverrideContent' }
      & Pick<SchemaTypes.FinderApplicationModuleSubmitOrderScenarioOverrideContent, 'isSummaryVehicleVisible' | 'scenarios' | 'isActive'>
      & { subject: (
        { __typename: 'DealerTranslationText' }
        & TranslatedTextDataFragment
      ), introTitle: (
        { __typename: 'DealerTranslationText' }
        & TranslatedTextDataFragment
      ), introImage?: SchemaTypes.Maybe<(
        { __typename: 'UploadedFileWithPreview' }
        & UploadFileWithPreviewFormDataFragment
      )>, contentText: (
        { __typename: 'DealerTranslationText' }
        & TranslatedTextDataFragment
      ) }
    )> }
  ), reminderEmail: (
    { __typename: 'FinderApplicationEmailWithScenarioOverridesReminderContent' }
    & { defaultValue: (
      { __typename: 'FinderApplicationModuleReminderEmailContent' }
      & Pick<SchemaTypes.FinderApplicationModuleReminderEmailContent, 'sendingTime' | 'isActive'>
      & { subject: (
        { __typename: 'DealerTranslationText' }
        & TranslatedTextDataFragment
      ), introTitle: (
        { __typename: 'DealerTranslationText' }
        & TranslatedTextDataFragment
      ), introImage?: SchemaTypes.Maybe<(
        { __typename: 'UploadedFileWithPreview' }
        & UploadFileWithPreviewFormDataFragment
      )>, contentText: (
        { __typename: 'DealerTranslationText' }
        & TranslatedTextDataFragment
      ) }
    ), overrides: Array<(
      { __typename: 'FinderApplicationModuleReminderEmailScenarioOverrideContent' }
      & Pick<SchemaTypes.FinderApplicationModuleReminderEmailScenarioOverrideContent, 'sendingTime' | 'scenarios' | 'isActive'>
      & { subject: (
        { __typename: 'DealerTranslationText' }
        & TranslatedTextDataFragment
      ), introTitle: (
        { __typename: 'DealerTranslationText' }
        & TranslatedTextDataFragment
      ), introImage?: SchemaTypes.Maybe<(
        { __typename: 'UploadedFileWithPreview' }
        & UploadFileWithPreviewFormDataFragment
      )>, contentText: (
        { __typename: 'DealerTranslationText' }
        & TranslatedTextDataFragment
      ) }
    )> }
  ) }
);

export const FinderApplicationModuleEmailContentSpecsFragmentDoc = /*#__PURE__*/ gql`
    fragment FinderApplicationModuleEmailContentSpecs on FinderApplicationModuleEmailContents {
  submitOrder {
    defaultValue {
      subject {
        ...TranslatedTextData
      }
      introTitle {
        ...TranslatedTextData
      }
      introImage {
        ...UploadFileWithPreviewFormData
      }
      contentText {
        ...TranslatedTextData
      }
      isSummaryVehicleVisible
    }
    overrides {
      subject {
        ...TranslatedTextData
      }
      introTitle {
        ...TranslatedTextData
      }
      introImage {
        ...UploadFileWithPreviewFormData
      }
      contentText {
        ...TranslatedTextData
      }
      isSummaryVehicleVisible
      scenarios
      isActive
    }
  }
  reminderEmail {
    defaultValue {
      subject {
        ...TranslatedTextData
      }
      introTitle {
        ...TranslatedTextData
      }
      introImage {
        ...UploadFileWithPreviewFormData
      }
      contentText {
        ...TranslatedTextData
      }
      sendingTime
      isActive
    }
    overrides {
      subject {
        ...TranslatedTextData
      }
      introTitle {
        ...TranslatedTextData
      }
      introImage {
        ...UploadFileWithPreviewFormData
      }
      contentText {
        ...TranslatedTextData
      }
      sendingTime
      scenarios
      isActive
    }
  }
}
    `;