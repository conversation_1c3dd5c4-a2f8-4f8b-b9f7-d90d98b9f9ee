fragment EventApplicationModuleEmailContentSpecs on EventApplicationModuleEmailContents {
    submitOrder {
        defaultValue {
            subject {
                ...TranslatedTextData
            }
            introTitle {
                ...TranslatedTextData
            }
            introImage {
                ...UploadFileWithPreviewFormData
            }
            contentText {
                ...TranslatedTextData
            }
        }
        overrides {
            subject {
                ...TranslatedTextData
            }
            introTitle {
                ...TranslatedTextData
            }
            introImage {
                ...UploadFileWithPreviewFormData
            }
            contentText {
                ...TranslatedTextData
            }
            scenarios
            isActive
        }
    }
}

fragment EventEmailContentSpecs on EventEmailContents {
    submitOrder {
        subject {
            ...TranslatedTextData
        }
        introTitle {
            ...TranslatedTextData
        }
        introImage {
            ...UploadFileWithPreviewFormData
        }
        contentText {
            ...TranslatedTextData
        }
    }

    testDrive {
        customer {
            submitConfirmation {
                ...AppointmentModuleEmailContentSpecs
            }
            bookingConfirmation {
                ...AppointmentModuleEmailContentSpecs
            }
            bookingCancellation {
                ...AppointmentModuleEmailContentSpecs
            }
            bookingAmendment {
                ...AppointmentModuleEmailContentSpecs
            }
            endTestDriveWithProcess {
                ...AppointmentModuleEmailContentSpecs
            }
            completeTestDriveWithoutProcess {
                ...AppointmentModuleEmailContentSpecs
            }
        }
    }
}
