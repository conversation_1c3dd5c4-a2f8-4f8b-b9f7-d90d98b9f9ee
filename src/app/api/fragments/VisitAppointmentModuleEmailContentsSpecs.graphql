fragment VisitAppointmentModuleEmailContentSpecs on VisitAppointmentModuleEmailContent {
    subject {
        ...DealerTranslatedStringSettingData
    }
    introTitle {
        ...DealerTranslatedStringSettingData
    }
    introImage {
        ...UploadFileWithPreviewFormData
    }
    contentText {
        ...DealerTranslatedStringSettingData
    }
    isSummaryVehicleVisible {
        ...DealerBooleanSettingData
    }
}

fragment VisitAppointmentModuleEmailContentCustomerSpecs on VisitAppointmentModuleEmailContentCustomer {
    submitConfirmation {
        ...VisitAppointmentModuleEmailContentSpecs
    }
    bookingConfirmation {
        ...VisitAppointmentModuleEmailContentSpecs
    }
    bookingCancellation {
        ...VisitAppointmentModuleEmailContentSpecs
    }
    bookingAmendment {
        ...VisitAppointmentModuleEmailContentSpecs
    }
    bookingComplete {
        ...VisitAppointmentModuleEmailContentSpecs
    }
}

fragment VisitAppointmentModuleEmailContentSalesPersonSpecs on VisitAppointmentModuleEmailContentSalesPerson {
    submitConfirmation {
        ...VisitAppointmentModuleEmailContentSpecs
    }
    bookingCancellation {
        ...VisitAppointmentModuleEmailContentSpecs
    }
    bookingAmendment {
        ...VisitAppointmentModuleEmailContentSpecs
    }
}

fragment VisitAppointmentModuleEmailContentsSpecs on VisitAppointmentModuleEmailContents {
    customer {
        ...VisitAppointmentModuleEmailContentCustomerSpecs
    }

    salesPerson {
        ...VisitAppointmentModuleEmailContentSalesPersonSpecs
    }
}
