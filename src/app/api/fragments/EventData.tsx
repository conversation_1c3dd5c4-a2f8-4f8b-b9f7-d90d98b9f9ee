import type * as SchemaTypes from '../types';

import type { EventModuleData_AdyenPaymentModule_Fragment, EventModuleData_AppointmentModule_Fragment, EventModuleData_AutoplayModule_Fragment, EventModuleData_BankModule_Fragment, EventModuleData_BasicSigningModule_Fragment, EventModuleData_CapModule_Fragment, EventModuleData_ConfiguratorModule_Fragment, EventModuleData_ConsentsAndDeclarationsModule_Fragment, EventModuleData_CtsModule_Fragment, EventModuleData_DocusignModule_Fragment, EventModuleData_EventApplicationModule_Fragment, EventModuleData_FinderApplicationPrivateModule_Fragment, EventModuleData_FinderApplicationPublicModule_Fragment, EventModuleData_FinderVehicleManagementModule_Fragment, EventModuleData_FiservPaymentModule_Fragment, EventModuleData_GiftVoucherModule_Fragment, EventModuleData_InsuranceModule_Fragment, EventModuleData_LabelsModule_Fragment, EventModuleData_LaunchPadModule_Fragment, EventModuleData_LocalCustomerManagementModule_Fragment, EventModuleData_MaintenanceModule_Fragment, EventModuleData_MarketingModule_Fragment, EventModuleData_MobilityModule_Fragment, EventModuleData_MyInfoModule_Fragment, EventModuleData_NamirialSigningModule_Fragment, EventModuleData_OidcModule_Fragment, EventModuleData_PayGatePaymentModule_Fragment, EventModuleData_PorscheIdModule_Fragment, EventModuleData_PorscheMasterDataModule_Fragment, EventModuleData_PorschePaymentModule_Fragment, EventModuleData_PorscheRetainModule_Fragment, EventModuleData_PromoCodeModule_Fragment, EventModuleData_SalesControlBoardModule_Fragment, EventModuleData_SalesOfferModule_Fragment, EventModuleData_SimpleVehicleManagementModule_Fragment, EventModuleData_StandardApplicationModule_Fragment, EventModuleData_TradeInModule_Fragment, EventModuleData_TtbPaymentModule_Fragment, EventModuleData_UserlikeChatbotModule_Fragment, EventModuleData_VehicleDataWithPorscheCodeIntegrationModule_Fragment, EventModuleData_VisitAppointmentModule_Fragment, EventModuleData_WebsiteModule_Fragment, EventModuleData_WhatsappLiveChatModule_Fragment } from './EventModuleData';
import type { CompanyInModuleOptionDataFragment } from './CompanyInModuleOptionData';
import type { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragment } from './VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import type { LocalCustomerManagementModuleKycFieldSpecsFragment } from './LocalCustomerManagementModuleKycFieldSpecs';
import type { AppointmentTimeSlotDataFragment } from './AppointmentTimeSlotData';
import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { TimeSlotDataFragment } from './TimeSlotData';
import type { TranslatedStringSpecsFragment } from './TranslatedStringSpecs';
import type { DealershipSettingSpecData_DealershipMyInfoSetting_Fragment, DealershipSettingSpecData_DealershipPaymentSetting_Fragment, DealershipSettingSpecData_DealershipPublicSalesPerson_Fragment } from './DealershipSettingSpecData';
import type { DepositAmountDataFragment } from './DepositAmountData';
import type { SimpleVersioningDataFragment } from './SimpleVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from './AuthorData';
import type { KycPresetsSpecFragment } from './KYCPresetsSpec';
import type { ConditionSpecs_ApplicationModuleCondition_Fragment, ConditionSpecs_BankCondition_Fragment, ConditionSpecs_ContextualCondition_Fragment, ConditionSpecs_DealerCondition_Fragment, ConditionSpecs_GiftVoucherCondition_Fragment, ConditionSpecs_InsurerCondition_Fragment, ConditionSpecs_LocationCondition_Fragment, ConditionSpecs_LogicCondition_Fragment, ConditionSpecs_SalesOfferAgreementsCondition_Fragment } from './ConditionSpecs';
import type { BaseConditionSpecs_ApplicationModuleCondition_Fragment, BaseConditionSpecs_BankCondition_Fragment, BaseConditionSpecs_ContextualCondition_Fragment, BaseConditionSpecs_DealerCondition_Fragment, BaseConditionSpecs_GiftVoucherCondition_Fragment, BaseConditionSpecs_InsurerCondition_Fragment, BaseConditionSpecs_LocationCondition_Fragment, BaseConditionSpecs_LogicCondition_Fragment, BaseConditionSpecs_SalesOfferAgreementsCondition_Fragment } from './BaseConditionSpecs';
import type { MobilityLocationDataFragment } from './MobilityLocationData';
import type { UserPreviewDataFragment } from './UserPreviewData';
import type { KycExtraSettingsSpecsFragment } from './KYCExtraSettingsSpecs';
import type { CustomizedFieldDataFragment } from './CustomizedFieldData';
import type { UploadFileWithPreviewFormDataFragment } from './UploadFileWithPreviewFormData';
import type { EventEmailContentSpecsFragment, EventApplicationModuleEmailContentSpecsFragment } from './EventApplicationModuleEmailContentSpecs';
import type { TranslatedTextDataFragment } from './TranslationTextData';
import type { AppointmentModuleEmailContentSpecsFragment, AppointmentModuleEmailContentsSpecsFragment, AppointmentModuleEmailContentCustomerSpecsFragment, AppointmentModuleEmailContentSalesPersonSpecsFragment, AppointmentModuleEmailContentFinderReservationSpecsFragment } from './AppointmentModuleEmailContentsSpecs';
import type { DealerTranslatedStringSettingDataFragment } from './DealerTranslatedStringData';
import type { DealerBooleanSettingDataFragment } from './DealerBooleanSettingData';
import type { ThankYouPageContentSpecsFragment } from './ThankYouPageContent';
import type { CustomTestDriveBookingSlotsDataFragment } from './CustomTestDriveBookingSlotsData';
import type { TestDriveFixedPeriodDataFragment } from './TestDriveFixedPeriodData';
import type { TestDriveBookingWindowSettingsDataFragment } from './TestDriveBookingWindowSettingsData';
import { gql } from '@apollo/client';
import { EventModuleDataFragmentDoc } from './EventModuleData';
import { CompanyInModuleOptionDataFragmentDoc } from './CompanyInModuleOptionData';
import { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragmentDoc } from './VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import { LocalCustomerManagementModuleKycFieldSpecsFragmentDoc } from './LocalCustomerManagementModuleKycFieldSpecs';
import { AppointmentTimeSlotDataFragmentDoc } from './AppointmentTimeSlotData';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { TimeSlotDataFragmentDoc } from './TimeSlotData';
import { TranslatedStringSpecsFragmentDoc } from './TranslatedStringSpecs';
import { DealershipSettingSpecDataFragmentDoc } from './DealershipSettingSpecData';
import { DepositAmountDataFragmentDoc } from './DepositAmountData';
import { SimpleVersioningDataFragmentDoc } from './SimpleVersioningData';
import { AuthorDataFragmentDoc } from './AuthorData';
import { KycPresetsSpecFragmentDoc } from './KYCPresetsSpec';
import { ConditionSpecsFragmentDoc } from './ConditionSpecs';
import { BaseConditionSpecsFragmentDoc } from './BaseConditionSpecs';
import { MobilityLocationDataFragmentDoc } from './MobilityLocationData';
import { UserPreviewDataFragmentDoc } from './UserPreviewData';
import { KycExtraSettingsSpecsFragmentDoc } from './KYCExtraSettingsSpecs';
import { CustomizedFieldDataFragmentDoc } from './CustomizedFieldData';
import { UploadFileWithPreviewFormDataFragmentDoc } from './UploadFileWithPreviewFormData';
import { EventEmailContentSpecsFragmentDoc, EventApplicationModuleEmailContentSpecsFragmentDoc } from './EventApplicationModuleEmailContentSpecs';
import { TranslatedTextDataFragmentDoc } from './TranslationTextData';
import { AppointmentModuleEmailContentSpecsFragmentDoc, AppointmentModuleEmailContentsSpecsFragmentDoc, AppointmentModuleEmailContentCustomerSpecsFragmentDoc, AppointmentModuleEmailContentSalesPersonSpecsFragmentDoc, AppointmentModuleEmailContentFinderReservationSpecsFragmentDoc } from './AppointmentModuleEmailContentsSpecs';
import { DealerTranslatedStringSettingDataFragmentDoc } from './DealerTranslatedStringData';
import { DealerBooleanSettingDataFragmentDoc } from './DealerBooleanSettingData';
import { ThankYouPageContentSpecsFragmentDoc } from './ThankYouPageContent';
import { CustomTestDriveBookingSlotsDataFragmentDoc } from './CustomTestDriveBookingSlotsData';
import { TestDriveFixedPeriodDataFragmentDoc } from './TestDriveFixedPeriodData';
import { TestDriveBookingWindowSettingsDataFragmentDoc } from './TestDriveBookingWindowSettingsData';
export type EventDataFragment = (
  { __typename: 'Event' }
  & Pick<SchemaTypes.Event, 'id' | 'identifier' | 'scenarios' | 'displayName' | 'isActive' | 'skipForDeposit' | 'privateAccess' | 'isAllowTradeIn' | 'isAllowTestDrive' | 'isDeleted' | 'firstRouterPath' | 'urlSlug' | 'dealerIds' | 'showLiveChat' | 'showDealership' | 'isCapEnabled' | 'isSearchCapCustomerOptional' | 'capPrequalification' | 'permissions' | 'hasCustomiseEmail' | 'hasCustomiseBanner' | 'enableDynamicUtmTracking' | 'porscheIdModuleId' | 'isCustomerDataRetreivalByPorscheId' | 'isPorscheIdLoginMandatory' | 'displayAppointmentDatepicker' | 'displayVisitAppointmentDatepicker' | 'userIds' | 'hasVehicleIntegration' | 'salesConsultantAutoAssignmentEnabled' | 'hasCustomiseThankYouPage'>
  & { module: (
    { __typename: 'AdyenPaymentModule' }
    & EventModuleData_AdyenPaymentModule_Fragment
  ) | (
    { __typename: 'AppointmentModule' }
    & EventModuleData_AppointmentModule_Fragment
  ) | (
    { __typename: 'AutoplayModule' }
    & EventModuleData_AutoplayModule_Fragment
  ) | (
    { __typename: 'BankModule' }
    & EventModuleData_BankModule_Fragment
  ) | (
    { __typename: 'BasicSigningModule' }
    & EventModuleData_BasicSigningModule_Fragment
  ) | (
    { __typename: 'CapModule' }
    & EventModuleData_CapModule_Fragment
  ) | (
    { __typename: 'ConfiguratorModule' }
    & EventModuleData_ConfiguratorModule_Fragment
  ) | (
    { __typename: 'ConsentsAndDeclarationsModule' }
    & EventModuleData_ConsentsAndDeclarationsModule_Fragment
  ) | (
    { __typename: 'CtsModule' }
    & EventModuleData_CtsModule_Fragment
  ) | (
    { __typename: 'DocusignModule' }
    & EventModuleData_DocusignModule_Fragment
  ) | (
    { __typename: 'EventApplicationModule' }
    & EventModuleData_EventApplicationModule_Fragment
  ) | (
    { __typename: 'FinderApplicationPrivateModule' }
    & EventModuleData_FinderApplicationPrivateModule_Fragment
  ) | (
    { __typename: 'FinderApplicationPublicModule' }
    & EventModuleData_FinderApplicationPublicModule_Fragment
  ) | (
    { __typename: 'FinderVehicleManagementModule' }
    & EventModuleData_FinderVehicleManagementModule_Fragment
  ) | (
    { __typename: 'FiservPaymentModule' }
    & EventModuleData_FiservPaymentModule_Fragment
  ) | (
    { __typename: 'GiftVoucherModule' }
    & EventModuleData_GiftVoucherModule_Fragment
  ) | (
    { __typename: 'InsuranceModule' }
    & EventModuleData_InsuranceModule_Fragment
  ) | (
    { __typename: 'LabelsModule' }
    & EventModuleData_LabelsModule_Fragment
  ) | (
    { __typename: 'LaunchPadModule' }
    & EventModuleData_LaunchPadModule_Fragment
  ) | (
    { __typename: 'LocalCustomerManagementModule' }
    & EventModuleData_LocalCustomerManagementModule_Fragment
  ) | (
    { __typename: 'MaintenanceModule' }
    & EventModuleData_MaintenanceModule_Fragment
  ) | (
    { __typename: 'MarketingModule' }
    & EventModuleData_MarketingModule_Fragment
  ) | (
    { __typename: 'MobilityModule' }
    & EventModuleData_MobilityModule_Fragment
  ) | (
    { __typename: 'MyInfoModule' }
    & EventModuleData_MyInfoModule_Fragment
  ) | (
    { __typename: 'NamirialSigningModule' }
    & EventModuleData_NamirialSigningModule_Fragment
  ) | (
    { __typename: 'OIDCModule' }
    & EventModuleData_OidcModule_Fragment
  ) | (
    { __typename: 'PayGatePaymentModule' }
    & EventModuleData_PayGatePaymentModule_Fragment
  ) | (
    { __typename: 'PorscheIdModule' }
    & EventModuleData_PorscheIdModule_Fragment
  ) | (
    { __typename: 'PorscheMasterDataModule' }
    & EventModuleData_PorscheMasterDataModule_Fragment
  ) | (
    { __typename: 'PorschePaymentModule' }
    & EventModuleData_PorschePaymentModule_Fragment
  ) | (
    { __typename: 'PorscheRetainModule' }
    & EventModuleData_PorscheRetainModule_Fragment
  ) | (
    { __typename: 'PromoCodeModule' }
    & EventModuleData_PromoCodeModule_Fragment
  ) | (
    { __typename: 'SalesControlBoardModule' }
    & EventModuleData_SalesControlBoardModule_Fragment
  ) | (
    { __typename: 'SalesOfferModule' }
    & EventModuleData_SalesOfferModule_Fragment
  ) | (
    { __typename: 'SimpleVehicleManagementModule' }
    & EventModuleData_SimpleVehicleManagementModule_Fragment
  ) | (
    { __typename: 'StandardApplicationModule' }
    & EventModuleData_StandardApplicationModule_Fragment
  ) | (
    { __typename: 'TradeInModule' }
    & EventModuleData_TradeInModule_Fragment
  ) | (
    { __typename: 'TtbPaymentModule' }
    & EventModuleData_TtbPaymentModule_Fragment
  ) | (
    { __typename: 'UserlikeChatbotModule' }
    & EventModuleData_UserlikeChatbotModule_Fragment
  ) | (
    { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
    & EventModuleData_VehicleDataWithPorscheCodeIntegrationModule_Fragment
  ) | (
    { __typename: 'VisitAppointmentModule' }
    & EventModuleData_VisitAppointmentModule_Fragment
  ) | (
    { __typename: 'WebsiteModule' }
    & EventModuleData_WebsiteModule_Fragment
  ) | (
    { __typename: 'WhatsappLiveChatModule' }
    & EventModuleData_WhatsappLiveChatModule_Fragment
  ), name: (
    { __typename: 'TranslatedString' }
    & TranslatedStringSpecsFragment
  ), period: (
    { __typename: 'Period' }
    & Pick<SchemaTypes.Period, 'start' | 'end'>
  ), myInfoSetting?: SchemaTypes.Maybe<(
    { __typename: 'DealershipMyInfoSetting' }
    & DealershipSettingSpecData_DealershipMyInfoSetting_Fragment
  ) | (
    { __typename: 'DealershipPaymentSetting' }
    & DealershipSettingSpecData_DealershipPaymentSetting_Fragment
  ) | (
    { __typename: 'DealershipPublicSalesPerson' }
    & DealershipSettingSpecData_DealershipPublicSalesPerson_Fragment
  )>, publicSalesPerson?: SchemaTypes.Maybe<(
    { __typename: 'DealershipMyInfoSetting' }
    & DealershipSettingSpecData_DealershipMyInfoSetting_Fragment
  ) | (
    { __typename: 'DealershipPaymentSetting' }
    & DealershipSettingSpecData_DealershipPaymentSetting_Fragment
  ) | (
    { __typename: 'DealershipPublicSalesPerson' }
    & DealershipSettingSpecData_DealershipPublicSalesPerson_Fragment
  )>, paymentSetting?: SchemaTypes.Maybe<(
    { __typename: 'DealershipMyInfoSetting' }
    & DealershipSettingSpecData_DealershipMyInfoSetting_Fragment
  ) | (
    { __typename: 'DealershipPaymentSetting' }
    & DealershipSettingSpecData_DealershipPaymentSetting_Fragment
  ) | (
    { __typename: 'DealershipPublicSalesPerson' }
    & DealershipSettingSpecData_DealershipPublicSalesPerson_Fragment
  )>, depositAmount: (
    { __typename: 'DepositAmount' }
    & DepositAmountDataFragment
  ), versioning: (
    { __typename: 'SimpleVersioning' }
    & SimpleVersioningDataFragment
  ), dealerVehicles: Array<(
    { __typename: 'DealerVehicles' }
    & Pick<SchemaTypes.DealerVehicles, 'dealerId' | 'vehicleSuiteIds'>
    & { vehicles: Array<(
      { __typename: 'FinderVehicle' }
      & Pick<SchemaTypes.FinderVehicle, 'id'>
      & { name: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ) }
    ) | (
      { __typename: 'LocalMake' }
      & Pick<SchemaTypes.LocalMake, 'id'>
      & { name: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ) }
    ) | (
      { __typename: 'LocalModel' }
      & Pick<SchemaTypes.LocalModel, 'id'>
      & { name: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ) }
    ) | (
      { __typename: 'LocalVariant' }
      & Pick<SchemaTypes.LocalVariant, 'id'>
      & { name: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ) }
    )>, dealer: (
      { __typename: 'Dealer' }
      & Pick<SchemaTypes.Dealer, 'id'>
      & { legalName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ) }
    ) }
  )>, kycPresets: Array<(
    { __typename: 'KYCPreset' }
    & KycPresetsSpecFragment
  )>, kycExtraSettings: (
    { __typename: 'LocalCustomerManagementKYCFieldsExtraConfig' }
    & KycExtraSettingsSpecsFragment
  ), customizedFields: Array<(
    { __typename: 'CustomizedField' }
    & CustomizedFieldDataFragment
  )>, banner?: SchemaTypes.Maybe<(
    { __typename: 'Banner' }
    & Pick<SchemaTypes.Banner, 'id' | 'bannerTextPosition' | 'isActive'>
    & { bannerImage?: SchemaTypes.Maybe<(
      { __typename: 'UploadedFileWithPreview' }
      & UploadFileWithPreviewFormDataFragment
    )>, mobileBannerImage?: SchemaTypes.Maybe<(
      { __typename: 'UploadedFileWithPreview' }
      & UploadFileWithPreviewFormDataFragment
    )>, bannerHeader?: SchemaTypes.Maybe<(
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    )>, bannerText: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ) }
  )>, emailContents?: SchemaTypes.Maybe<(
    { __typename: 'EventEmailContents' }
    & EventEmailContentSpecsFragment
  )>, utmParametersSettings: (
    { __typename: 'EventUtmParameters' }
    & { defaultValue: (
      { __typename: 'EventUtmParametersDefaultValue' }
      & Pick<SchemaTypes.EventUtmParametersDefaultValue, 'capCampaignId' | 'capLeadSource' | 'capLeadOrigin'>
    ), overrides: Array<(
      { __typename: 'EventUtmParametersOverride' }
      & Pick<SchemaTypes.EventUtmParametersOverride, 'capCampaignId' | 'capLeadSource' | 'capLeadOrigin' | 'utmUrl'>
    )> }
  ), thankYouPageContent: (
    { __typename: 'ThankYouPageContent' }
    & ThankYouPageContentSpecsFragment
  ), customTestDriveBookingSlots?: SchemaTypes.Maybe<(
    { __typename: 'CustomTestDriveBookingSlots' }
    & CustomTestDriveBookingSlotsDataFragment
  )> }
);

export const EventDataFragmentDoc = /*#__PURE__*/ gql`
    fragment EventData on Event {
  id
  identifier
  scenarios
  module {
    ...EventModuleData
  }
  displayName
  name {
    ...TranslatedStringSpecs
  }
  isActive
  period {
    start
    end
  }
  myInfoSetting {
    ...DealershipSettingSpecData
  }
  publicSalesPerson {
    ...DealershipSettingSpecData
  }
  paymentSetting {
    ...DealershipSettingSpecData
  }
  depositAmount {
    ...DepositAmountData
  }
  skipForDeposit
  privateAccess
  isAllowTradeIn
  isAllowTestDrive
  isDeleted
  versioning {
    ...SimpleVersioningData
  }
  firstRouterPath
  urlSlug
  dealerIds
  dealerVehicles {
    dealerId
    vehicleSuiteIds
    vehicles {
      id
      name {
        ...TranslatedStringData
      }
    }
    dealer {
      id
      legalName {
        ...TranslatedStringData
      }
    }
  }
  showLiveChat
  showDealership
  kycPresets {
    ...KYCPresetsSpec
  }
  kycExtraSettings {
    ...KYCExtraSettingsSpecs
  }
  customizedFields {
    ...CustomizedFieldData
  }
  isCapEnabled
  isSearchCapCustomerOptional
  capPrequalification
  permissions
  firstRouterPath
  hasCustomiseEmail
  hasCustomiseBanner
  banner {
    id
    bannerImage {
      ...UploadFileWithPreviewFormData
    }
    mobileBannerImage {
      ...UploadFileWithPreviewFormData
    }
    bannerHeader {
      ...TranslatedStringData
    }
    bannerText {
      ...TranslatedStringData
    }
    bannerTextPosition
    isActive
  }
  emailContents {
    ...EventEmailContentSpecs
  }
  enableDynamicUtmTracking
  utmParametersSettings {
    defaultValue {
      capCampaignId
      capLeadSource
      capLeadOrigin
    }
    overrides {
      capCampaignId
      capLeadSource
      capLeadOrigin
      utmUrl
    }
  }
  porscheIdModuleId
  isCustomerDataRetreivalByPorscheId
  isPorscheIdLoginMandatory
  displayAppointmentDatepicker
  displayVisitAppointmentDatepicker
  userIds
  hasVehicleIntegration
  salesConsultantAutoAssignmentEnabled
  hasCustomiseThankYouPage
  thankYouPageContent {
    ...ThankYouPageContentSpecs
  }
  customTestDriveBookingSlots {
    ...CustomTestDriveBookingSlotsData
  }
}
    `;