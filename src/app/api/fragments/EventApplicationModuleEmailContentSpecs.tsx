import type * as SchemaTypes from '../types';

import type { TranslatedTextDataFragment } from './TranslationTextData';
import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { UploadFileWithPreviewFormDataFragment } from './UploadFileWithPreviewFormData';
import type { AppointmentModuleEmailContentSpecsFragment, AppointmentModuleEmailContentsSpecsFragment, AppointmentModuleEmailContentCustomerSpecsFragment, AppointmentModuleEmailContentSalesPersonSpecsFragment, AppointmentModuleEmailContentFinderReservationSpecsFragment } from './AppointmentModuleEmailContentsSpecs';
import type { DealerTranslatedStringSettingDataFragment } from './DealerTranslatedStringData';
import type { DealerBooleanSettingDataFragment } from './DealerBooleanSettingData';
import { gql } from '@apollo/client';
import { TranslatedTextDataFragmentDoc } from './TranslationTextData';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { UploadFileWithPreviewFormDataFragmentDoc } from './UploadFileWithPreviewFormData';
import { AppointmentModuleEmailContentSpecsFragmentDoc, AppointmentModuleEmailContentsSpecsFragmentDoc, AppointmentModuleEmailContentCustomerSpecsFragmentDoc, AppointmentModuleEmailContentSalesPersonSpecsFragmentDoc, AppointmentModuleEmailContentFinderReservationSpecsFragmentDoc } from './AppointmentModuleEmailContentsSpecs';
import { DealerTranslatedStringSettingDataFragmentDoc } from './DealerTranslatedStringData';
import { DealerBooleanSettingDataFragmentDoc } from './DealerBooleanSettingData';
export type EventApplicationModuleEmailContentSpecsFragment = (
  { __typename: 'EventApplicationModuleEmailContents' }
  & { submitOrder: (
    { __typename: 'EventApplicationSubmitOrderWithOverrideContent' }
    & { defaultValue: (
      { __typename: 'EventApplicationModuleSubmitOrderContent' }
      & { subject: (
        { __typename: 'DealerTranslationText' }
        & TranslatedTextDataFragment
      ), introTitle: (
        { __typename: 'DealerTranslationText' }
        & TranslatedTextDataFragment
      ), introImage?: SchemaTypes.Maybe<(
        { __typename: 'UploadedFileWithPreview' }
        & UploadFileWithPreviewFormDataFragment
      )>, contentText: (
        { __typename: 'DealerTranslationText' }
        & TranslatedTextDataFragment
      ) }
    ), overrides: Array<(
      { __typename: 'EventApplicationSubmitOrderOverrideContent' }
      & Pick<SchemaTypes.EventApplicationSubmitOrderOverrideContent, 'scenarios' | 'isActive'>
      & { subject: (
        { __typename: 'DealerTranslationText' }
        & TranslatedTextDataFragment
      ), introTitle: (
        { __typename: 'DealerTranslationText' }
        & TranslatedTextDataFragment
      ), introImage?: SchemaTypes.Maybe<(
        { __typename: 'UploadedFileWithPreview' }
        & UploadFileWithPreviewFormDataFragment
      )>, contentText: (
        { __typename: 'DealerTranslationText' }
        & TranslatedTextDataFragment
      ) }
    )> }
  ) }
);

export type EventEmailContentSpecsFragment = (
  { __typename: 'EventEmailContents' }
  & { submitOrder: (
    { __typename: 'EventSubmitOrder' }
    & { subject: (
      { __typename: 'DealerTranslationText' }
      & TranslatedTextDataFragment
    ), introTitle: (
      { __typename: 'DealerTranslationText' }
      & TranslatedTextDataFragment
    ), introImage?: SchemaTypes.Maybe<(
      { __typename: 'UploadedFileWithPreview' }
      & UploadFileWithPreviewFormDataFragment
    )>, contentText: (
      { __typename: 'DealerTranslationText' }
      & TranslatedTextDataFragment
    ) }
  ), testDrive: (
    { __typename: 'TestDriveEmailOnEvent' }
    & { customer: (
      { __typename: 'AppointmentModuleEmailContentCustomer' }
      & { submitConfirmation: (
        { __typename: 'AppointmentModuleEmailContent' }
        & AppointmentModuleEmailContentSpecsFragment
      ), bookingConfirmation: (
        { __typename: 'AppointmentModuleEmailContent' }
        & AppointmentModuleEmailContentSpecsFragment
      ), bookingCancellation: (
        { __typename: 'AppointmentModuleEmailContent' }
        & AppointmentModuleEmailContentSpecsFragment
      ), bookingAmendment: (
        { __typename: 'AppointmentModuleEmailContent' }
        & AppointmentModuleEmailContentSpecsFragment
      ), endTestDriveWithProcess: (
        { __typename: 'AppointmentModuleEmailContent' }
        & AppointmentModuleEmailContentSpecsFragment
      ), completeTestDriveWithoutProcess: (
        { __typename: 'AppointmentModuleEmailContent' }
        & AppointmentModuleEmailContentSpecsFragment
      ) }
    ) }
  ) }
);

export const EventApplicationModuleEmailContentSpecsFragmentDoc = /*#__PURE__*/ gql`
    fragment EventApplicationModuleEmailContentSpecs on EventApplicationModuleEmailContents {
  submitOrder {
    defaultValue {
      subject {
        ...TranslatedTextData
      }
      introTitle {
        ...TranslatedTextData
      }
      introImage {
        ...UploadFileWithPreviewFormData
      }
      contentText {
        ...TranslatedTextData
      }
    }
    overrides {
      subject {
        ...TranslatedTextData
      }
      introTitle {
        ...TranslatedTextData
      }
      introImage {
        ...UploadFileWithPreviewFormData
      }
      contentText {
        ...TranslatedTextData
      }
      scenarios
      isActive
    }
  }
}
    `;
export const EventEmailContentSpecsFragmentDoc = /*#__PURE__*/ gql`
    fragment EventEmailContentSpecs on EventEmailContents {
  submitOrder {
    subject {
      ...TranslatedTextData
    }
    introTitle {
      ...TranslatedTextData
    }
    introImage {
      ...UploadFileWithPreviewFormData
    }
    contentText {
      ...TranslatedTextData
    }
  }
  testDrive {
    customer {
      submitConfirmation {
        ...AppointmentModuleEmailContentSpecs
      }
      bookingConfirmation {
        ...AppointmentModuleEmailContentSpecs
      }
      bookingCancellation {
        ...AppointmentModuleEmailContentSpecs
      }
      bookingAmendment {
        ...AppointmentModuleEmailContentSpecs
      }
      endTestDriveWithProcess {
        ...AppointmentModuleEmailContentSpecs
      }
      completeTestDriveWithoutProcess {
        ...AppointmentModuleEmailContentSpecs
      }
    }
  }
}
    `;