import type * as SchemaTypes from '../types';

import type { DealerTranslatedStringSettingDataFragment } from './DealerTranslatedStringData';
import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { UploadFileWithPreviewFormDataFragment } from './UploadFileWithPreviewFormData';
import type { DealerBooleanSettingDataFragment } from './DealerBooleanSettingData';
import { gql } from '@apollo/client';
import { DealerTranslatedStringSettingDataFragmentDoc } from './DealerTranslatedStringData';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { UploadFileWithPreviewFormDataFragmentDoc } from './UploadFileWithPreviewFormData';
import { DealerBooleanSettingDataFragmentDoc } from './DealerBooleanSettingData';
export type VisitAppointmentModuleEmailContentSpecsFragment = (
  { __typename: 'VisitAppointmentModuleEmailContent' }
  & { subject: (
    { __typename: 'DealerTranslatedStringSetting' }
    & DealerTranslatedStringSettingDataFragment
  ), introTitle: (
    { __typename: 'DealerTranslatedStringSetting' }
    & DealerTranslatedStringSettingDataFragment
  ), introImage?: SchemaTypes.Maybe<(
    { __typename: 'UploadedFileWithPreview' }
    & UploadFileWithPreviewFormDataFragment
  )>, contentText: (
    { __typename: 'DealerTranslatedStringSetting' }
    & DealerTranslatedStringSettingDataFragment
  ), isSummaryVehicleVisible: (
    { __typename: 'DealerBooleanSetting' }
    & DealerBooleanSettingDataFragment
  ) }
);

export type VisitAppointmentModuleEmailContentCustomerSpecsFragment = (
  { __typename: 'VisitAppointmentModuleEmailContentCustomer' }
  & { submitConfirmation: (
    { __typename: 'VisitAppointmentModuleEmailContent' }
    & VisitAppointmentModuleEmailContentSpecsFragment
  ), bookingConfirmation: (
    { __typename: 'VisitAppointmentModuleEmailContent' }
    & VisitAppointmentModuleEmailContentSpecsFragment
  ), bookingCancellation: (
    { __typename: 'VisitAppointmentModuleEmailContent' }
    & VisitAppointmentModuleEmailContentSpecsFragment
  ), bookingAmendment: (
    { __typename: 'VisitAppointmentModuleEmailContent' }
    & VisitAppointmentModuleEmailContentSpecsFragment
  ), bookingComplete: (
    { __typename: 'VisitAppointmentModuleEmailContent' }
    & VisitAppointmentModuleEmailContentSpecsFragment
  ) }
);

export type VisitAppointmentModuleEmailContentSalesPersonSpecsFragment = (
  { __typename: 'VisitAppointmentModuleEmailContentSalesPerson' }
  & { submitConfirmation: (
    { __typename: 'VisitAppointmentModuleEmailContent' }
    & VisitAppointmentModuleEmailContentSpecsFragment
  ), bookingCancellation: (
    { __typename: 'VisitAppointmentModuleEmailContent' }
    & VisitAppointmentModuleEmailContentSpecsFragment
  ), bookingAmendment: (
    { __typename: 'VisitAppointmentModuleEmailContent' }
    & VisitAppointmentModuleEmailContentSpecsFragment
  ) }
);

export type VisitAppointmentModuleEmailContentsSpecsFragment = (
  { __typename: 'VisitAppointmentModuleEmailContents' }
  & { customer: (
    { __typename: 'VisitAppointmentModuleEmailContentCustomer' }
    & VisitAppointmentModuleEmailContentCustomerSpecsFragment
  ), salesPerson: (
    { __typename: 'VisitAppointmentModuleEmailContentSalesPerson' }
    & VisitAppointmentModuleEmailContentSalesPersonSpecsFragment
  ) }
);

export const VisitAppointmentModuleEmailContentSpecsFragmentDoc = /*#__PURE__*/ gql`
    fragment VisitAppointmentModuleEmailContentSpecs on VisitAppointmentModuleEmailContent {
  subject {
    ...DealerTranslatedStringSettingData
  }
  introTitle {
    ...DealerTranslatedStringSettingData
  }
  introImage {
    ...UploadFileWithPreviewFormData
  }
  contentText {
    ...DealerTranslatedStringSettingData
  }
  isSummaryVehicleVisible {
    ...DealerBooleanSettingData
  }
}
    `;
export const VisitAppointmentModuleEmailContentCustomerSpecsFragmentDoc = /*#__PURE__*/ gql`
    fragment VisitAppointmentModuleEmailContentCustomerSpecs on VisitAppointmentModuleEmailContentCustomer {
  submitConfirmation {
    ...VisitAppointmentModuleEmailContentSpecs
  }
  bookingConfirmation {
    ...VisitAppointmentModuleEmailContentSpecs
  }
  bookingCancellation {
    ...VisitAppointmentModuleEmailContentSpecs
  }
  bookingAmendment {
    ...VisitAppointmentModuleEmailContentSpecs
  }
  bookingComplete {
    ...VisitAppointmentModuleEmailContentSpecs
  }
}
    `;
export const VisitAppointmentModuleEmailContentSalesPersonSpecsFragmentDoc = /*#__PURE__*/ gql`
    fragment VisitAppointmentModuleEmailContentSalesPersonSpecs on VisitAppointmentModuleEmailContentSalesPerson {
  submitConfirmation {
    ...VisitAppointmentModuleEmailContentSpecs
  }
  bookingCancellation {
    ...VisitAppointmentModuleEmailContentSpecs
  }
  bookingAmendment {
    ...VisitAppointmentModuleEmailContentSpecs
  }
}
    `;
export const VisitAppointmentModuleEmailContentsSpecsFragmentDoc = /*#__PURE__*/ gql`
    fragment VisitAppointmentModuleEmailContentsSpecs on VisitAppointmentModuleEmailContents {
  customer {
    ...VisitAppointmentModuleEmailContentCustomerSpecs
  }
  salesPerson {
    ...VisitAppointmentModuleEmailContentSalesPersonSpecs
  }
}
    `;