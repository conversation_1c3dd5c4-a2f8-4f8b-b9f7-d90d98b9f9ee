import type * as SchemaTypes from '../types';

import type { EventDataFragment } from '../fragments/EventData';
import type { EventModuleData_AdyenPaymentModule_Fragment, EventModuleData_AppointmentModule_Fragment, EventModuleData_AutoplayModule_Fragment, EventModuleData_BankModule_Fragment, EventModuleData_BasicSigningModule_Fragment, EventModuleData_CapModule_Fragment, EventModuleData_ConfiguratorModule_Fragment, EventModuleData_ConsentsAndDeclarationsModule_Fragment, EventModuleData_CtsModule_Fragment, EventModuleData_DocusignModule_Fragment, EventModuleData_EventApplicationModule_Fragment, EventModuleData_FinderApplicationPrivateModule_Fragment, EventModuleData_FinderApplicationPublicModule_Fragment, EventModuleData_FinderVehicleManagementModule_Fragment, EventModuleData_FiservPaymentModule_Fragment, EventModuleData_GiftVoucherModule_Fragment, EventModuleData_InsuranceModule_Fragment, EventModuleData_LabelsModule_Fragment, EventModuleData_LaunchPadModule_Fragment, EventModuleData_LocalCustomerManagementModule_Fragment, EventModuleData_MaintenanceModule_Fragment, EventModuleData_MarketingModule_Fragment, EventModuleData_MobilityModule_Fragment, EventModuleData_MyInfoModule_Fragment, EventModuleData_NamirialSigningModule_Fragment, EventModuleData_OidcModule_Fragment, EventModuleData_PayGatePaymentModule_Fragment, EventModuleData_PorscheIdModule_Fragment, EventModuleData_PorscheMasterDataModule_Fragment, EventModuleData_PorschePaymentModule_Fragment, EventModuleData_PorscheRetainModule_Fragment, EventModuleData_PromoCodeModule_Fragment, EventModuleData_SalesControlBoardModule_Fragment, EventModuleData_SalesOfferModule_Fragment, EventModuleData_SimpleVehicleManagementModule_Fragment, EventModuleData_StandardApplicationModule_Fragment, EventModuleData_TradeInModule_Fragment, EventModuleData_TtbPaymentModule_Fragment, EventModuleData_UserlikeChatbotModule_Fragment, EventModuleData_VehicleDataWithPorscheCodeIntegrationModule_Fragment, EventModuleData_VisitAppointmentModule_Fragment, EventModuleData_WebsiteModule_Fragment, EventModuleData_WhatsappLiveChatModule_Fragment } from '../fragments/EventModuleData';
import type { CompanyInModuleOptionDataFragment } from '../fragments/CompanyInModuleOptionData';
import type { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragment } from '../fragments/VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import type { LocalCustomerManagementModuleKycFieldSpecsFragment } from '../fragments/LocalCustomerManagementModuleKycFieldSpecs';
import type { AppointmentTimeSlotDataFragment } from '../fragments/AppointmentTimeSlotData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import type { TimeSlotDataFragment } from '../fragments/TimeSlotData';
import type { TranslatedStringSpecsFragment } from '../fragments/TranslatedStringSpecs';
import type { DealershipSettingSpecData_DealershipMyInfoSetting_Fragment, DealershipSettingSpecData_DealershipPaymentSetting_Fragment, DealershipSettingSpecData_DealershipPublicSalesPerson_Fragment } from '../fragments/DealershipSettingSpecData';
import type { DepositAmountDataFragment } from '../fragments/DepositAmountData';
import type { SimpleVersioningDataFragment } from '../fragments/SimpleVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from '../fragments/AuthorData';
import type { KycPresetsSpecFragment } from '../fragments/KYCPresetsSpec';
import type { ConditionSpecs_ApplicationModuleCondition_Fragment, ConditionSpecs_BankCondition_Fragment, ConditionSpecs_ContextualCondition_Fragment, ConditionSpecs_DealerCondition_Fragment, ConditionSpecs_GiftVoucherCondition_Fragment, ConditionSpecs_InsurerCondition_Fragment, ConditionSpecs_LocationCondition_Fragment, ConditionSpecs_LogicCondition_Fragment, ConditionSpecs_SalesOfferAgreementsCondition_Fragment } from '../fragments/ConditionSpecs';
import type { BaseConditionSpecs_ApplicationModuleCondition_Fragment, BaseConditionSpecs_BankCondition_Fragment, BaseConditionSpecs_ContextualCondition_Fragment, BaseConditionSpecs_DealerCondition_Fragment, BaseConditionSpecs_GiftVoucherCondition_Fragment, BaseConditionSpecs_InsurerCondition_Fragment, BaseConditionSpecs_LocationCondition_Fragment, BaseConditionSpecs_LogicCondition_Fragment, BaseConditionSpecs_SalesOfferAgreementsCondition_Fragment } from '../fragments/BaseConditionSpecs';
import type { MobilityLocationDataFragment } from '../fragments/MobilityLocationData';
import type { UserPreviewDataFragment } from '../fragments/UserPreviewData';
import type { KycExtraSettingsSpecsFragment } from '../fragments/KYCExtraSettingsSpecs';
import type { CustomizedFieldDataFragment } from '../fragments/CustomizedFieldData';
import type { UploadFileWithPreviewFormDataFragment } from '../fragments/UploadFileWithPreviewFormData';
import type { EventEmailContentSpecsFragment, EventApplicationModuleEmailContentSpecsFragment } from '../fragments/EventApplicationModuleEmailContentSpecs';
import type { TranslatedTextDataFragment } from '../fragments/TranslationTextData';
import type { AppointmentModuleEmailContentSpecsFragment, AppointmentModuleEmailContentsSpecsFragment, AppointmentModuleEmailContentCustomerSpecsFragment, AppointmentModuleEmailContentSalesPersonSpecsFragment, AppointmentModuleEmailContentFinderReservationSpecsFragment } from '../fragments/AppointmentModuleEmailContentsSpecs';
import type { DealerTranslatedStringSettingDataFragment } from '../fragments/DealerTranslatedStringData';
import type { DealerBooleanSettingDataFragment } from '../fragments/DealerBooleanSettingData';
import type { ThankYouPageContentSpecsFragment } from '../fragments/ThankYouPageContent';
import type { CustomTestDriveBookingSlotsDataFragment } from '../fragments/CustomTestDriveBookingSlotsData';
import type { TestDriveFixedPeriodDataFragment } from '../fragments/TestDriveFixedPeriodData';
import type { TestDriveBookingWindowSettingsDataFragment } from '../fragments/TestDriveBookingWindowSettingsData';
import { gql } from '@apollo/client';
import { EventDataFragmentDoc } from '../fragments/EventData';
import { EventModuleDataFragmentDoc } from '../fragments/EventModuleData';
import { CompanyInModuleOptionDataFragmentDoc } from '../fragments/CompanyInModuleOptionData';
import { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragmentDoc } from '../fragments/VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import { LocalCustomerManagementModuleKycFieldSpecsFragmentDoc } from '../fragments/LocalCustomerManagementModuleKycFieldSpecs';
import { AppointmentTimeSlotDataFragmentDoc } from '../fragments/AppointmentTimeSlotData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import { TimeSlotDataFragmentDoc } from '../fragments/TimeSlotData';
import { TranslatedStringSpecsFragmentDoc } from '../fragments/TranslatedStringSpecs';
import { DealershipSettingSpecDataFragmentDoc } from '../fragments/DealershipSettingSpecData';
import { DepositAmountDataFragmentDoc } from '../fragments/DepositAmountData';
import { SimpleVersioningDataFragmentDoc } from '../fragments/SimpleVersioningData';
import { AuthorDataFragmentDoc } from '../fragments/AuthorData';
import { KycPresetsSpecFragmentDoc } from '../fragments/KYCPresetsSpec';
import { ConditionSpecsFragmentDoc } from '../fragments/ConditionSpecs';
import { BaseConditionSpecsFragmentDoc } from '../fragments/BaseConditionSpecs';
import { MobilityLocationDataFragmentDoc } from '../fragments/MobilityLocationData';
import { UserPreviewDataFragmentDoc } from '../fragments/UserPreviewData';
import { KycExtraSettingsSpecsFragmentDoc } from '../fragments/KYCExtraSettingsSpecs';
import { CustomizedFieldDataFragmentDoc } from '../fragments/CustomizedFieldData';
import { UploadFileWithPreviewFormDataFragmentDoc } from '../fragments/UploadFileWithPreviewFormData';
import { EventEmailContentSpecsFragmentDoc, EventApplicationModuleEmailContentSpecsFragmentDoc } from '../fragments/EventApplicationModuleEmailContentSpecs';
import { TranslatedTextDataFragmentDoc } from '../fragments/TranslationTextData';
import { AppointmentModuleEmailContentSpecsFragmentDoc, AppointmentModuleEmailContentsSpecsFragmentDoc, AppointmentModuleEmailContentCustomerSpecsFragmentDoc, AppointmentModuleEmailContentSalesPersonSpecsFragmentDoc, AppointmentModuleEmailContentFinderReservationSpecsFragmentDoc } from '../fragments/AppointmentModuleEmailContentsSpecs';
import { DealerTranslatedStringSettingDataFragmentDoc } from '../fragments/DealerTranslatedStringData';
import { DealerBooleanSettingDataFragmentDoc } from '../fragments/DealerBooleanSettingData';
import { ThankYouPageContentSpecsFragmentDoc } from '../fragments/ThankYouPageContent';
import { CustomTestDriveBookingSlotsDataFragmentDoc } from '../fragments/CustomTestDriveBookingSlotsData';
import { TestDriveFixedPeriodDataFragmentDoc } from '../fragments/TestDriveFixedPeriodData';
import { TestDriveBookingWindowSettingsDataFragmentDoc } from '../fragments/TestDriveBookingWindowSettingsData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type DeleteEventLevelAssetMutationVariables = SchemaTypes.Exact<{
  id: SchemaTypes.Scalars['ObjectID']['input'];
  type: SchemaTypes.EventModuleAsset;
}>;


export type DeleteEventLevelAssetMutation = (
  { __typename: 'Mutation' }
  & { event?: SchemaTypes.Maybe<(
    { __typename: 'Event' }
    & EventDataFragment
  )> }
);


export const DeleteEventLevelAssetDocument = /*#__PURE__*/ gql`
    mutation deleteEventLevelAsset($id: ObjectID!, $type: EventModuleAsset!) {
  event: deleteEventLevelAsset(id: $id, type: $type) {
    ...EventData
  }
}
    ${EventDataFragmentDoc}
${EventModuleDataFragmentDoc}
${CompanyInModuleOptionDataFragmentDoc}
${VehicleDataWithPorscheCodeIntegrationSettingSpecsFragmentDoc}
${LocalCustomerManagementModuleKycFieldSpecsFragmentDoc}
${AppointmentTimeSlotDataFragmentDoc}
${TranslatedStringDataFragmentDoc}
${TimeSlotDataFragmentDoc}
${TranslatedStringSpecsFragmentDoc}
${DealershipSettingSpecDataFragmentDoc}
${DepositAmountDataFragmentDoc}
${SimpleVersioningDataFragmentDoc}
${AuthorDataFragmentDoc}
${KycPresetsSpecFragmentDoc}
${ConditionSpecsFragmentDoc}
${BaseConditionSpecsFragmentDoc}
${MobilityLocationDataFragmentDoc}
${UserPreviewDataFragmentDoc}
${KycExtraSettingsSpecsFragmentDoc}
${CustomizedFieldDataFragmentDoc}
${UploadFileWithPreviewFormDataFragmentDoc}
${EventEmailContentSpecsFragmentDoc}
${TranslatedTextDataFragmentDoc}
${AppointmentModuleEmailContentSpecsFragmentDoc}
${DealerTranslatedStringSettingDataFragmentDoc}
${DealerBooleanSettingDataFragmentDoc}
${ThankYouPageContentSpecsFragmentDoc}
${CustomTestDriveBookingSlotsDataFragmentDoc}
${TestDriveFixedPeriodDataFragmentDoc}
${TestDriveBookingWindowSettingsDataFragmentDoc}`;
export type DeleteEventLevelAssetMutationFn = Apollo.MutationFunction<DeleteEventLevelAssetMutation, DeleteEventLevelAssetMutationVariables>;

/**
 * __useDeleteEventLevelAssetMutation__
 *
 * To run a mutation, you first call `useDeleteEventLevelAssetMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeleteEventLevelAssetMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deleteEventLevelAssetMutation, { data, loading, error }] = useDeleteEventLevelAssetMutation({
 *   variables: {
 *      id: // value for 'id'
 *      type: // value for 'type'
 *   },
 * });
 */
export function useDeleteEventLevelAssetMutation(baseOptions?: Apollo.MutationHookOptions<DeleteEventLevelAssetMutation, DeleteEventLevelAssetMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<DeleteEventLevelAssetMutation, DeleteEventLevelAssetMutationVariables>(DeleteEventLevelAssetDocument, options);
      }
export type DeleteEventLevelAssetMutationHookResult = ReturnType<typeof useDeleteEventLevelAssetMutation>;
export type DeleteEventLevelAssetMutationResult = Apollo.MutationResult<DeleteEventLevelAssetMutation>;
export type DeleteEventLevelAssetMutationOptions = Apollo.BaseMutationOptions<DeleteEventLevelAssetMutation, DeleteEventLevelAssetMutationVariables>;