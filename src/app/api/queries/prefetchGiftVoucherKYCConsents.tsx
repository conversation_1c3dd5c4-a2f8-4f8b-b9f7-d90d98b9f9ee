import type * as SchemaTypes from '../types';

import type { PrefetchKycConsentsDataFragment } from '../fragments/PrefetchKYCConsentsData';
import type { KycFieldSpecsFragment } from '../fragments/KYCFieldSpecs';
import type { ApplicationAgreementData_CheckboxApplicationAgreement_Fragment, ApplicationAgreementData_MarketingApplicationAgreement_Fragment, ApplicationAgreementData_TextApplicationAgreement_Fragment } from '../fragments/ApplicationAgreementData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import type { ConditionSpecs_ApplicationModuleCondition_Fragment, ConditionSpecs_BankCondition_Fragment, ConditionSpecs_ContextualCondition_Fragment, ConditionSpecs_DealerCondition_Fragment, ConditionSpecs_GiftVoucherCondition_Fragment, ConditionSpecs_InsurerCondition_Fragment, ConditionSpecs_LocationCondition_Fragment, ConditionSpecs_LogicCondition_Fragment, ConditionSpecs_SalesOfferAgreementsCondition_Fragment } from '../fragments/ConditionSpecs';
import type { BaseConditionSpecs_ApplicationModuleCondition_Fragment, BaseConditionSpecs_BankCondition_Fragment, BaseConditionSpecs_ContextualCondition_Fragment, BaseConditionSpecs_DealerCondition_Fragment, BaseConditionSpecs_GiftVoucherCondition_Fragment, BaseConditionSpecs_InsurerCondition_Fragment, BaseConditionSpecs_LocationCondition_Fragment, BaseConditionSpecs_LogicCondition_Fragment, BaseConditionSpecs_SalesOfferAgreementsCondition_Fragment } from '../fragments/BaseConditionSpecs';
import type { MobilityLocationDataFragment } from '../fragments/MobilityLocationData';
import type { UserPreviewDataFragment } from '../fragments/UserPreviewData';
import type { MarketingPlatformSpecsFragment } from '../fragments/MarketingPlatformSpecs';
import type { MarketingPlatformsAgreedSpecsFragment } from '../fragments/MarketingPlatformsAgreedSpecs';
import { gql } from '@apollo/client';
import { PrefetchKycConsentsDataFragmentDoc } from '../fragments/PrefetchKYCConsentsData';
import { KycFieldSpecsFragmentDoc } from '../fragments/KYCFieldSpecs';
import { ApplicationAgreementDataFragmentDoc } from '../fragments/ApplicationAgreementData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import { ConditionSpecsFragmentDoc } from '../fragments/ConditionSpecs';
import { BaseConditionSpecsFragmentDoc } from '../fragments/BaseConditionSpecs';
import { MobilityLocationDataFragmentDoc } from '../fragments/MobilityLocationData';
import { UserPreviewDataFragmentDoc } from '../fragments/UserPreviewData';
import { MarketingPlatformSpecsFragmentDoc } from '../fragments/MarketingPlatformSpecs';
import { MarketingPlatformsAgreedSpecsFragmentDoc } from '../fragments/MarketingPlatformsAgreedSpecs';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type PrefetchGiftVoucherKycConsentsQueryVariables = SchemaTypes.Exact<{
  mobilityModuleId: SchemaTypes.Scalars['ObjectID']['input'];
}>;


export type PrefetchGiftVoucherKycConsentsQuery = (
  { __typename: 'Query' }
  & { applicant: (
    { __typename: 'PrefetchKYCConsents' }
    & PrefetchKycConsentsDataFragment
  ) }
);


export const PrefetchGiftVoucherKycConsentsDocument = /*#__PURE__*/ gql`
    query prefetchGiftVoucherKYCConsents($mobilityModuleId: ObjectID!) {
  applicant: prefetchGiftVoucherKYCConsents(
    mobilityModuleId: $mobilityModuleId
    customerKind: "local"
  ) {
    ...PrefetchKYCConsentsData
  }
}
    ${PrefetchKycConsentsDataFragmentDoc}
${KycFieldSpecsFragmentDoc}
${ApplicationAgreementDataFragmentDoc}
${TranslatedStringDataFragmentDoc}
${ConditionSpecsFragmentDoc}
${BaseConditionSpecsFragmentDoc}
${MobilityLocationDataFragmentDoc}
${UserPreviewDataFragmentDoc}
${MarketingPlatformSpecsFragmentDoc}
${MarketingPlatformsAgreedSpecsFragmentDoc}`;

/**
 * __usePrefetchGiftVoucherKycConsentsQuery__
 *
 * To run a query within a React component, call `usePrefetchGiftVoucherKycConsentsQuery` and pass it any options that fit your needs.
 * When your component renders, `usePrefetchGiftVoucherKycConsentsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = usePrefetchGiftVoucherKycConsentsQuery({
 *   variables: {
 *      mobilityModuleId: // value for 'mobilityModuleId'
 *   },
 * });
 */
export function usePrefetchGiftVoucherKycConsentsQuery(baseOptions: Apollo.QueryHookOptions<PrefetchGiftVoucherKycConsentsQuery, PrefetchGiftVoucherKycConsentsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<PrefetchGiftVoucherKycConsentsQuery, PrefetchGiftVoucherKycConsentsQueryVariables>(PrefetchGiftVoucherKycConsentsDocument, options);
      }
export function usePrefetchGiftVoucherKycConsentsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<PrefetchGiftVoucherKycConsentsQuery, PrefetchGiftVoucherKycConsentsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<PrefetchGiftVoucherKycConsentsQuery, PrefetchGiftVoucherKycConsentsQueryVariables>(PrefetchGiftVoucherKycConsentsDocument, options);
        }
export type PrefetchGiftVoucherKycConsentsQueryHookResult = ReturnType<typeof usePrefetchGiftVoucherKycConsentsQuery>;
export type PrefetchGiftVoucherKycConsentsLazyQueryHookResult = ReturnType<typeof usePrefetchGiftVoucherKycConsentsLazyQuery>;
export type PrefetchGiftVoucherKycConsentsQueryResult = Apollo.QueryResult<PrefetchGiftVoucherKycConsentsQuery, PrefetchGiftVoucherKycConsentsQueryVariables>;