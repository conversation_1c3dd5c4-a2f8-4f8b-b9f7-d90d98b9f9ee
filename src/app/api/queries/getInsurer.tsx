import type * as SchemaTypes from '../types';

import type { InsurerDetailsDataFragment } from '../fragments/InsurerDetailsData';
import type { TranslatedStringSpecsFragment } from '../fragments/TranslatedStringSpecs';
import type { InsurerIntegrationData_EazyInsurerIntegration_Fragment, InsurerIntegrationData_EmailInsurerIntegration_Fragment } from '../fragments/InsurerIntegrationData';
import type { DealerDisclaimersConfiguratorDataFragment } from '../fragments/DealerPriceDisclaimerConfiguratorData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import type { InsuranceProductDetails_Eazy_Fragment, InsuranceProductDetails_ErgoLookupTable_Fragment } from '../fragments/InsuranceProductDetails';
import type { EazyDetailsFragment } from '../fragments/EazyDetails';
import type { ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment, ModulesCompanyTimezoneData_AppointmentModule_Fragment, ModulesCompanyTimezoneData_AutoplayModule_Fragment, ModulesCompanyTimezoneData_BankModule_Fragment, ModulesCompanyTimezoneData_BasicSigningModule_Fragment, ModulesCompanyTimezoneData_CapModule_Fragment, ModulesCompanyTimezoneData_ConfiguratorModule_Fragment, ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment, ModulesCompanyTimezoneData_CtsModule_Fragment, ModulesCompanyTimezoneData_DocusignModule_Fragment, ModulesCompanyTimezoneData_EventApplicationModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment, ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_FiservPaymentModule_Fragment, ModulesCompanyTimezoneData_GiftVoucherModule_Fragment, ModulesCompanyTimezoneData_InsuranceModule_Fragment, ModulesCompanyTimezoneData_LabelsModule_Fragment, ModulesCompanyTimezoneData_LaunchPadModule_Fragment, ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment, ModulesCompanyTimezoneData_MaintenanceModule_Fragment, ModulesCompanyTimezoneData_MarketingModule_Fragment, ModulesCompanyTimezoneData_MobilityModule_Fragment, ModulesCompanyTimezoneData_MyInfoModule_Fragment, ModulesCompanyTimezoneData_NamirialSigningModule_Fragment, ModulesCompanyTimezoneData_OidcModule_Fragment, ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheIdModule_Fragment, ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment, ModulesCompanyTimezoneData_PorschePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheRetainModule_Fragment, ModulesCompanyTimezoneData_PromoCodeModule_Fragment, ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment, ModulesCompanyTimezoneData_SalesOfferModule_Fragment, ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_StandardApplicationModule_Fragment, ModulesCompanyTimezoneData_TradeInModule_Fragment, ModulesCompanyTimezoneData_TtbPaymentModule_Fragment, ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment, ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment, ModulesCompanyTimezoneData_WebsiteModule_Fragment, ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment } from '../fragments/ModulesCompanyTimezoneData';
import type { PeriodDataFragment } from '../fragments/PeriodData';
import type { ErgoLookupTableDetailsFragment } from '../fragments/ErgoLookupTableDetails';
import type { SimpleVersioningDataFragment } from '../fragments/SimpleVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from '../fragments/AuthorData';
import { gql } from '@apollo/client';
import { InsurerDetailsDataFragmentDoc } from '../fragments/InsurerDetailsData';
import { TranslatedStringSpecsFragmentDoc } from '../fragments/TranslatedStringSpecs';
import { InsurerIntegrationDataFragmentDoc } from '../fragments/InsurerIntegrationData';
import { DealerDisclaimersConfiguratorDataFragmentDoc } from '../fragments/DealerPriceDisclaimerConfiguratorData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import { InsuranceProductDetailsFragmentDoc } from '../fragments/InsuranceProductDetails';
import { EazyDetailsFragmentDoc } from '../fragments/EazyDetails';
import { ModulesCompanyTimezoneDataFragmentDoc } from '../fragments/ModulesCompanyTimezoneData';
import { PeriodDataFragmentDoc } from '../fragments/PeriodData';
import { ErgoLookupTableDetailsFragmentDoc } from '../fragments/ErgoLookupTableDetails';
import { SimpleVersioningDataFragmentDoc } from '../fragments/SimpleVersioningData';
import { AuthorDataFragmentDoc } from '../fragments/AuthorData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GetInsurerQueryVariables = SchemaTypes.Exact<{
  id: SchemaTypes.Scalars['ObjectID']['input'];
}>;


export type GetInsurerQuery = (
  { __typename: 'Query' }
  & { insurer?: SchemaTypes.Maybe<(
    { __typename: 'Insurer' }
    & InsurerDetailsDataFragment
  )> }
);


export const GetInsurerDocument = /*#__PURE__*/ gql`
    query getInsurer($id: ObjectID!) {
  insurer: getInsurer(id: $id) {
    ...InsurerDetailsData
  }
}
    ${InsurerDetailsDataFragmentDoc}
${TranslatedStringSpecsFragmentDoc}
${InsurerIntegrationDataFragmentDoc}
${DealerDisclaimersConfiguratorDataFragmentDoc}
${TranslatedStringDataFragmentDoc}
${InsuranceProductDetailsFragmentDoc}
${EazyDetailsFragmentDoc}
${ModulesCompanyTimezoneDataFragmentDoc}
${PeriodDataFragmentDoc}
${ErgoLookupTableDetailsFragmentDoc}
${SimpleVersioningDataFragmentDoc}
${AuthorDataFragmentDoc}`;

/**
 * __useGetInsurerQuery__
 *
 * To run a query within a React component, call `useGetInsurerQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetInsurerQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetInsurerQuery({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useGetInsurerQuery(baseOptions: Apollo.QueryHookOptions<GetInsurerQuery, GetInsurerQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetInsurerQuery, GetInsurerQueryVariables>(GetInsurerDocument, options);
      }
export function useGetInsurerLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetInsurerQuery, GetInsurerQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetInsurerQuery, GetInsurerQueryVariables>(GetInsurerDocument, options);
        }
export type GetInsurerQueryHookResult = ReturnType<typeof useGetInsurerQuery>;
export type GetInsurerLazyQueryHookResult = ReturnType<typeof useGetInsurerLazyQuery>;
export type GetInsurerQueryResult = Apollo.QueryResult<GetInsurerQuery, GetInsurerQueryVariables>;