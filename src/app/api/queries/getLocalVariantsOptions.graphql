query getLocalVariantsOptions($filter: LocalVariantFilteringRule) {
    list: listLocalVariants(filter: $filter) {
        items {
            id
            identifier
            name {
                ...TranslatedStringData
            }

            ... on LocalVariant {
                __typename
                id
                identifier
                order
                isActive
                vehiclePrice
                moduleId
                name {
                    ...TranslatedStringData
                }

                description {
                    ...TranslatedStringData
                }

                images {
                    ...UploadFileWithPreviewFormData
                }

                modelId
                model {
                    ...LocalModelCalculatorSpecs
                }

                submodelId
                submodel {
                    ...LocalModelCalculatorSpecs
                }

                energyConsumption {
                    value
                    unit
                }
                powerOutput
                topSpeed
                engine {
                    ...TranslatedStringData
                }
                features {
                    ...TranslatedStringData
                }

                brochure {
                    ...UploadFileWithPreviewFormData
                }

                engineDisplacement
            }

            moduleId
            versioning {
                suiteId
                isLatest
            }
        }
    }
}
