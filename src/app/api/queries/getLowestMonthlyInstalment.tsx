import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type GetLowestMonthlyInstalmentQueryVariables = SchemaTypes.Exact<{
  data: SchemaTypes.LowestMonthlyInstalmentPayload;
}>;


export type GetLowestMonthlyInstalmentQuery = (
  { __typename: 'Query' }
  & { lowestMonthlyInstalment: SchemaTypes.Query['calculateLowestMonthlyInstalment'] }
);


export const GetLowestMonthlyInstalmentDocument = /*#__PURE__*/ gql`
    query getLowestMonthlyInstalment($data: LowestMonthlyInstalmentPayload!) {
  lowestMonthlyInstalment: calculateLowestMonthlyInstalment(data: $data)
}
    `;

/**
 * __useGetLowestMonthlyInstalmentQuery__
 *
 * To run a query within a React component, call `useGetLowestMonthlyInstalmentQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetLowestMonthlyInstalmentQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetLowestMonthlyInstalmentQuery({
 *   variables: {
 *      data: // value for 'data'
 *   },
 * });
 */
export function useGetLowestMonthlyInstalmentQuery(baseOptions: Apollo.QueryHookOptions<GetLowestMonthlyInstalmentQuery, GetLowestMonthlyInstalmentQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetLowestMonthlyInstalmentQuery, GetLowestMonthlyInstalmentQueryVariables>(GetLowestMonthlyInstalmentDocument, options);
      }
export function useGetLowestMonthlyInstalmentLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetLowestMonthlyInstalmentQuery, GetLowestMonthlyInstalmentQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetLowestMonthlyInstalmentQuery, GetLowestMonthlyInstalmentQueryVariables>(GetLowestMonthlyInstalmentDocument, options);
        }
export type GetLowestMonthlyInstalmentQueryHookResult = ReturnType<typeof useGetLowestMonthlyInstalmentQuery>;
export type GetLowestMonthlyInstalmentLazyQueryHookResult = ReturnType<typeof useGetLowestMonthlyInstalmentLazyQuery>;
export type GetLowestMonthlyInstalmentQueryResult = Apollo.QueryResult<GetLowestMonthlyInstalmentQuery, GetLowestMonthlyInstalmentQueryVariables>;