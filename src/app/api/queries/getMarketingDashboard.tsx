import type * as SchemaTypes from '../types';

import type { FeatureValueDataFragment } from '../fragments/FeatureValueData';
import { gql } from '@apollo/client';
import { FeatureValueDataFragmentDoc } from '../fragments/FeatureValueData';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type GetMarketingDashboardQueryVariables = SchemaTypes.Exact<{
  filter?: SchemaTypes.InputMaybe<SchemaTypes.MarketingDashboardFilterPayload>;
}>;


export type GetMarketingDashboardQuery = (
  { __typename: 'Query' }
  & { marketing?: SchemaTypes.Maybe<(
    { __typename: 'MarketingDashboard' }
    & { current: (
      { __typename: 'FeatureValue' }
      & FeatureValueDataFragment
    ), previous: (
      { __typename: 'FeatureValue' }
      & FeatureValueDataFragment
    ) }
  )> }
);


export const GetMarketingDashboardDocument = /*#__PURE__*/ gql`
    query getMarketingDashboard($filter: MarketingDashboardFilterPayload) {
  marketing: getMarketingDashboard(filter: $filter) {
    current {
      ...FeatureValueData
    }
    previous {
      ...FeatureValueData
    }
  }
}
    ${FeatureValueDataFragmentDoc}`;

/**
 * __useGetMarketingDashboardQuery__
 *
 * To run a query within a React component, call `useGetMarketingDashboardQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetMarketingDashboardQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetMarketingDashboardQuery({
 *   variables: {
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useGetMarketingDashboardQuery(baseOptions?: Apollo.QueryHookOptions<GetMarketingDashboardQuery, GetMarketingDashboardQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetMarketingDashboardQuery, GetMarketingDashboardQueryVariables>(GetMarketingDashboardDocument, options);
      }
export function useGetMarketingDashboardLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetMarketingDashboardQuery, GetMarketingDashboardQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetMarketingDashboardQuery, GetMarketingDashboardQueryVariables>(GetMarketingDashboardDocument, options);
        }
export type GetMarketingDashboardQueryHookResult = ReturnType<typeof useGetMarketingDashboardQuery>;
export type GetMarketingDashboardLazyQueryHookResult = ReturnType<typeof useGetMarketingDashboardLazyQuery>;
export type GetMarketingDashboardQueryResult = Apollo.QueryResult<GetMarketingDashboardQuery, GetMarketingDashboardQueryVariables>;