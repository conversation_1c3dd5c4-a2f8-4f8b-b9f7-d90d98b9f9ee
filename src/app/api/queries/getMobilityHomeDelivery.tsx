import type * as SchemaTypes from '../types';

import type { MobilityModuleSpecsFragment } from '../fragments/MobilityModuleSpecs';
import type { CompanyInModuleOptionDataFragment } from '../fragments/CompanyInModuleOptionData';
import type { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragment } from '../fragments/VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import type { SimpleVersioningDataFragment } from '../fragments/SimpleVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from '../fragments/AuthorData';
import type { DealerTranslatedStringSettingDataFragment } from '../fragments/DealerTranslatedStringData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import type { DealerBookingCodeSpecsFragment } from '../fragments/DealerBookingCodeSpecs';
import type { MobilitySigningSettingSpecsFragment } from '../fragments/MobilitySigningSettingSpecs';
import type { MobilityLocationDataFragment } from '../fragments/MobilityLocationData';
import type { UserPreviewDataFragment } from '../fragments/UserPreviewData';
import type { KycPresetsOptionsDataFragment } from '../fragments/KYCPresetsOptionsData';
import type { MobilityModuleEmailScenarioContentSpecsFragment } from '../fragments/MobilityModuleEmailScenarioContentSpecs';
import type { MobilityCustomerEmailContentDataFragment } from '../fragments/MobilityCustomerEmailContentData';
import type { MobilityEmailContentDataFragment } from '../fragments/MobilityEmailContentData';
import type { TranslatedTextDataFragment } from '../fragments/TranslationTextData';
import type { UploadFileWithPreviewFormDataFragment } from '../fragments/UploadFileWithPreviewFormData';
import type { MobilityOperatorEmailContentDataFragment } from '../fragments/MobilityOperatorEmailContentData';
import type { MobilityHomeDeliveryDataFragment } from '../fragments/MobilityHomeDeliveryData';
import type { CounterSettingsSpecsFragment } from '../fragments/CounterSettingsSpecs';
import { gql } from '@apollo/client';
import { MobilityModuleSpecsFragmentDoc } from '../fragments/MobilityModuleSpecs';
import { CompanyInModuleOptionDataFragmentDoc } from '../fragments/CompanyInModuleOptionData';
import { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragmentDoc } from '../fragments/VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import { SimpleVersioningDataFragmentDoc } from '../fragments/SimpleVersioningData';
import { AuthorDataFragmentDoc } from '../fragments/AuthorData';
import { DealerTranslatedStringSettingDataFragmentDoc } from '../fragments/DealerTranslatedStringData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import { DealerBookingCodeSpecsFragmentDoc } from '../fragments/DealerBookingCodeSpecs';
import { MobilitySigningSettingSpecsFragmentDoc } from '../fragments/MobilitySigningSettingSpecs';
import { MobilityLocationDataFragmentDoc } from '../fragments/MobilityLocationData';
import { UserPreviewDataFragmentDoc } from '../fragments/UserPreviewData';
import { KycPresetsOptionsDataFragmentDoc } from '../fragments/KYCPresetsOptionsData';
import { MobilityModuleEmailScenarioContentSpecsFragmentDoc } from '../fragments/MobilityModuleEmailScenarioContentSpecs';
import { MobilityCustomerEmailContentDataFragmentDoc } from '../fragments/MobilityCustomerEmailContentData';
import { MobilityEmailContentDataFragmentDoc } from '../fragments/MobilityEmailContentData';
import { TranslatedTextDataFragmentDoc } from '../fragments/TranslationTextData';
import { UploadFileWithPreviewFormDataFragmentDoc } from '../fragments/UploadFileWithPreviewFormData';
import { MobilityOperatorEmailContentDataFragmentDoc } from '../fragments/MobilityOperatorEmailContentData';
import { MobilityHomeDeliveryDataFragmentDoc } from '../fragments/MobilityHomeDeliveryData';
import { CounterSettingsSpecsFragmentDoc } from '../fragments/CounterSettingsSpecs';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GetMobilityHomeDeliveryQueryVariables = SchemaTypes.Exact<{
  deliveryId: SchemaTypes.Scalars['ObjectID']['input'];
}>;


export type GetMobilityHomeDeliveryQuery = (
  { __typename: 'Query' }
  & { delivery?: SchemaTypes.Maybe<(
    { __typename: 'MobilityModule' }
    & MobilityModuleSpecsFragment
  )> }
);


export const GetMobilityHomeDeliveryDocument = /*#__PURE__*/ gql`
    query getMobilityHomeDelivery($deliveryId: ObjectID!) {
  delivery: getMobilityHomeDelivery(deliveryId: $deliveryId) {
    ...MobilityModuleSpecs
  }
}
    ${MobilityModuleSpecsFragmentDoc}
${CompanyInModuleOptionDataFragmentDoc}
${VehicleDataWithPorscheCodeIntegrationSettingSpecsFragmentDoc}
${SimpleVersioningDataFragmentDoc}
${AuthorDataFragmentDoc}
${DealerTranslatedStringSettingDataFragmentDoc}
${TranslatedStringDataFragmentDoc}
${DealerBookingCodeSpecsFragmentDoc}
${MobilitySigningSettingSpecsFragmentDoc}
${MobilityLocationDataFragmentDoc}
${UserPreviewDataFragmentDoc}
${KycPresetsOptionsDataFragmentDoc}
${MobilityModuleEmailScenarioContentSpecsFragmentDoc}
${MobilityCustomerEmailContentDataFragmentDoc}
${MobilityEmailContentDataFragmentDoc}
${TranslatedTextDataFragmentDoc}
${UploadFileWithPreviewFormDataFragmentDoc}
${MobilityOperatorEmailContentDataFragmentDoc}
${MobilityHomeDeliveryDataFragmentDoc}
${CounterSettingsSpecsFragmentDoc}`;

/**
 * __useGetMobilityHomeDeliveryQuery__
 *
 * To run a query within a React component, call `useGetMobilityHomeDeliveryQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetMobilityHomeDeliveryQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetMobilityHomeDeliveryQuery({
 *   variables: {
 *      deliveryId: // value for 'deliveryId'
 *   },
 * });
 */
export function useGetMobilityHomeDeliveryQuery(baseOptions: Apollo.QueryHookOptions<GetMobilityHomeDeliveryQuery, GetMobilityHomeDeliveryQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetMobilityHomeDeliveryQuery, GetMobilityHomeDeliveryQueryVariables>(GetMobilityHomeDeliveryDocument, options);
      }
export function useGetMobilityHomeDeliveryLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetMobilityHomeDeliveryQuery, GetMobilityHomeDeliveryQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetMobilityHomeDeliveryQuery, GetMobilityHomeDeliveryQueryVariables>(GetMobilityHomeDeliveryDocument, options);
        }
export type GetMobilityHomeDeliveryQueryHookResult = ReturnType<typeof useGetMobilityHomeDeliveryQuery>;
export type GetMobilityHomeDeliveryLazyQueryHookResult = ReturnType<typeof useGetMobilityHomeDeliveryLazyQuery>;
export type GetMobilityHomeDeliveryQueryResult = Apollo.QueryResult<GetMobilityHomeDeliveryQuery, GetMobilityHomeDeliveryQueryVariables>;