import type * as SchemaTypes from '../types';

import type { AuditTrailDetailsFragment } from '../fragments/AuditTrailDetails';
import { gql } from '@apollo/client';
import { AuditTrailDetailsFragmentDoc } from '../fragments/AuditTrailDetails';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GetInventoryAuditTrailsQueryVariables = SchemaTypes.Exact<{
  inventoryId: SchemaTypes.Scalars['ObjectID']['input'];
  pagination?: SchemaTypes.InputMaybe<SchemaTypes.Pagination>;
}>;


export type GetInventoryAuditTrailsQuery = (
  { __typename: 'Query' }
  & { result: (
    { __typename: 'PaginatedAuditTrails' }
    & Pick<SchemaTypes.PaginatedAuditTrails, 'count'>
    & { items: Array<(
      { __typename: 'AuditTrail' }
      & AuditTrailDetailsFragment
    )> }
  ) }
);


export const GetInventoryAuditTrailsDocument = /*#__PURE__*/ gql`
    query getInventoryAuditTrails($inventoryId: ObjectID!, $pagination: Pagination) {
  result: getInventoryAuditTrails(
    inventoryId: $inventoryId
    pagination: $pagination
  ) {
    count
    items {
      ...AuditTrailDetails
    }
  }
}
    ${AuditTrailDetailsFragmentDoc}`;

/**
 * __useGetInventoryAuditTrailsQuery__
 *
 * To run a query within a React component, call `useGetInventoryAuditTrailsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetInventoryAuditTrailsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetInventoryAuditTrailsQuery({
 *   variables: {
 *      inventoryId: // value for 'inventoryId'
 *      pagination: // value for 'pagination'
 *   },
 * });
 */
export function useGetInventoryAuditTrailsQuery(baseOptions: Apollo.QueryHookOptions<GetInventoryAuditTrailsQuery, GetInventoryAuditTrailsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetInventoryAuditTrailsQuery, GetInventoryAuditTrailsQueryVariables>(GetInventoryAuditTrailsDocument, options);
      }
export function useGetInventoryAuditTrailsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetInventoryAuditTrailsQuery, GetInventoryAuditTrailsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetInventoryAuditTrailsQuery, GetInventoryAuditTrailsQueryVariables>(GetInventoryAuditTrailsDocument, options);
        }
export type GetInventoryAuditTrailsQueryHookResult = ReturnType<typeof useGetInventoryAuditTrailsQuery>;
export type GetInventoryAuditTrailsLazyQueryHookResult = ReturnType<typeof useGetInventoryAuditTrailsLazyQuery>;
export type GetInventoryAuditTrailsQueryResult = Apollo.QueryResult<GetInventoryAuditTrailsQuery, GetInventoryAuditTrailsQueryVariables>;