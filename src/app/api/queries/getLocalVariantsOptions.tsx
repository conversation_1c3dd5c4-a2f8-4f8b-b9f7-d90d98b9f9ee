import type * as SchemaTypes from '../types';

import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import type { UploadFileWithPreviewFormDataFragment } from '../fragments/UploadFileWithPreviewFormData';
import type { LocalModelCalculatorSpecsFragment } from '../fragments/LocalModelCalculatorSpecs';
import type { LocalMakeCalculatorSpecsFragment } from '../fragments/LocalMakeCalculatorSpecs';
import { gql } from '@apollo/client';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import { UploadFileWithPreviewFormDataFragmentDoc } from '../fragments/UploadFileWithPreviewFormData';
import { LocalModelCalculatorSpecsFragmentDoc } from '../fragments/LocalModelCalculatorSpecs';
import { LocalMakeCalculatorSpecsFragmentDoc } from '../fragments/LocalMakeCalculatorSpecs';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GetLocalVariantsOptionsQueryVariables = SchemaTypes.Exact<{
  filter?: SchemaTypes.InputMaybe<SchemaTypes.LocalVariantFilteringRule>;
}>;


export type GetLocalVariantsOptionsQuery = (
  { __typename: 'Query' }
  & { list: (
    { __typename: 'PaginatedLocalVariants' }
    & { items: Array<(
      { __typename: 'LocalVariant' }
      & Pick<SchemaTypes.LocalVariant, 'id' | 'identifier' | 'order' | 'isActive' | 'vehiclePrice' | 'moduleId' | 'modelId' | 'submodelId' | 'powerOutput' | 'topSpeed' | 'engineDisplacement'>
      & { name: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
        & TranslatedStringDataFragment
      ), description: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), images?: SchemaTypes.Maybe<Array<SchemaTypes.Maybe<(
        { __typename: 'UploadedFileWithPreview' }
        & UploadFileWithPreviewFormDataFragment
      )>>>, model: (
        { __typename: 'LocalModel' }
        & LocalModelCalculatorSpecsFragment
      ), submodel?: SchemaTypes.Maybe<(
        { __typename: 'LocalModel' }
        & LocalModelCalculatorSpecsFragment
      )>, energyConsumption?: SchemaTypes.Maybe<(
        { __typename: 'EnergyConsumption' }
        & Pick<SchemaTypes.EnergyConsumption, 'value' | 'unit'>
      )>, engine: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), features: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), brochure?: SchemaTypes.Maybe<(
        { __typename: 'UploadedFileWithPreview' }
        & UploadFileWithPreviewFormDataFragment
      )>, versioning: (
        { __typename: 'AdvancedVersioning' }
        & Pick<SchemaTypes.AdvancedVersioning, 'suiteId' | 'isLatest'>
      ) }
    )> }
  ) }
);


export const GetLocalVariantsOptionsDocument = /*#__PURE__*/ gql`
    query getLocalVariantsOptions($filter: LocalVariantFilteringRule) {
  list: listLocalVariants(filter: $filter) {
    items {
      id
      identifier
      name {
        ...TranslatedStringData
      }
      ... on LocalVariant {
        __typename
        id
        identifier
        order
        isActive
        vehiclePrice
        moduleId
        name {
          ...TranslatedStringData
        }
        description {
          ...TranslatedStringData
        }
        images {
          ...UploadFileWithPreviewFormData
        }
        modelId
        model {
          ...LocalModelCalculatorSpecs
        }
        submodelId
        submodel {
          ...LocalModelCalculatorSpecs
        }
        energyConsumption {
          value
          unit
        }
        powerOutput
        topSpeed
        engine {
          ...TranslatedStringData
        }
        features {
          ...TranslatedStringData
        }
        brochure {
          ...UploadFileWithPreviewFormData
        }
        engineDisplacement
      }
      moduleId
      versioning {
        suiteId
        isLatest
      }
    }
  }
}
    ${TranslatedStringDataFragmentDoc}
${UploadFileWithPreviewFormDataFragmentDoc}
${LocalModelCalculatorSpecsFragmentDoc}
${LocalMakeCalculatorSpecsFragmentDoc}`;

/**
 * __useGetLocalVariantsOptionsQuery__
 *
 * To run a query within a React component, call `useGetLocalVariantsOptionsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetLocalVariantsOptionsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetLocalVariantsOptionsQuery({
 *   variables: {
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useGetLocalVariantsOptionsQuery(baseOptions?: Apollo.QueryHookOptions<GetLocalVariantsOptionsQuery, GetLocalVariantsOptionsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetLocalVariantsOptionsQuery, GetLocalVariantsOptionsQueryVariables>(GetLocalVariantsOptionsDocument, options);
      }
export function useGetLocalVariantsOptionsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetLocalVariantsOptionsQuery, GetLocalVariantsOptionsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetLocalVariantsOptionsQuery, GetLocalVariantsOptionsQueryVariables>(GetLocalVariantsOptionsDocument, options);
        }
export type GetLocalVariantsOptionsQueryHookResult = ReturnType<typeof useGetLocalVariantsOptionsQuery>;
export type GetLocalVariantsOptionsLazyQueryHookResult = ReturnType<typeof useGetLocalVariantsOptionsLazyQuery>;
export type GetLocalVariantsOptionsQueryResult = Apollo.QueryResult<GetLocalVariantsOptionsQuery, GetLocalVariantsOptionsQueryVariables>;