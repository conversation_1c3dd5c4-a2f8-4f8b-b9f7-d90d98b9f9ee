import type * as SchemaTypes from '../types';

import type { MobilityInventoryOptionFragment } from '../fragments/MobilityInventoryOption';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import { gql } from '@apollo/client';
import { MobilityInventoryOptionFragmentDoc } from '../fragments/MobilityInventoryOption';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GetInventoriesByMobilityModuleIdQueryVariables = SchemaTypes.Exact<{
  mobilityModuleId: SchemaTypes.Scalars['ObjectID']['input'];
}>;


export type GetInventoriesByMobilityModuleIdQuery = (
  { __typename: 'Query' }
  & { inventories: Array<(
    { __typename: 'MobilityInventory' }
    & MobilityInventoryOptionFragment
  )> }
);


export const GetInventoriesByMobilityModuleIdDocument = /*#__PURE__*/ gql`
    query getInventoriesByMobilityModuleId($mobilityModuleId: ObjectID!) {
  inventories: getInventoriesByMobilityModuleId(
    mobilityModuleId: $mobilityModuleId
  ) {
    ...MobilityInventoryOption
  }
}
    ${MobilityInventoryOptionFragmentDoc}
${TranslatedStringDataFragmentDoc}`;

/**
 * __useGetInventoriesByMobilityModuleIdQuery__
 *
 * To run a query within a React component, call `useGetInventoriesByMobilityModuleIdQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetInventoriesByMobilityModuleIdQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetInventoriesByMobilityModuleIdQuery({
 *   variables: {
 *      mobilityModuleId: // value for 'mobilityModuleId'
 *   },
 * });
 */
export function useGetInventoriesByMobilityModuleIdQuery(baseOptions: Apollo.QueryHookOptions<GetInventoriesByMobilityModuleIdQuery, GetInventoriesByMobilityModuleIdQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetInventoriesByMobilityModuleIdQuery, GetInventoriesByMobilityModuleIdQueryVariables>(GetInventoriesByMobilityModuleIdDocument, options);
      }
export function useGetInventoriesByMobilityModuleIdLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetInventoriesByMobilityModuleIdQuery, GetInventoriesByMobilityModuleIdQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetInventoriesByMobilityModuleIdQuery, GetInventoriesByMobilityModuleIdQueryVariables>(GetInventoriesByMobilityModuleIdDocument, options);
        }
export type GetInventoriesByMobilityModuleIdQueryHookResult = ReturnType<typeof useGetInventoriesByMobilityModuleIdQuery>;
export type GetInventoriesByMobilityModuleIdLazyQueryHookResult = ReturnType<typeof useGetInventoriesByMobilityModuleIdLazyQuery>;
export type GetInventoriesByMobilityModuleIdQueryResult = Apollo.QueryResult<GetInventoriesByMobilityModuleIdQuery, GetInventoriesByMobilityModuleIdQueryVariables>;