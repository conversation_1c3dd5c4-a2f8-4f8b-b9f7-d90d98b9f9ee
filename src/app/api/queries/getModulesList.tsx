import type * as SchemaTypes from '../types';

import type { GetModulesList_AdyenPaymentModule_Fragment, GetModulesList_AppointmentModule_Fragment, GetModulesList_AutoplayModule_Fragment, GetModulesList_BankModule_Fragment, GetModulesList_BasicSigningModule_Fragment, GetModulesList_CapModule_Fragment, GetModulesList_ConfiguratorModule_Fragment, GetModulesList_ConsentsAndDeclarationsModule_Fragment, GetModulesList_CtsModule_Fragment, GetModulesList_DocusignModule_Fragment, GetModulesList_EventApplicationModule_Fragment, GetModulesList_FinderApplicationPrivateModule_Fragment, GetModulesList_FinderApplicationPublicModule_Fragment, GetModulesList_FinderVehicleManagementModule_Fragment, GetModulesList_FiservPaymentModule_Fragment, GetModulesList_GiftVoucherModule_Fragment, GetModulesList_InsuranceModule_Fragment, GetModulesList_LabelsModule_Fragment, GetModulesList_LaunchPadModule_Fragment, GetModulesList_LocalCustomerManagementModule_Fragment, GetModulesList_MaintenanceModule_Fragment, GetModulesList_MarketingModule_Fragment, GetModulesList_MobilityModule_Fragment, GetModulesList_MyInfoModule_Fragment, GetModulesList_NamirialSigningModule_Fragment, GetModulesList_OidcModule_Fragment, GetModulesList_PayGatePaymentModule_Fragment, GetModulesList_PorscheIdModule_Fragment, GetModulesList_PorscheMasterDataModule_Fragment, GetModulesList_PorschePaymentModule_Fragment, GetModulesList_PorscheRetainModule_Fragment, GetModulesList_PromoCodeModule_Fragment, GetModulesList_SalesControlBoardModule_Fragment, GetModulesList_SalesOfferModule_Fragment, GetModulesList_SimpleVehicleManagementModule_Fragment, GetModulesList_StandardApplicationModule_Fragment, GetModulesList_TradeInModule_Fragment, GetModulesList_TtbPaymentModule_Fragment, GetModulesList_UserlikeChatbotModule_Fragment, GetModulesList_VehicleDataWithPorscheCodeIntegrationModule_Fragment, GetModulesList_VisitAppointmentModule_Fragment, GetModulesList_WebsiteModule_Fragment, GetModulesList_WhatsappLiveChatModule_Fragment } from '../fragments/GetModulesList';
import type { ModuleListData_AdyenPaymentModule_Fragment, ModuleListData_AppointmentModule_Fragment, ModuleListData_AutoplayModule_Fragment, ModuleListData_BankModule_Fragment, ModuleListData_BasicSigningModule_Fragment, ModuleListData_CapModule_Fragment, ModuleListData_ConfiguratorModule_Fragment, ModuleListData_ConsentsAndDeclarationsModule_Fragment, ModuleListData_CtsModule_Fragment, ModuleListData_DocusignModule_Fragment, ModuleListData_EventApplicationModule_Fragment, ModuleListData_FinderApplicationPrivateModule_Fragment, ModuleListData_FinderApplicationPublicModule_Fragment, ModuleListData_FinderVehicleManagementModule_Fragment, ModuleListData_FiservPaymentModule_Fragment, ModuleListData_GiftVoucherModule_Fragment, ModuleListData_InsuranceModule_Fragment, ModuleListData_LabelsModule_Fragment, ModuleListData_LaunchPadModule_Fragment, ModuleListData_LocalCustomerManagementModule_Fragment, ModuleListData_MaintenanceModule_Fragment, ModuleListData_MarketingModule_Fragment, ModuleListData_MobilityModule_Fragment, ModuleListData_MyInfoModule_Fragment, ModuleListData_NamirialSigningModule_Fragment, ModuleListData_OidcModule_Fragment, ModuleListData_PayGatePaymentModule_Fragment, ModuleListData_PorscheIdModule_Fragment, ModuleListData_PorscheMasterDataModule_Fragment, ModuleListData_PorschePaymentModule_Fragment, ModuleListData_PorscheRetainModule_Fragment, ModuleListData_PromoCodeModule_Fragment, ModuleListData_SalesControlBoardModule_Fragment, ModuleListData_SalesOfferModule_Fragment, ModuleListData_SimpleVehicleManagementModule_Fragment, ModuleListData_StandardApplicationModule_Fragment, ModuleListData_TradeInModule_Fragment, ModuleListData_TtbPaymentModule_Fragment, ModuleListData_UserlikeChatbotModule_Fragment, ModuleListData_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModuleListData_VisitAppointmentModule_Fragment, ModuleListData_WebsiteModule_Fragment, ModuleListData_WhatsappLiveChatModule_Fragment } from '../fragments/ModuleListData';
import type { MobilityLocationDataFragment } from '../fragments/MobilityLocationData';
import type { UserPreviewDataFragment } from '../fragments/UserPreviewData';
import { gql } from '@apollo/client';
import { GetModulesListFragmentDoc } from '../fragments/GetModulesList';
import { ModuleListDataFragmentDoc } from '../fragments/ModuleListData';
import { MobilityLocationDataFragmentDoc } from '../fragments/MobilityLocationData';
import { UserPreviewDataFragmentDoc } from '../fragments/UserPreviewData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GetModulesListQueryVariables = SchemaTypes.Exact<{
  filter?: SchemaTypes.InputMaybe<SchemaTypes.ModuleFilteringRule>;
}>;


export type GetModulesListQuery = (
  { __typename: 'Query' }
  & { modules: (
    { __typename: 'PaginatedModules' }
    & { items: Array<(
      { __typename: 'AdyenPaymentModule' }
      & GetModulesList_AdyenPaymentModule_Fragment
    ) | (
      { __typename: 'AppointmentModule' }
      & GetModulesList_AppointmentModule_Fragment
    ) | (
      { __typename: 'AutoplayModule' }
      & GetModulesList_AutoplayModule_Fragment
    ) | (
      { __typename: 'BankModule' }
      & GetModulesList_BankModule_Fragment
    ) | (
      { __typename: 'BasicSigningModule' }
      & GetModulesList_BasicSigningModule_Fragment
    ) | (
      { __typename: 'CapModule' }
      & GetModulesList_CapModule_Fragment
    ) | (
      { __typename: 'ConfiguratorModule' }
      & GetModulesList_ConfiguratorModule_Fragment
    ) | (
      { __typename: 'ConsentsAndDeclarationsModule' }
      & GetModulesList_ConsentsAndDeclarationsModule_Fragment
    ) | (
      { __typename: 'CtsModule' }
      & GetModulesList_CtsModule_Fragment
    ) | (
      { __typename: 'DocusignModule' }
      & GetModulesList_DocusignModule_Fragment
    ) | (
      { __typename: 'EventApplicationModule' }
      & GetModulesList_EventApplicationModule_Fragment
    ) | (
      { __typename: 'FinderApplicationPrivateModule' }
      & GetModulesList_FinderApplicationPrivateModule_Fragment
    ) | (
      { __typename: 'FinderApplicationPublicModule' }
      & GetModulesList_FinderApplicationPublicModule_Fragment
    ) | (
      { __typename: 'FinderVehicleManagementModule' }
      & GetModulesList_FinderVehicleManagementModule_Fragment
    ) | (
      { __typename: 'FiservPaymentModule' }
      & GetModulesList_FiservPaymentModule_Fragment
    ) | (
      { __typename: 'GiftVoucherModule' }
      & GetModulesList_GiftVoucherModule_Fragment
    ) | (
      { __typename: 'InsuranceModule' }
      & GetModulesList_InsuranceModule_Fragment
    ) | (
      { __typename: 'LabelsModule' }
      & GetModulesList_LabelsModule_Fragment
    ) | (
      { __typename: 'LaunchPadModule' }
      & GetModulesList_LaunchPadModule_Fragment
    ) | (
      { __typename: 'LocalCustomerManagementModule' }
      & GetModulesList_LocalCustomerManagementModule_Fragment
    ) | (
      { __typename: 'MaintenanceModule' }
      & GetModulesList_MaintenanceModule_Fragment
    ) | (
      { __typename: 'MarketingModule' }
      & GetModulesList_MarketingModule_Fragment
    ) | (
      { __typename: 'MobilityModule' }
      & GetModulesList_MobilityModule_Fragment
    ) | (
      { __typename: 'MyInfoModule' }
      & GetModulesList_MyInfoModule_Fragment
    ) | (
      { __typename: 'NamirialSigningModule' }
      & GetModulesList_NamirialSigningModule_Fragment
    ) | (
      { __typename: 'OIDCModule' }
      & GetModulesList_OidcModule_Fragment
    ) | (
      { __typename: 'PayGatePaymentModule' }
      & GetModulesList_PayGatePaymentModule_Fragment
    ) | (
      { __typename: 'PorscheIdModule' }
      & GetModulesList_PorscheIdModule_Fragment
    ) | (
      { __typename: 'PorscheMasterDataModule' }
      & GetModulesList_PorscheMasterDataModule_Fragment
    ) | (
      { __typename: 'PorschePaymentModule' }
      & GetModulesList_PorschePaymentModule_Fragment
    ) | (
      { __typename: 'PorscheRetainModule' }
      & GetModulesList_PorscheRetainModule_Fragment
    ) | (
      { __typename: 'PromoCodeModule' }
      & GetModulesList_PromoCodeModule_Fragment
    ) | (
      { __typename: 'SalesControlBoardModule' }
      & GetModulesList_SalesControlBoardModule_Fragment
    ) | (
      { __typename: 'SalesOfferModule' }
      & GetModulesList_SalesOfferModule_Fragment
    ) | (
      { __typename: 'SimpleVehicleManagementModule' }
      & GetModulesList_SimpleVehicleManagementModule_Fragment
    ) | (
      { __typename: 'StandardApplicationModule' }
      & GetModulesList_StandardApplicationModule_Fragment
    ) | (
      { __typename: 'TradeInModule' }
      & GetModulesList_TradeInModule_Fragment
    ) | (
      { __typename: 'TtbPaymentModule' }
      & GetModulesList_TtbPaymentModule_Fragment
    ) | (
      { __typename: 'UserlikeChatbotModule' }
      & GetModulesList_UserlikeChatbotModule_Fragment
    ) | (
      { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
      & GetModulesList_VehicleDataWithPorscheCodeIntegrationModule_Fragment
    ) | (
      { __typename: 'VisitAppointmentModule' }
      & GetModulesList_VisitAppointmentModule_Fragment
    ) | (
      { __typename: 'WebsiteModule' }
      & GetModulesList_WebsiteModule_Fragment
    ) | (
      { __typename: 'WhatsappLiveChatModule' }
      & GetModulesList_WhatsappLiveChatModule_Fragment
    )> }
  ) }
);


export const GetModulesListDocument = /*#__PURE__*/ gql`
    query getModulesList($filter: ModuleFilteringRule) {
  modules: listModules(filter: $filter) {
    items {
      ...GetModulesList
    }
  }
}
    ${GetModulesListFragmentDoc}
${ModuleListDataFragmentDoc}
${MobilityLocationDataFragmentDoc}
${UserPreviewDataFragmentDoc}`;

/**
 * __useGetModulesListQuery__
 *
 * To run a query within a React component, call `useGetModulesListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetModulesListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetModulesListQuery({
 *   variables: {
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useGetModulesListQuery(baseOptions?: Apollo.QueryHookOptions<GetModulesListQuery, GetModulesListQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetModulesListQuery, GetModulesListQueryVariables>(GetModulesListDocument, options);
      }
export function useGetModulesListLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetModulesListQuery, GetModulesListQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetModulesListQuery, GetModulesListQueryVariables>(GetModulesListDocument, options);
        }
export type GetModulesListQueryHookResult = ReturnType<typeof useGetModulesListQuery>;
export type GetModulesListLazyQueryHookResult = ReturnType<typeof useGetModulesListLazyQuery>;
export type GetModulesListQueryResult = Apollo.QueryResult<GetModulesListQuery, GetModulesListQueryVariables>;