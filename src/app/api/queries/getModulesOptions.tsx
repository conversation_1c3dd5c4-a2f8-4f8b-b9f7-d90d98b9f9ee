import type * as SchemaTypes from '../types';

import type { ModulesOptionsData_AdyenPaymentModule_Fragment, ModulesOptionsData_AppointmentModule_Fragment, ModulesOptionsData_AutoplayModule_Fragment, ModulesOptionsData_BankModule_Fragment, ModulesOptionsData_BasicSigningModule_Fragment, ModulesOptionsData_CapModule_Fragment, ModulesOptionsData_ConfiguratorModule_Fragment, ModulesOptionsData_ConsentsAndDeclarationsModule_Fragment, ModulesOptionsData_CtsModule_Fragment, ModulesOptionsData_DocusignModule_Fragment, ModulesOptionsData_EventApplicationModule_Fragment, ModulesOptionsData_FinderApplicationPrivateModule_Fragment, ModulesOptionsData_FinderApplicationPublicModule_Fragment, ModulesOptionsData_FinderVehicleManagementModule_Fragment, ModulesOptionsData_FiservPaymentModule_Fragment, ModulesOptionsData_GiftVoucherModule_Fragment, ModulesOptionsData_InsuranceModule_Fragment, ModulesOptionsData_LabelsModule_Fragment, ModulesOptionsData_LaunchPadModule_Fragment, ModulesOptionsData_LocalCustomerManagementModule_Fragment, ModulesOptionsData_MaintenanceModule_Fragment, ModulesOptionsData_MarketingModule_Fragment, ModulesOptionsData_MobilityModule_Fragment, ModulesOptionsData_MyInfoModule_Fragment, ModulesOptionsData_NamirialSigningModule_Fragment, ModulesOptionsData_OidcModule_Fragment, ModulesOptionsData_PayGatePaymentModule_Fragment, ModulesOptionsData_PorscheIdModule_Fragment, ModulesOptionsData_PorscheMasterDataModule_Fragment, ModulesOptionsData_PorschePaymentModule_Fragment, ModulesOptionsData_PorscheRetainModule_Fragment, ModulesOptionsData_PromoCodeModule_Fragment, ModulesOptionsData_SalesControlBoardModule_Fragment, ModulesOptionsData_SalesOfferModule_Fragment, ModulesOptionsData_SimpleVehicleManagementModule_Fragment, ModulesOptionsData_StandardApplicationModule_Fragment, ModulesOptionsData_TradeInModule_Fragment, ModulesOptionsData_TtbPaymentModule_Fragment, ModulesOptionsData_UserlikeChatbotModule_Fragment, ModulesOptionsData_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModulesOptionsData_VisitAppointmentModule_Fragment, ModulesOptionsData_WebsiteModule_Fragment, ModulesOptionsData_WhatsappLiveChatModule_Fragment } from '../fragments/ModulesOptionsData';
import { gql } from '@apollo/client';
import { ModulesOptionsDataFragmentDoc } from '../fragments/ModulesOptionsData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GetModulesOptionsQueryVariables = SchemaTypes.Exact<{
  filter?: SchemaTypes.InputMaybe<SchemaTypes.ModuleFilteringRule>;
}>;


export type GetModulesOptionsQuery = (
  { __typename: 'Query' }
  & { modules: (
    { __typename: 'PaginatedModules' }
    & { items: Array<(
      { __typename: 'AdyenPaymentModule' }
      & ModulesOptionsData_AdyenPaymentModule_Fragment
    ) | (
      { __typename: 'AppointmentModule' }
      & ModulesOptionsData_AppointmentModule_Fragment
    ) | (
      { __typename: 'AutoplayModule' }
      & ModulesOptionsData_AutoplayModule_Fragment
    ) | (
      { __typename: 'BankModule' }
      & ModulesOptionsData_BankModule_Fragment
    ) | (
      { __typename: 'BasicSigningModule' }
      & ModulesOptionsData_BasicSigningModule_Fragment
    ) | (
      { __typename: 'CapModule' }
      & ModulesOptionsData_CapModule_Fragment
    ) | (
      { __typename: 'ConfiguratorModule' }
      & ModulesOptionsData_ConfiguratorModule_Fragment
    ) | (
      { __typename: 'ConsentsAndDeclarationsModule' }
      & ModulesOptionsData_ConsentsAndDeclarationsModule_Fragment
    ) | (
      { __typename: 'CtsModule' }
      & ModulesOptionsData_CtsModule_Fragment
    ) | (
      { __typename: 'DocusignModule' }
      & ModulesOptionsData_DocusignModule_Fragment
    ) | (
      { __typename: 'EventApplicationModule' }
      & ModulesOptionsData_EventApplicationModule_Fragment
    ) | (
      { __typename: 'FinderApplicationPrivateModule' }
      & ModulesOptionsData_FinderApplicationPrivateModule_Fragment
    ) | (
      { __typename: 'FinderApplicationPublicModule' }
      & ModulesOptionsData_FinderApplicationPublicModule_Fragment
    ) | (
      { __typename: 'FinderVehicleManagementModule' }
      & ModulesOptionsData_FinderVehicleManagementModule_Fragment
    ) | (
      { __typename: 'FiservPaymentModule' }
      & ModulesOptionsData_FiservPaymentModule_Fragment
    ) | (
      { __typename: 'GiftVoucherModule' }
      & ModulesOptionsData_GiftVoucherModule_Fragment
    ) | (
      { __typename: 'InsuranceModule' }
      & ModulesOptionsData_InsuranceModule_Fragment
    ) | (
      { __typename: 'LabelsModule' }
      & ModulesOptionsData_LabelsModule_Fragment
    ) | (
      { __typename: 'LaunchPadModule' }
      & ModulesOptionsData_LaunchPadModule_Fragment
    ) | (
      { __typename: 'LocalCustomerManagementModule' }
      & ModulesOptionsData_LocalCustomerManagementModule_Fragment
    ) | (
      { __typename: 'MaintenanceModule' }
      & ModulesOptionsData_MaintenanceModule_Fragment
    ) | (
      { __typename: 'MarketingModule' }
      & ModulesOptionsData_MarketingModule_Fragment
    ) | (
      { __typename: 'MobilityModule' }
      & ModulesOptionsData_MobilityModule_Fragment
    ) | (
      { __typename: 'MyInfoModule' }
      & ModulesOptionsData_MyInfoModule_Fragment
    ) | (
      { __typename: 'NamirialSigningModule' }
      & ModulesOptionsData_NamirialSigningModule_Fragment
    ) | (
      { __typename: 'OIDCModule' }
      & ModulesOptionsData_OidcModule_Fragment
    ) | (
      { __typename: 'PayGatePaymentModule' }
      & ModulesOptionsData_PayGatePaymentModule_Fragment
    ) | (
      { __typename: 'PorscheIdModule' }
      & ModulesOptionsData_PorscheIdModule_Fragment
    ) | (
      { __typename: 'PorscheMasterDataModule' }
      & ModulesOptionsData_PorscheMasterDataModule_Fragment
    ) | (
      { __typename: 'PorschePaymentModule' }
      & ModulesOptionsData_PorschePaymentModule_Fragment
    ) | (
      { __typename: 'PorscheRetainModule' }
      & ModulesOptionsData_PorscheRetainModule_Fragment
    ) | (
      { __typename: 'PromoCodeModule' }
      & ModulesOptionsData_PromoCodeModule_Fragment
    ) | (
      { __typename: 'SalesControlBoardModule' }
      & ModulesOptionsData_SalesControlBoardModule_Fragment
    ) | (
      { __typename: 'SalesOfferModule' }
      & ModulesOptionsData_SalesOfferModule_Fragment
    ) | (
      { __typename: 'SimpleVehicleManagementModule' }
      & Pick<SchemaTypes.SimpleVehicleManagementModule, 'permissions' | 'porscheMasterDataModuleId'>
      & ModulesOptionsData_SimpleVehicleManagementModule_Fragment
    ) | (
      { __typename: 'StandardApplicationModule' }
      & ModulesOptionsData_StandardApplicationModule_Fragment
    ) | (
      { __typename: 'TradeInModule' }
      & ModulesOptionsData_TradeInModule_Fragment
    ) | (
      { __typename: 'TtbPaymentModule' }
      & ModulesOptionsData_TtbPaymentModule_Fragment
    ) | (
      { __typename: 'UserlikeChatbotModule' }
      & ModulesOptionsData_UserlikeChatbotModule_Fragment
    ) | (
      { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
      & ModulesOptionsData_VehicleDataWithPorscheCodeIntegrationModule_Fragment
    ) | (
      { __typename: 'VisitAppointmentModule' }
      & ModulesOptionsData_VisitAppointmentModule_Fragment
    ) | (
      { __typename: 'WebsiteModule' }
      & ModulesOptionsData_WebsiteModule_Fragment
    ) | (
      { __typename: 'WhatsappLiveChatModule' }
      & ModulesOptionsData_WhatsappLiveChatModule_Fragment
    )> }
  ) }
);


export const GetModulesOptionsDocument = /*#__PURE__*/ gql`
    query getModulesOptions($filter: ModuleFilteringRule) {
  modules: listModules(filter: $filter) {
    items {
      ...ModulesOptionsData
      ... on SimpleVehicleManagementModule {
        permissions
        porscheMasterDataModuleId
      }
    }
  }
}
    ${ModulesOptionsDataFragmentDoc}`;

/**
 * __useGetModulesOptionsQuery__
 *
 * To run a query within a React component, call `useGetModulesOptionsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetModulesOptionsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetModulesOptionsQuery({
 *   variables: {
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useGetModulesOptionsQuery(baseOptions?: Apollo.QueryHookOptions<GetModulesOptionsQuery, GetModulesOptionsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetModulesOptionsQuery, GetModulesOptionsQueryVariables>(GetModulesOptionsDocument, options);
      }
export function useGetModulesOptionsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetModulesOptionsQuery, GetModulesOptionsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetModulesOptionsQuery, GetModulesOptionsQueryVariables>(GetModulesOptionsDocument, options);
        }
export type GetModulesOptionsQueryHookResult = ReturnType<typeof useGetModulesOptionsQuery>;
export type GetModulesOptionsLazyQueryHookResult = ReturnType<typeof useGetModulesOptionsLazyQuery>;
export type GetModulesOptionsQueryResult = Apollo.QueryResult<GetModulesOptionsQuery, GetModulesOptionsQueryVariables>;