import type * as SchemaTypes from '../types';

import type { InventoryOptionSpecsFragment } from '../fragments/InventoryOptionSpecs';
import type { InventoryModuleOptionSpecsFragment } from '../fragments/InventoryModuleOptionSpecs';
import type { InventoryModelOptionSpecsFragment } from '../fragments/InventoryModelOptionSpecs';
import type { InventorySubModelOptionSpecsFragment } from '../fragments/InventorySubModelOptionSpecs';
import type { InventoryVariantOptionSpecsFragment } from '../fragments/InventoryVariantOptionSpecs';
import { gql } from '@apollo/client';
import { InventoryOptionSpecsFragmentDoc } from '../fragments/InventoryOptionSpecs';
import { InventoryModuleOptionSpecsFragmentDoc } from '../fragments/InventoryModuleOptionSpecs';
import { InventoryModelOptionSpecsFragmentDoc } from '../fragments/InventoryModelOptionSpecs';
import { InventorySubModelOptionSpecsFragmentDoc } from '../fragments/InventorySubModelOptionSpecs';
import { InventoryVariantOptionSpecsFragmentDoc } from '../fragments/InventoryVariantOptionSpecs';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GetInventoryOptionsQueryVariables = SchemaTypes.Exact<{
  filters?: SchemaTypes.InputMaybe<SchemaTypes.InventoryOptionsFilteringRule>;
}>;


export type GetInventoryOptionsQuery = (
  { __typename: 'Query' }
  & { options: (
    { __typename: 'InventoryOptions' }
    & InventoryOptionSpecsFragment
  ) }
);


export const GetInventoryOptionsDocument = /*#__PURE__*/ gql`
    query getInventoryOptions($filters: InventoryOptionsFilteringRule) {
  options: getInventoryOptions(filters: $filters) {
    ...InventoryOptionSpecs
  }
}
    ${InventoryOptionSpecsFragmentDoc}
${InventoryModuleOptionSpecsFragmentDoc}
${InventoryModelOptionSpecsFragmentDoc}
${InventorySubModelOptionSpecsFragmentDoc}
${InventoryVariantOptionSpecsFragmentDoc}`;

/**
 * __useGetInventoryOptionsQuery__
 *
 * To run a query within a React component, call `useGetInventoryOptionsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetInventoryOptionsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetInventoryOptionsQuery({
 *   variables: {
 *      filters: // value for 'filters'
 *   },
 * });
 */
export function useGetInventoryOptionsQuery(baseOptions?: Apollo.QueryHookOptions<GetInventoryOptionsQuery, GetInventoryOptionsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetInventoryOptionsQuery, GetInventoryOptionsQueryVariables>(GetInventoryOptionsDocument, options);
      }
export function useGetInventoryOptionsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetInventoryOptionsQuery, GetInventoryOptionsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetInventoryOptionsQuery, GetInventoryOptionsQueryVariables>(GetInventoryOptionsDocument, options);
        }
export type GetInventoryOptionsQueryHookResult = ReturnType<typeof useGetInventoryOptionsQuery>;
export type GetInventoryOptionsLazyQueryHookResult = ReturnType<typeof useGetInventoryOptionsLazyQuery>;
export type GetInventoryOptionsQueryResult = Apollo.QueryResult<GetInventoryOptionsQuery, GetInventoryOptionsQueryVariables>;