import type * as SchemaTypes from '../types';

import type { MobilityLocationDataFragment } from '../fragments/MobilityLocationData';
import type { UserPreviewDataFragment } from '../fragments/UserPreviewData';
import { gql } from '@apollo/client';
import { MobilityLocationDataFragmentDoc } from '../fragments/MobilityLocationData';
import { UserPreviewDataFragmentDoc } from '../fragments/UserPreviewData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GetMobilityLocationQueryVariables = SchemaTypes.Exact<{
  locationId: SchemaTypes.Scalars['ObjectID']['input'];
}>;


export type GetMobilityLocationQuery = (
  { __typename: 'Query' }
  & { location?: SchemaTypes.Maybe<(
    { __typename: 'MobilityLocation' }
    & MobilityLocationDataFragment
  )> }
);


export const GetMobilityLocationDocument = /*#__PURE__*/ gql`
    query getMobilityLocation($locationId: ObjectID!) {
  location: getMobilityLocation(locationId: $locationId) {
    ...MobilityLocationData
  }
}
    ${MobilityLocationDataFragmentDoc}
${UserPreviewDataFragmentDoc}`;

/**
 * __useGetMobilityLocationQuery__
 *
 * To run a query within a React component, call `useGetMobilityLocationQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetMobilityLocationQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetMobilityLocationQuery({
 *   variables: {
 *      locationId: // value for 'locationId'
 *   },
 * });
 */
export function useGetMobilityLocationQuery(baseOptions: Apollo.QueryHookOptions<GetMobilityLocationQuery, GetMobilityLocationQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetMobilityLocationQuery, GetMobilityLocationQueryVariables>(GetMobilityLocationDocument, options);
      }
export function useGetMobilityLocationLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetMobilityLocationQuery, GetMobilityLocationQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetMobilityLocationQuery, GetMobilityLocationQueryVariables>(GetMobilityLocationDocument, options);
        }
export type GetMobilityLocationQueryHookResult = ReturnType<typeof useGetMobilityLocationQuery>;
export type GetMobilityLocationLazyQueryHookResult = ReturnType<typeof useGetMobilityLocationLazyQuery>;
export type GetMobilityLocationQueryResult = Apollo.QueryResult<GetMobilityLocationQuery, GetMobilityLocationQueryVariables>;