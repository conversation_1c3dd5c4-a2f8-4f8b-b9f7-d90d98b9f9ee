import type * as SchemaTypes from '../types';

import type { ModelConfiguratorDetailsFragment } from '../fragments/ModelConfiguratorDetails';
import type { UploadFileFormDataFragment } from '../fragments/UploadFileFormData';
import type { UploadFileWithPreviewFormDataFragment } from '../fragments/UploadFileWithPreviewFormData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import type { VariantConfiguratorDetailsFragment } from '../fragments/VariantConfiguratorDetails';
import type { VehicleCalculatorSpecs_FinderVehicle_Fragment, VehicleCalculatorSpecs_LocalMake_Fragment, VehicleCalculatorSpecs_LocalModel_Fragment, VehicleCalculatorSpecs_LocalVariant_Fragment } from '../fragments/VehicleCalculatorSpecs';
import type { LocalVariantCalculatorSpecsFragment } from '../fragments/LocalVariantCalculatorSpecs';
import type { LocalModelCalculatorSpecsFragment } from '../fragments/LocalModelCalculatorSpecs';
import type { LocalMakeCalculatorSpecsFragment } from '../fragments/LocalMakeCalculatorSpecs';
import type { FinderVehicleDataFragment } from '../fragments/FinderVehicleData';
import type { BlockDetails_ColorBlock_Fragment, BlockDetails_OptionsBlock_Fragment, BlockDetails_PackageBlock_Fragment, BlockDetails_TrimBlock_Fragment } from '../fragments/BlockDetails';
import type { OptionSettingDetails_ComboOptionSettings_Fragment, OptionSettingDetails_DropdownOptionSettings_Fragment, OptionSettingDetails_MultiSelectOptionSettings_Fragment, OptionSettingDetails_SingleSelectOptionSettings_Fragment } from '../fragments/OptionSettingDetails';
import type { InventoryDetailsPublicData_ConfiguratorInventory_Fragment, InventoryDetailsPublicData_MobilityInventory_Fragment } from '../fragments/InventoryDetailsPublicData';
import type { StockInventorySpecs_ConfiguratorStockInventory_Fragment, StockInventorySpecs_MobilityStockInventory_Fragment } from '../fragments/StockInventorySpecs';
import type { ApplicationStageData_ConfiguratorApplication_Fragment, ApplicationStageData_EventApplication_Fragment, ApplicationStageData_FinderApplication_Fragment, ApplicationStageData_LaunchpadApplication_Fragment, ApplicationStageData_MobilityApplication_Fragment, ApplicationStageData_SalesOfferApplication_Fragment, ApplicationStageData_StandardApplication_Fragment } from '../fragments/ApplicationStageData';
import type { ReferenceApplicationData_ConfiguratorApplication_Fragment, ReferenceApplicationData_EventApplication_Fragment, ReferenceApplicationData_FinderApplication_Fragment, ReferenceApplicationData_LaunchpadApplication_Fragment, ReferenceApplicationData_MobilityApplication_Fragment, ReferenceApplicationData_SalesOfferApplication_Fragment, ReferenceApplicationData_StandardApplication_Fragment, ReferenceDepositData_ApplicationAdyenDeposit_Fragment, ReferenceDepositData_ApplicationFiservDeposit_Fragment, ReferenceDepositData_ApplicationPayGateDeposit_Fragment, ReferenceDepositData_ApplicationPorscheDeposit_Fragment, ReferenceDepositData_ApplicationTtbDeposit_Fragment, ReferenceFinancingData_DefaultApplicationFinancing_Fragment, ReferenceFinancingData_NewZealandApplicationFinancing_Fragment, ReferenceFinancingData_SingaporeApplicationFinancing_Fragment, ReferenceInsuranceData_DefaultApplicationInsurancing_Fragment, ReferenceInsuranceData_NewZealandApplicationInsurancing_Fragment, ReferenceInsuranceData_SingaporeApplicationInsurancing_Fragment } from '../fragments/ReferenceApplicationData';
import type { VehicleSpecs_FinderVehicle_Fragment, VehicleSpecs_LocalMake_Fragment, VehicleSpecs_LocalModel_Fragment, VehicleSpecs_LocalVariant_Fragment } from '../fragments/VehicleSpecs';
import type { LocalVariantSpecsFragment } from '../fragments/LocalVariantSpecs';
import type { LocalModelSpecsFragment } from '../fragments/LocalModelSpecs';
import type { LocalMakeSpecsFragment } from '../fragments/LocalMakeSpecs';
import type { AdvancedVersioningDataFragment } from '../fragments/AdvancedVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from '../fragments/AuthorData';
import type { FinderVehicleSpecsFragment } from '../fragments/FinderVehicleSpecs';
import type { FullListingValueFragment, FormattedDateDataFragment, LocalizedStringDataFragment, LocalizedValueDataFragment, NumberUnitDataFragment } from '../fragments/finderListing.fragment';
import type { TradeInVehicleDataFragment } from '../fragments/TradeInVehicleData';
import type { CompanyPublicSpecsFragment } from '../fragments/CompanyPublicSpecs';
import type { PeriodDataFragment } from '../fragments/PeriodData';
import type { StockBlockingPeriodDataFragment } from '../fragments/StockBlockingPeriod';
import type { ConfiguratorInventoryPublicSpecsFragment } from '../fragments/ConfiguratorInventoryPublicSpecs';
import type { MobilityInventoryPublicSpecsFragment } from '../fragments/MobilityInventoryPublicSpecs';
import type { MobilityModuleSpecsFragment } from '../fragments/MobilityModuleSpecs';
import type { CompanyInModuleOptionDataFragment } from '../fragments/CompanyInModuleOptionData';
import type { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragment } from '../fragments/VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import type { SimpleVersioningDataFragment } from '../fragments/SimpleVersioningData';
import type { DealerTranslatedStringSettingDataFragment } from '../fragments/DealerTranslatedStringData';
import type { DealerBookingCodeSpecsFragment } from '../fragments/DealerBookingCodeSpecs';
import type { MobilitySigningSettingSpecsFragment } from '../fragments/MobilitySigningSettingSpecs';
import type { MobilityLocationDataFragment } from '../fragments/MobilityLocationData';
import type { UserPreviewDataFragment } from '../fragments/UserPreviewData';
import type { KycPresetsOptionsDataFragment } from '../fragments/KYCPresetsOptionsData';
import type { MobilityModuleEmailScenarioContentSpecsFragment } from '../fragments/MobilityModuleEmailScenarioContentSpecs';
import type { MobilityCustomerEmailContentDataFragment } from '../fragments/MobilityCustomerEmailContentData';
import type { MobilityEmailContentDataFragment } from '../fragments/MobilityEmailContentData';
import type { TranslatedTextDataFragment } from '../fragments/TranslationTextData';
import type { MobilityOperatorEmailContentDataFragment } from '../fragments/MobilityOperatorEmailContentData';
import type { MobilityHomeDeliveryDataFragment } from '../fragments/MobilityHomeDeliveryData';
import type { CounterSettingsSpecsFragment } from '../fragments/CounterSettingsSpecs';
import { gql } from '@apollo/client';
import { ModelConfiguratorDetailsFragmentDoc } from '../fragments/ModelConfiguratorDetails';
import { UploadFileFormDataFragmentDoc } from '../fragments/UploadFileFormData';
import { UploadFileWithPreviewFormDataFragmentDoc } from '../fragments/UploadFileWithPreviewFormData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import { VariantConfiguratorDetailsFragmentDoc } from '../fragments/VariantConfiguratorDetails';
import { VehicleCalculatorSpecsFragmentDoc } from '../fragments/VehicleCalculatorSpecs';
import { LocalVariantCalculatorSpecsFragmentDoc } from '../fragments/LocalVariantCalculatorSpecs';
import { LocalModelCalculatorSpecsFragmentDoc } from '../fragments/LocalModelCalculatorSpecs';
import { LocalMakeCalculatorSpecsFragmentDoc } from '../fragments/LocalMakeCalculatorSpecs';
import { FinderVehicleDataFragmentDoc } from '../fragments/FinderVehicleData';
import { BlockDetailsFragmentDoc } from '../fragments/BlockDetails';
import { OptionSettingDetailsFragmentDoc } from '../fragments/OptionSettingDetails';
import { InventoryDetailsPublicDataFragmentDoc } from '../fragments/InventoryDetailsPublicData';
import { StockInventorySpecsFragmentDoc } from '../fragments/StockInventorySpecs';
import { ApplicationStageDataFragmentDoc } from '../fragments/ApplicationStageData';
import { ReferenceApplicationDataFragmentDoc, ReferenceDepositDataFragmentDoc, ReferenceFinancingDataFragmentDoc, ReferenceInsuranceDataFragmentDoc } from '../fragments/ReferenceApplicationData';
import { VehicleSpecsFragmentDoc } from '../fragments/VehicleSpecs';
import { LocalVariantSpecsFragmentDoc } from '../fragments/LocalVariantSpecs';
import { LocalModelSpecsFragmentDoc } from '../fragments/LocalModelSpecs';
import { LocalMakeSpecsFragmentDoc } from '../fragments/LocalMakeSpecs';
import { AdvancedVersioningDataFragmentDoc } from '../fragments/AdvancedVersioningData';
import { AuthorDataFragmentDoc } from '../fragments/AuthorData';
import { FinderVehicleSpecsFragmentDoc } from '../fragments/FinderVehicleSpecs';
import { FullListingValueFragmentDoc, FormattedDateDataFragmentDoc, LocalizedStringDataFragmentDoc, LocalizedValueDataFragmentDoc, NumberUnitDataFragmentDoc } from '../fragments/finderListing.fragment';
import { TradeInVehicleDataFragmentDoc } from '../fragments/TradeInVehicleData';
import { CompanyPublicSpecsFragmentDoc } from '../fragments/CompanyPublicSpecs';
import { PeriodDataFragmentDoc } from '../fragments/PeriodData';
import { StockBlockingPeriodDataFragmentDoc } from '../fragments/StockBlockingPeriod';
import { ConfiguratorInventoryPublicSpecsFragmentDoc } from '../fragments/ConfiguratorInventoryPublicSpecs';
import { MobilityInventoryPublicSpecsFragmentDoc } from '../fragments/MobilityInventoryPublicSpecs';
import { MobilityModuleSpecsFragmentDoc } from '../fragments/MobilityModuleSpecs';
import { CompanyInModuleOptionDataFragmentDoc } from '../fragments/CompanyInModuleOptionData';
import { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragmentDoc } from '../fragments/VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import { SimpleVersioningDataFragmentDoc } from '../fragments/SimpleVersioningData';
import { DealerTranslatedStringSettingDataFragmentDoc } from '../fragments/DealerTranslatedStringData';
import { DealerBookingCodeSpecsFragmentDoc } from '../fragments/DealerBookingCodeSpecs';
import { MobilitySigningSettingSpecsFragmentDoc } from '../fragments/MobilitySigningSettingSpecs';
import { MobilityLocationDataFragmentDoc } from '../fragments/MobilityLocationData';
import { UserPreviewDataFragmentDoc } from '../fragments/UserPreviewData';
import { KycPresetsOptionsDataFragmentDoc } from '../fragments/KYCPresetsOptionsData';
import { MobilityModuleEmailScenarioContentSpecsFragmentDoc } from '../fragments/MobilityModuleEmailScenarioContentSpecs';
import { MobilityCustomerEmailContentDataFragmentDoc } from '../fragments/MobilityCustomerEmailContentData';
import { MobilityEmailContentDataFragmentDoc } from '../fragments/MobilityEmailContentData';
import { TranslatedTextDataFragmentDoc } from '../fragments/TranslationTextData';
import { MobilityOperatorEmailContentDataFragmentDoc } from '../fragments/MobilityOperatorEmailContentData';
import { MobilityHomeDeliveryDataFragmentDoc } from '../fragments/MobilityHomeDeliveryData';
import { CounterSettingsSpecsFragmentDoc } from '../fragments/CounterSettingsSpecs';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GetModelConfiguratorForApplicationQueryVariables = SchemaTypes.Exact<{
  urlIdentifier?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['String']['input']>;
  productionOnly?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['Boolean']['input']>;
  bankModuleId?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['ObjectID']['input']>;
  dealerId?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['ObjectID']['input']>;
  applicationModuleIds?: SchemaTypes.InputMaybe<Array<SchemaTypes.Scalars['ObjectID']['input']> | SchemaTypes.Scalars['ObjectID']['input']>;
}>;


export type GetModelConfiguratorForApplicationQuery = (
  { __typename: 'Query' }
  & { modelConfigurator?: SchemaTypes.Maybe<(
    { __typename: 'ModelConfigurator' }
    & ModelConfiguratorDetailsFragment
  )> }
);


export const GetModelConfiguratorForApplicationDocument = /*#__PURE__*/ gql`
    query getModelConfiguratorForApplication($urlIdentifier: String, $productionOnly: Boolean, $bankModuleId: ObjectID, $dealerId: ObjectID, $applicationModuleIds: [ObjectID!]) {
  modelConfigurator: getModelConfigurator(
    urlIdentifier: $urlIdentifier
    productionOnly: $productionOnly
    applicationModuleIds: $applicationModuleIds
  ) {
    ...ModelConfiguratorDetails
  }
}
    ${ModelConfiguratorDetailsFragmentDoc}
${UploadFileFormDataFragmentDoc}
${UploadFileWithPreviewFormDataFragmentDoc}
${TranslatedStringDataFragmentDoc}
${VariantConfiguratorDetailsFragmentDoc}
${VehicleCalculatorSpecsFragmentDoc}
${LocalVariantCalculatorSpecsFragmentDoc}
${LocalModelCalculatorSpecsFragmentDoc}
${LocalMakeCalculatorSpecsFragmentDoc}
${FinderVehicleDataFragmentDoc}
${BlockDetailsFragmentDoc}
${OptionSettingDetailsFragmentDoc}
${InventoryDetailsPublicDataFragmentDoc}
${StockInventorySpecsFragmentDoc}
${ApplicationStageDataFragmentDoc}
${ReferenceApplicationDataFragmentDoc}
${ReferenceDepositDataFragmentDoc}
${ReferenceFinancingDataFragmentDoc}
${ReferenceInsuranceDataFragmentDoc}
${VehicleSpecsFragmentDoc}
${LocalVariantSpecsFragmentDoc}
${LocalModelSpecsFragmentDoc}
${LocalMakeSpecsFragmentDoc}
${AdvancedVersioningDataFragmentDoc}
${AuthorDataFragmentDoc}
${FinderVehicleSpecsFragmentDoc}
${FullListingValueFragmentDoc}
${FormattedDateDataFragmentDoc}
${LocalizedStringDataFragmentDoc}
${LocalizedValueDataFragmentDoc}
${NumberUnitDataFragmentDoc}
${TradeInVehicleDataFragmentDoc}
${CompanyPublicSpecsFragmentDoc}
${PeriodDataFragmentDoc}
${StockBlockingPeriodDataFragmentDoc}
${ConfiguratorInventoryPublicSpecsFragmentDoc}
${MobilityInventoryPublicSpecsFragmentDoc}
${MobilityModuleSpecsFragmentDoc}
${CompanyInModuleOptionDataFragmentDoc}
${VehicleDataWithPorscheCodeIntegrationSettingSpecsFragmentDoc}
${SimpleVersioningDataFragmentDoc}
${DealerTranslatedStringSettingDataFragmentDoc}
${DealerBookingCodeSpecsFragmentDoc}
${MobilitySigningSettingSpecsFragmentDoc}
${MobilityLocationDataFragmentDoc}
${UserPreviewDataFragmentDoc}
${KycPresetsOptionsDataFragmentDoc}
${MobilityModuleEmailScenarioContentSpecsFragmentDoc}
${MobilityCustomerEmailContentDataFragmentDoc}
${MobilityEmailContentDataFragmentDoc}
${TranslatedTextDataFragmentDoc}
${MobilityOperatorEmailContentDataFragmentDoc}
${MobilityHomeDeliveryDataFragmentDoc}
${CounterSettingsSpecsFragmentDoc}`;

/**
 * __useGetModelConfiguratorForApplicationQuery__
 *
 * To run a query within a React component, call `useGetModelConfiguratorForApplicationQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetModelConfiguratorForApplicationQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetModelConfiguratorForApplicationQuery({
 *   variables: {
 *      urlIdentifier: // value for 'urlIdentifier'
 *      productionOnly: // value for 'productionOnly'
 *      bankModuleId: // value for 'bankModuleId'
 *      dealerId: // value for 'dealerId'
 *      applicationModuleIds: // value for 'applicationModuleIds'
 *   },
 * });
 */
export function useGetModelConfiguratorForApplicationQuery(baseOptions?: Apollo.QueryHookOptions<GetModelConfiguratorForApplicationQuery, GetModelConfiguratorForApplicationQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetModelConfiguratorForApplicationQuery, GetModelConfiguratorForApplicationQueryVariables>(GetModelConfiguratorForApplicationDocument, options);
      }
export function useGetModelConfiguratorForApplicationLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetModelConfiguratorForApplicationQuery, GetModelConfiguratorForApplicationQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetModelConfiguratorForApplicationQuery, GetModelConfiguratorForApplicationQueryVariables>(GetModelConfiguratorForApplicationDocument, options);
        }
export type GetModelConfiguratorForApplicationQueryHookResult = ReturnType<typeof useGetModelConfiguratorForApplicationQuery>;
export type GetModelConfiguratorForApplicationLazyQueryHookResult = ReturnType<typeof useGetModelConfiguratorForApplicationLazyQuery>;
export type GetModelConfiguratorForApplicationQueryResult = Apollo.QueryResult<GetModelConfiguratorForApplicationQuery, GetModelConfiguratorForApplicationQueryVariables>;