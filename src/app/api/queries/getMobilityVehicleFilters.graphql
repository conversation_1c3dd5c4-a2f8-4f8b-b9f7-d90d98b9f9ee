query getMobilityVehicleFilters($moduleId: ObjectID!, $productionOnly: Boolean) {
    filters: getMobilityVehicleFilters(moduleId: $moduleId, productionOnly: $productionOnly) {
        id
        models {
            ...TranslatedStringData
        }
        submodels {
            name {
                ...TranslatedStringData
            }
            parentModelId
            parentModel {
                id
                name {
                    ...TranslatedStringData
                }
            }
        }
        dailyRates {
            from
            to
        }
    }
}