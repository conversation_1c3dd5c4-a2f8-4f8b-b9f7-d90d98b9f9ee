import type * as SchemaTypes from '../types';

import type { LocalVariantPublicSpecsFragment } from '../fragments/LocalVariantPublicSpecs';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import type { UploadFileWithPreviewFormDataFragment } from '../fragments/UploadFileWithPreviewFormData';
import type { LocalModelPublicSpecsFragment } from '../fragments/LocalModelPublicSpecs';
import type { LocalMakePublicSpecsFragment } from '../fragments/LocalMakePublicSpecs';
import { gql } from '@apollo/client';
import { LocalVariantPublicSpecsFragmentDoc } from '../fragments/LocalVariantPublicSpecs';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import { UploadFileWithPreviewFormDataFragmentDoc } from '../fragments/UploadFileWithPreviewFormData';
import { LocalModelPublicSpecsFragmentDoc } from '../fragments/LocalModelPublicSpecs';
import { LocalMakePublicSpecsFragmentDoc } from '../fragments/LocalMakePublicSpecs';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GetMarketingDashboardFilterOptionQueryVariables = SchemaTypes.Exact<{
  companyId: SchemaTypes.Scalars['ObjectID']['input'];
  period?: SchemaTypes.InputMaybe<SchemaTypes.PeriodPayload>;
}>;


export type GetMarketingDashboardFilterOptionQuery = (
  { __typename: 'Query' }
  & { filter: (
    { __typename: 'MarketingDashboardFilterDropdown' }
    & { variants: Array<{ __typename: 'FinderVehicle' } | { __typename: 'LocalMake' } | { __typename: 'LocalModel' } | (
      { __typename: 'LocalVariant' }
      & LocalVariantPublicSpecsFragment
    )>, campaigns: Array<(
      { __typename: 'FilterDropdownOption' }
      & Pick<SchemaTypes.FilterDropdownOption, 'value' | 'label'>
    )>, sources: Array<(
      { __typename: 'FilterDropdownOption' }
      & Pick<SchemaTypes.FilterDropdownOption, 'value' | 'label'>
    )>, mediums: Array<(
      { __typename: 'FilterDropdownOption' }
      & Pick<SchemaTypes.FilterDropdownOption, 'value' | 'label'>
    )> }
  ) }
);


export const GetMarketingDashboardFilterOptionDocument = /*#__PURE__*/ gql`
    query getMarketingDashboardFilterOption($companyId: ObjectID!, $period: PeriodPayload) {
  filter: getMarketingDashboardFilterOption(
    companyId: $companyId
    period: $period
  ) {
    variants {
      ...LocalVariantPublicSpecs
    }
    campaigns {
      value
      label
    }
    sources {
      value
      label
    }
    mediums {
      value
      label
    }
  }
}
    ${LocalVariantPublicSpecsFragmentDoc}
${TranslatedStringDataFragmentDoc}
${UploadFileWithPreviewFormDataFragmentDoc}
${LocalModelPublicSpecsFragmentDoc}
${LocalMakePublicSpecsFragmentDoc}`;

/**
 * __useGetMarketingDashboardFilterOptionQuery__
 *
 * To run a query within a React component, call `useGetMarketingDashboardFilterOptionQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetMarketingDashboardFilterOptionQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetMarketingDashboardFilterOptionQuery({
 *   variables: {
 *      companyId: // value for 'companyId'
 *      period: // value for 'period'
 *   },
 * });
 */
export function useGetMarketingDashboardFilterOptionQuery(baseOptions: Apollo.QueryHookOptions<GetMarketingDashboardFilterOptionQuery, GetMarketingDashboardFilterOptionQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetMarketingDashboardFilterOptionQuery, GetMarketingDashboardFilterOptionQueryVariables>(GetMarketingDashboardFilterOptionDocument, options);
      }
export function useGetMarketingDashboardFilterOptionLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetMarketingDashboardFilterOptionQuery, GetMarketingDashboardFilterOptionQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetMarketingDashboardFilterOptionQuery, GetMarketingDashboardFilterOptionQueryVariables>(GetMarketingDashboardFilterOptionDocument, options);
        }
export type GetMarketingDashboardFilterOptionQueryHookResult = ReturnType<typeof useGetMarketingDashboardFilterOptionQuery>;
export type GetMarketingDashboardFilterOptionLazyQueryHookResult = ReturnType<typeof useGetMarketingDashboardFilterOptionLazyQuery>;
export type GetMarketingDashboardFilterOptionQueryResult = Apollo.QueryResult<GetMarketingDashboardFilterOptionQuery, GetMarketingDashboardFilterOptionQueryVariables>;