query getModelConfiguratorForApplication(
    $urlIdentifier: String
    $productionOnly: Boolean
    $bankModuleId: ObjectID
    $dealerId: ObjectID
    $applicationModuleIds: [ObjectID!]
) {
    modelConfigurator: getModelConfigurator(
        urlIdentifier: $urlIdentifier
        productionOnly: $productionOnly
        applicationModuleIds: $applicationModuleIds
    ) {
        ...ModelConfiguratorDetails
    }
}
