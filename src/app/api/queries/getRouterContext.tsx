import type * as SchemaTypes from '../types';

import type { RouterContextDataFragment } from '../fragments/RouterContextData';
import type { CompanyWithPermissionsContextDataFragment } from '../fragments/CompanyWithPermissionsContextData';
import type { CompanyContextDataFragment } from '../fragments/CompanyContextData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import type { LanguagePackContextDataFragment } from '../fragments/LanguagePackContextData';
import type { UploadFileWithPreviewFormDataFragment } from '../fragments/UploadFileWithPreviewFormData';
import type { AvailableModulesDataFragment } from '../fragments/AvailableModulesData';
import type { CompanyDealerDataFragment } from '../fragments/CompanyDealerData';
import type { MaintenanceUpdateFragment } from '../fragments/MaintenanceUpdate';
import type { SimpleVersioningDataFragment } from '../fragments/SimpleVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from '../fragments/AuthorData';
import type { UserAvatarSpecsFragment } from '../fragments/UserAvatarSpecs';
import type { EdmEmailFooterPublicDataFragment } from '../fragments/EdmEmailFooterPublicData';
import type { EdmSocialMediaDataFragment } from '../fragments/EdmSocialMediaData';
import type { RouterLayoutContextData_BasicLayout_Fragment, RouterLayoutContextData_BasicProLayout_Fragment, RouterLayoutContextData_PorscheV3Layout_Fragment } from '../fragments/RouterLayoutContextData';
import type { EndpointContextData_ApplicationListEndpoint_Fragment, EndpointContextData_ConfiguratorApplicationEntrypoint_Fragment, EndpointContextData_CustomerListEndpoint_Fragment, EndpointContextData_DummyPrivatePageEndpoint_Fragment, EndpointContextData_DummyWelcomePageEndpoint_Fragment, EndpointContextData_EventApplicationEntrypoint_Fragment, EndpointContextData_FinderApplicationEntrypoint_Fragment, EndpointContextData_FinderApplicationPublicAccessEntrypoint_Fragment, EndpointContextData_LaunchPadApplicationEntrypoint_Fragment, EndpointContextData_LeadListEndpoint_Fragment, EndpointContextData_MobilityApplicationEntrypoint_Fragment, EndpointContextData_StandardApplicationEntrypoint_Fragment, EndpointContextData_StandardApplicationPublicAccessEntrypoint_Fragment, EndpointContextData_WebPageEndpoint_Fragment } from '../fragments/EndpointContextData';
import type { DummyPrivatePageEndpointContextDataFragment } from '../fragments/DummyPrivatePageEndpointContextData';
import type { StandardApplicationEntrypointContextDataFragment } from '../fragments/StandardApplicationEntrypointContextData';
import type { ApplicationMarketTypeFragment_DefaultApplicationMarket_Fragment, ApplicationMarketTypeFragment_NewZealandApplicationMarket_Fragment, ApplicationMarketTypeFragment_SingaporeApplicationMarket_Fragment } from '../fragments/ApplicationMarketTypeFragment';
import type { DealerMarketDataFragment } from '../fragments/DealerMarketData';
import type { BankDealerMarketDataFragment } from '../fragments/BankDealerMarketData';
import type { NzFeesDealerMarketDataFragment } from '../fragments/NzFeesDealerMarketData';
import type { DealershipSettingSpecData_DealershipMyInfoSetting_Fragment, DealershipSettingSpecData_DealershipPaymentSetting_Fragment, DealershipSettingSpecData_DealershipPublicSalesPerson_Fragment } from '../fragments/DealershipSettingSpecData';
import type { AppointmentModuleSpecsFragment } from '../fragments/AppointmentModuleSpecs';
import type { AppointmentTimeSlotDataFragment } from '../fragments/AppointmentTimeSlotData';
import type { NamirialSigningModuleSpecsFragment } from '../fragments/NamirialSigningModuleSpecs';
import type { NamirialSettingsSpecFragment } from '../fragments/NamirialSettingsSpec';
import type { AppointmentModuleEmailContentsSpecsFragment, AppointmentModuleEmailContentCustomerSpecsFragment, AppointmentModuleEmailContentSpecsFragment, AppointmentModuleEmailContentSalesPersonSpecsFragment, AppointmentModuleEmailContentFinderReservationSpecsFragment } from '../fragments/AppointmentModuleEmailContentsSpecs';
import type { DealerTranslatedStringSettingDataFragment } from '../fragments/DealerTranslatedStringData';
import type { DealerBooleanSettingDataFragment } from '../fragments/DealerBooleanSettingData';
import type { VisitAppointmentModuleSpecsFragment } from '../fragments/VisitAppointmentModuleSpecs';
import type { CompanyInModuleOptionDataFragment } from '../fragments/CompanyInModuleOptionData';
import type { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragment } from '../fragments/VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import type { TimeSlotDataFragment } from '../fragments/TimeSlotData';
import type { VisitAppointmentModuleEmailContentsSpecsFragment, VisitAppointmentModuleEmailContentCustomerSpecsFragment, VisitAppointmentModuleEmailContentSpecsFragment, VisitAppointmentModuleEmailContentSalesPersonSpecsFragment } from '../fragments/VisitAppointmentModuleEmailContentsSpecs';
import type { InsurerEntrypointContextDataFragment } from '../fragments/InsurerEntrypointContextData';
import type { DealerDisclaimersConfiguratorDataFragment } from '../fragments/DealerPriceDisclaimerConfiguratorData';
import type { FlexibleDiscountDataFragment } from '../fragments/FlexibleDiscountData';
import type { KycExtraSettingsSpecsFragment } from '../fragments/KYCExtraSettingsSpecs';
import type { ApplicationListEndpointContextDataFragment } from '../fragments/ApplicationListEndpointContextData';
import type { LeadListEndpointContextDataFragment } from '../fragments/LeadListEndpointContextData';
import type { EventApplicationEntrypointContextDataFragment } from '../fragments/EventApplicationEntrypointContextData';
import type { LaunchPadApplicationEntrypointContextDataFragment } from '../fragments/LaunchPadApplicationEntrypointContextData';
import type { LaunchPadModuleSpecsFragment } from '../fragments/LaunchPadModuleSpecs';
import type { CounterSettingsSpecsFragment } from '../fragments/CounterSettingsSpecs';
import type { ConfiguratorApplicationEntrypointContextDataFragment } from '../fragments/ConfiguratorApplicationEntrypointContextData';
import type { MyInfoSettingSpecFragment } from '../fragments/MyInfoSettingSpec';
import type { DepositAmountDataFragment } from '../fragments/DepositAmountData';
import type { CustomerListEndpointContextDataFragment } from '../fragments/CustomerListEndpointContextData';
import type { MobilityApplicationEntrypointContextDataFragment } from '../fragments/MobilityApplicationEntrypointContextData';
import type { DealerBookingCodeDataFragment } from '../fragments/DealerBookingCodeData';
import type { MobilityLocationDataFragment } from '../fragments/MobilityLocationData';
import type { UserPreviewDataFragment } from '../fragments/UserPreviewData';
import type { DateUnitDataFragment } from '../fragments/DateUnitData';
import type { WebpageEndpointContextDataFragment } from '../fragments/WebpageEndpointContextData';
import type { WebPageEndpointSpecsFragment } from '../fragments/WebPageEndpointSpecs';
import type { WebPagePathDataFragment } from '../fragments/WebPagePathData';
import type { StandardApplicationPublicAccessEntrypointContextDataFragment } from '../fragments/StandardApplicationPublicAccessEntrypointContextData';
import type { DealerApplicationFragmentFragment } from '../fragments/DealerApplicationFragment';
import type { DealerContactFragmentFragment } from '../fragments/DealerContactFragment';
import type { DealerSocialMediaFragmentFragment } from '../fragments/DealerSocialMediaFragment';
import type { DealerDisclaimersFragmentFragment } from '../fragments/DealerDisclaimersFragment';
import type { DealerIntegrationDetailsFragmentFragment } from '../fragments/DealerIntegrationDetailsFragment';
import type { FinderApplicationPublicAccessEntrypointContextDataFragment } from '../fragments/FinderApplicationPublicAccessEntrypointContextData';
import type { EntrypointFinderApplicationPublicModuleFragment } from '../fragments/EntrypointFinderApplicationPublicModule';
import type { FinderApplicationModuleEmailContentSpecsFragment } from '../fragments/FinderApplicationModuleEmailContentSpecs';
import type { TranslatedTextDataFragment } from '../fragments/TranslationTextData';
import type { ModuleDisclaimersDataFragment } from '../fragments/ModuleDisclaimersData';
import type { FinderApplicationEntrypointContextDataFragment } from '../fragments/FinderApplicationEntrypointContextData';
import type { EntrypointFinderApplicationPrivateModuleFragment } from '../fragments/EntrypointFinderApplicationPrivateModule';
import type { MenuItemSpecs_MenuCustomPathItem_Fragment, MenuItemSpecs_MenuEndpointItem_Fragment, MenuItemSpecs_MenuLogoutActionItem_Fragment } from '../fragments/MenuItemSpecs';
import type { TranslatedStringSpecsFragment } from '../fragments/TranslatedStringSpecs';
import type { PathScriptSpecsFragment } from '../fragments/PathScriptSpecs';
import { gql } from '@apollo/client';
import { RouterContextDataFragmentDoc } from '../fragments/RouterContextData';
import { CompanyWithPermissionsContextDataFragmentDoc } from '../fragments/CompanyWithPermissionsContextData';
import { CompanyContextDataFragmentDoc } from '../fragments/CompanyContextData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import { LanguagePackContextDataFragmentDoc } from '../fragments/LanguagePackContextData';
import { UploadFileWithPreviewFormDataFragmentDoc } from '../fragments/UploadFileWithPreviewFormData';
import { AvailableModulesDataFragmentDoc } from '../fragments/AvailableModulesData';
import { CompanyDealerDataFragmentDoc } from '../fragments/CompanyDealerData';
import { MaintenanceUpdateFragmentDoc } from '../fragments/MaintenanceUpdate';
import { SimpleVersioningDataFragmentDoc } from '../fragments/SimpleVersioningData';
import { AuthorDataFragmentDoc } from '../fragments/AuthorData';
import { UserAvatarSpecsFragmentDoc } from '../fragments/UserAvatarSpecs';
import { EdmEmailFooterPublicDataFragmentDoc } from '../fragments/EdmEmailFooterPublicData';
import { EdmSocialMediaDataFragmentDoc } from '../fragments/EdmSocialMediaData';
import { RouterLayoutContextDataFragmentDoc } from '../fragments/RouterLayoutContextData';
import { EndpointContextDataFragmentDoc } from '../fragments/EndpointContextData';
import { DummyPrivatePageEndpointContextDataFragmentDoc } from '../fragments/DummyPrivatePageEndpointContextData';
import { StandardApplicationEntrypointContextDataFragmentDoc } from '../fragments/StandardApplicationEntrypointContextData';
import { ApplicationMarketTypeFragmentFragmentDoc } from '../fragments/ApplicationMarketTypeFragment';
import { DealerMarketDataFragmentDoc } from '../fragments/DealerMarketData';
import { BankDealerMarketDataFragmentDoc } from '../fragments/BankDealerMarketData';
import { NzFeesDealerMarketDataFragmentDoc } from '../fragments/NzFeesDealerMarketData';
import { DealershipSettingSpecDataFragmentDoc } from '../fragments/DealershipSettingSpecData';
import { AppointmentModuleSpecsFragmentDoc } from '../fragments/AppointmentModuleSpecs';
import { AppointmentTimeSlotDataFragmentDoc } from '../fragments/AppointmentTimeSlotData';
import { NamirialSigningModuleSpecsFragmentDoc } from '../fragments/NamirialSigningModuleSpecs';
import { NamirialSettingsSpecFragmentDoc } from '../fragments/NamirialSettingsSpec';
import { AppointmentModuleEmailContentsSpecsFragmentDoc, AppointmentModuleEmailContentCustomerSpecsFragmentDoc, AppointmentModuleEmailContentSpecsFragmentDoc, AppointmentModuleEmailContentSalesPersonSpecsFragmentDoc, AppointmentModuleEmailContentFinderReservationSpecsFragmentDoc } from '../fragments/AppointmentModuleEmailContentsSpecs';
import { DealerTranslatedStringSettingDataFragmentDoc } from '../fragments/DealerTranslatedStringData';
import { DealerBooleanSettingDataFragmentDoc } from '../fragments/DealerBooleanSettingData';
import { VisitAppointmentModuleSpecsFragmentDoc } from '../fragments/VisitAppointmentModuleSpecs';
import { CompanyInModuleOptionDataFragmentDoc } from '../fragments/CompanyInModuleOptionData';
import { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragmentDoc } from '../fragments/VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import { TimeSlotDataFragmentDoc } from '../fragments/TimeSlotData';
import { VisitAppointmentModuleEmailContentsSpecsFragmentDoc, VisitAppointmentModuleEmailContentCustomerSpecsFragmentDoc, VisitAppointmentModuleEmailContentSpecsFragmentDoc, VisitAppointmentModuleEmailContentSalesPersonSpecsFragmentDoc } from '../fragments/VisitAppointmentModuleEmailContentsSpecs';
import { InsurerEntrypointContextDataFragmentDoc } from '../fragments/InsurerEntrypointContextData';
import { DealerDisclaimersConfiguratorDataFragmentDoc } from '../fragments/DealerPriceDisclaimerConfiguratorData';
import { FlexibleDiscountDataFragmentDoc } from '../fragments/FlexibleDiscountData';
import { KycExtraSettingsSpecsFragmentDoc } from '../fragments/KYCExtraSettingsSpecs';
import { ApplicationListEndpointContextDataFragmentDoc } from '../fragments/ApplicationListEndpointContextData';
import { LeadListEndpointContextDataFragmentDoc } from '../fragments/LeadListEndpointContextData';
import { EventApplicationEntrypointContextDataFragmentDoc } from '../fragments/EventApplicationEntrypointContextData';
import { LaunchPadApplicationEntrypointContextDataFragmentDoc } from '../fragments/LaunchPadApplicationEntrypointContextData';
import { LaunchPadModuleSpecsFragmentDoc } from '../fragments/LaunchPadModuleSpecs';
import { CounterSettingsSpecsFragmentDoc } from '../fragments/CounterSettingsSpecs';
import { ConfiguratorApplicationEntrypointContextDataFragmentDoc } from '../fragments/ConfiguratorApplicationEntrypointContextData';
import { MyInfoSettingSpecFragmentDoc } from '../fragments/MyInfoSettingSpec';
import { DepositAmountDataFragmentDoc } from '../fragments/DepositAmountData';
import { CustomerListEndpointContextDataFragmentDoc } from '../fragments/CustomerListEndpointContextData';
import { MobilityApplicationEntrypointContextDataFragmentDoc } from '../fragments/MobilityApplicationEntrypointContextData';
import { DealerBookingCodeDataFragmentDoc } from '../fragments/DealerBookingCodeData';
import { MobilityLocationDataFragmentDoc } from '../fragments/MobilityLocationData';
import { UserPreviewDataFragmentDoc } from '../fragments/UserPreviewData';
import { DateUnitDataFragmentDoc } from '../fragments/DateUnitData';
import { WebpageEndpointContextDataFragmentDoc } from '../fragments/WebpageEndpointContextData';
import { WebPageEndpointSpecsFragmentDoc } from '../fragments/WebPageEndpointSpecs';
import { WebPagePathDataFragmentDoc } from '../fragments/WebPagePathData';
import { StandardApplicationPublicAccessEntrypointContextDataFragmentDoc } from '../fragments/StandardApplicationPublicAccessEntrypointContextData';
import { DealerApplicationFragmentFragmentDoc } from '../fragments/DealerApplicationFragment';
import { DealerContactFragmentFragmentDoc } from '../fragments/DealerContactFragment';
import { DealerSocialMediaFragmentFragmentDoc } from '../fragments/DealerSocialMediaFragment';
import { DealerDisclaimersFragmentFragmentDoc } from '../fragments/DealerDisclaimersFragment';
import { DealerIntegrationDetailsFragmentFragmentDoc } from '../fragments/DealerIntegrationDetailsFragment';
import { FinderApplicationPublicAccessEntrypointContextDataFragmentDoc } from '../fragments/FinderApplicationPublicAccessEntrypointContextData';
import { EntrypointFinderApplicationPublicModuleFragmentDoc } from '../fragments/EntrypointFinderApplicationPublicModule';
import { FinderApplicationModuleEmailContentSpecsFragmentDoc } from '../fragments/FinderApplicationModuleEmailContentSpecs';
import { TranslatedTextDataFragmentDoc } from '../fragments/TranslationTextData';
import { ModuleDisclaimersDataFragmentDoc } from '../fragments/ModuleDisclaimersData';
import { FinderApplicationEntrypointContextDataFragmentDoc } from '../fragments/FinderApplicationEntrypointContextData';
import { EntrypointFinderApplicationPrivateModuleFragmentDoc } from '../fragments/EntrypointFinderApplicationPrivateModule';
import { MenuItemSpecsFragmentDoc } from '../fragments/MenuItemSpecs';
import { TranslatedStringSpecsFragmentDoc } from '../fragments/TranslatedStringSpecs';
import { PathScriptSpecsFragmentDoc } from '../fragments/PathScriptSpecs';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GetRouterContextQueryVariables = SchemaTypes.Exact<{
  routerId: SchemaTypes.Scalars['ObjectID']['input'];
}>;


export type GetRouterContextQuery = (
  { __typename: 'Query' }
  & { router?: SchemaTypes.Maybe<(
    { __typename: 'Router' }
    & RouterContextDataFragment
  )> }
);


export const GetRouterContextDocument = /*#__PURE__*/ gql`
    query getRouterContext($routerId: ObjectID!) {
  router: getRouter(id: $routerId) {
    ...RouterContextData
  }
}
    ${RouterContextDataFragmentDoc}
${CompanyWithPermissionsContextDataFragmentDoc}
${CompanyContextDataFragmentDoc}
${TranslatedStringDataFragmentDoc}
${LanguagePackContextDataFragmentDoc}
${UploadFileWithPreviewFormDataFragmentDoc}
${AvailableModulesDataFragmentDoc}
${CompanyDealerDataFragmentDoc}
${MaintenanceUpdateFragmentDoc}
${SimpleVersioningDataFragmentDoc}
${AuthorDataFragmentDoc}
${UserAvatarSpecsFragmentDoc}
${EdmEmailFooterPublicDataFragmentDoc}
${EdmSocialMediaDataFragmentDoc}
${RouterLayoutContextDataFragmentDoc}
${EndpointContextDataFragmentDoc}
${DummyPrivatePageEndpointContextDataFragmentDoc}
${StandardApplicationEntrypointContextDataFragmentDoc}
${ApplicationMarketTypeFragmentFragmentDoc}
${DealerMarketDataFragmentDoc}
${BankDealerMarketDataFragmentDoc}
${NzFeesDealerMarketDataFragmentDoc}
${DealershipSettingSpecDataFragmentDoc}
${AppointmentModuleSpecsFragmentDoc}
${AppointmentTimeSlotDataFragmentDoc}
${NamirialSigningModuleSpecsFragmentDoc}
${NamirialSettingsSpecFragmentDoc}
${AppointmentModuleEmailContentsSpecsFragmentDoc}
${AppointmentModuleEmailContentCustomerSpecsFragmentDoc}
${AppointmentModuleEmailContentSpecsFragmentDoc}
${DealerTranslatedStringSettingDataFragmentDoc}
${DealerBooleanSettingDataFragmentDoc}
${AppointmentModuleEmailContentSalesPersonSpecsFragmentDoc}
${AppointmentModuleEmailContentFinderReservationSpecsFragmentDoc}
${VisitAppointmentModuleSpecsFragmentDoc}
${CompanyInModuleOptionDataFragmentDoc}
${VehicleDataWithPorscheCodeIntegrationSettingSpecsFragmentDoc}
${TimeSlotDataFragmentDoc}
${VisitAppointmentModuleEmailContentsSpecsFragmentDoc}
${VisitAppointmentModuleEmailContentCustomerSpecsFragmentDoc}
${VisitAppointmentModuleEmailContentSpecsFragmentDoc}
${VisitAppointmentModuleEmailContentSalesPersonSpecsFragmentDoc}
${InsurerEntrypointContextDataFragmentDoc}
${DealerDisclaimersConfiguratorDataFragmentDoc}
${FlexibleDiscountDataFragmentDoc}
${KycExtraSettingsSpecsFragmentDoc}
${ApplicationListEndpointContextDataFragmentDoc}
${LeadListEndpointContextDataFragmentDoc}
${EventApplicationEntrypointContextDataFragmentDoc}
${LaunchPadApplicationEntrypointContextDataFragmentDoc}
${LaunchPadModuleSpecsFragmentDoc}
${CounterSettingsSpecsFragmentDoc}
${ConfiguratorApplicationEntrypointContextDataFragmentDoc}
${MyInfoSettingSpecFragmentDoc}
${DepositAmountDataFragmentDoc}
${CustomerListEndpointContextDataFragmentDoc}
${MobilityApplicationEntrypointContextDataFragmentDoc}
${DealerBookingCodeDataFragmentDoc}
${MobilityLocationDataFragmentDoc}
${UserPreviewDataFragmentDoc}
${DateUnitDataFragmentDoc}
${WebpageEndpointContextDataFragmentDoc}
${WebPageEndpointSpecsFragmentDoc}
${WebPagePathDataFragmentDoc}
${StandardApplicationPublicAccessEntrypointContextDataFragmentDoc}
${DealerApplicationFragmentFragmentDoc}
${DealerContactFragmentFragmentDoc}
${DealerSocialMediaFragmentFragmentDoc}
${DealerDisclaimersFragmentFragmentDoc}
${DealerIntegrationDetailsFragmentFragmentDoc}
${FinderApplicationPublicAccessEntrypointContextDataFragmentDoc}
${EntrypointFinderApplicationPublicModuleFragmentDoc}
${FinderApplicationModuleEmailContentSpecsFragmentDoc}
${TranslatedTextDataFragmentDoc}
${ModuleDisclaimersDataFragmentDoc}
${FinderApplicationEntrypointContextDataFragmentDoc}
${EntrypointFinderApplicationPrivateModuleFragmentDoc}
${MenuItemSpecsFragmentDoc}
${TranslatedStringSpecsFragmentDoc}
${PathScriptSpecsFragmentDoc}`;

/**
 * __useGetRouterContextQuery__
 *
 * To run a query within a React component, call `useGetRouterContextQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetRouterContextQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetRouterContextQuery({
 *   variables: {
 *      routerId: // value for 'routerId'
 *   },
 * });
 */
export function useGetRouterContextQuery(baseOptions: Apollo.QueryHookOptions<GetRouterContextQuery, GetRouterContextQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetRouterContextQuery, GetRouterContextQueryVariables>(GetRouterContextDocument, options);
      }
export function useGetRouterContextLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetRouterContextQuery, GetRouterContextQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetRouterContextQuery, GetRouterContextQueryVariables>(GetRouterContextDocument, options);
        }
export type GetRouterContextQueryHookResult = ReturnType<typeof useGetRouterContextQuery>;
export type GetRouterContextLazyQueryHookResult = ReturnType<typeof useGetRouterContextLazyQuery>;
export type GetRouterContextQueryResult = Apollo.QueryResult<GetRouterContextQuery, GetRouterContextQueryVariables>;