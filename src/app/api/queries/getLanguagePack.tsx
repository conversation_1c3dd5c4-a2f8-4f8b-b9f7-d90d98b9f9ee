import type * as SchemaTypes from '../types';

import type { LanguagePackSpecsFragment } from '../fragments/LanguagePackSpecs';
import type { SimpleVersioningDataFragment } from '../fragments/SimpleVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from '../fragments/AuthorData';
import { gql } from '@apollo/client';
import { LanguagePackSpecsFragmentDoc } from '../fragments/LanguagePackSpecs';
import { SimpleVersioningDataFragmentDoc } from '../fragments/SimpleVersioningData';
import { AuthorDataFragmentDoc } from '../fragments/AuthorData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GetLanguagePackQueryVariables = SchemaTypes.Exact<{
  id: SchemaTypes.Scalars['ObjectID']['input'];
}>;


export type GetLanguagePackQuery = (
  { __typename: 'Query' }
  & { language?: SchemaTypes.Maybe<(
    { __typename: 'LanguagePack' }
    & LanguagePackSpecsFragment
  )> }
);


export const GetLanguagePackDocument = /*#__PURE__*/ gql`
    query getLanguagePack($id: ObjectID!) {
  language: getLanguagePack(id: $id) {
    ...LanguagePackSpecs
  }
}
    ${LanguagePackSpecsFragmentDoc}
${SimpleVersioningDataFragmentDoc}
${AuthorDataFragmentDoc}`;

/**
 * __useGetLanguagePackQuery__
 *
 * To run a query within a React component, call `useGetLanguagePackQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetLanguagePackQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetLanguagePackQuery({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useGetLanguagePackQuery(baseOptions: Apollo.QueryHookOptions<GetLanguagePackQuery, GetLanguagePackQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetLanguagePackQuery, GetLanguagePackQueryVariables>(GetLanguagePackDocument, options);
      }
export function useGetLanguagePackLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetLanguagePackQuery, GetLanguagePackQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetLanguagePackQuery, GetLanguagePackQueryVariables>(GetLanguagePackDocument, options);
        }
export type GetLanguagePackQueryHookResult = ReturnType<typeof useGetLanguagePackQuery>;
export type GetLanguagePackLazyQueryHookResult = ReturnType<typeof useGetLanguagePackLazyQuery>;
export type GetLanguagePackQueryResult = Apollo.QueryResult<GetLanguagePackQuery, GetLanguagePackQueryVariables>;