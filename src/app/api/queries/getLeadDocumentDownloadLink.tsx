import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type GetLeadDocumentDownloadLinkQueryVariables = SchemaTypes.Exact<{
  leadId: SchemaTypes.Scalars['ObjectID']['input'];
  documentId: SchemaTypes.Scalars['ObjectID']['input'];
}>;


export type GetLeadDocumentDownloadLinkQuery = (
  { __typename: 'Query' }
  & { result?: SchemaTypes.Maybe<(
    { __typename: 'Download' }
    & Pick<SchemaTypes.Download, 'link'>
    & { encryption?: SchemaTypes.Maybe<(
      { __typename: 'DownloadEncryption' }
      & Pick<SchemaTypes.DownloadEncryption, 'enabled' | 'hint' | 'password'>
    )> }
  )> }
);


export const GetLeadDocumentDownloadLinkDocument = /*#__PURE__*/ gql`
    query getLeadDocumentDownloadLink($leadId: ObjectID!, $documentId: ObjectID!) {
  result: getLeadDocumentDownloadLink(leadId: $leadId, documentId: $documentId) {
    link
    encryption {
      enabled
      hint
      password
    }
  }
}
    `;

/**
 * __useGetLeadDocumentDownloadLinkQuery__
 *
 * To run a query within a React component, call `useGetLeadDocumentDownloadLinkQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetLeadDocumentDownloadLinkQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetLeadDocumentDownloadLinkQuery({
 *   variables: {
 *      leadId: // value for 'leadId'
 *      documentId: // value for 'documentId'
 *   },
 * });
 */
export function useGetLeadDocumentDownloadLinkQuery(baseOptions: Apollo.QueryHookOptions<GetLeadDocumentDownloadLinkQuery, GetLeadDocumentDownloadLinkQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetLeadDocumentDownloadLinkQuery, GetLeadDocumentDownloadLinkQueryVariables>(GetLeadDocumentDownloadLinkDocument, options);
      }
export function useGetLeadDocumentDownloadLinkLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetLeadDocumentDownloadLinkQuery, GetLeadDocumentDownloadLinkQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetLeadDocumentDownloadLinkQuery, GetLeadDocumentDownloadLinkQueryVariables>(GetLeadDocumentDownloadLinkDocument, options);
        }
export type GetLeadDocumentDownloadLinkQueryHookResult = ReturnType<typeof useGetLeadDocumentDownloadLinkQuery>;
export type GetLeadDocumentDownloadLinkLazyQueryHookResult = ReturnType<typeof useGetLeadDocumentDownloadLinkLazyQuery>;
export type GetLeadDocumentDownloadLinkQueryResult = Apollo.QueryResult<GetLeadDocumentDownloadLinkQuery, GetLeadDocumentDownloadLinkQueryVariables>;