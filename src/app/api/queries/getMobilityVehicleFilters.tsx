import type * as SchemaTypes from '../types';

import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import { gql } from '@apollo/client';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type GetMobilityVehicleFiltersQueryVariables = SchemaTypes.Exact<{
  moduleId: SchemaTypes.Scalars['ObjectID']['input'];
  productionOnly?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['Boolean']['input']>;
}>;


export type GetMobilityVehicleFiltersQuery = (
  { __typename: 'Query' }
  & { filters: (
    { __typename: 'MobilityVehicleFilters' }
    & Pick<SchemaTypes.MobilityVehicleFilters, 'id'>
    & { models: Array<(
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    )>, submodels: Array<(
      { __typename: 'SubmodelFilter' }
      & Pick<SchemaTypes.SubmodelFilter, 'parentModelId'>
      & { name: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ), parentModel: (
        { __typename: 'LocalModel' }
        & Pick<SchemaTypes.LocalModel, 'id'>
        & { name: (
          { __typename: 'TranslatedString' }
          & TranslatedStringDataFragment
        ) }
      ) }
    )>, dailyRates: Array<(
      { __typename: 'RangeAmount' }
      & Pick<SchemaTypes.RangeAmount, 'from' | 'to'>
    )> }
  ) }
);


export const GetMobilityVehicleFiltersDocument = /*#__PURE__*/ gql`
    query getMobilityVehicleFilters($moduleId: ObjectID!, $productionOnly: Boolean) {
  filters: getMobilityVehicleFilters(
    moduleId: $moduleId
    productionOnly: $productionOnly
  ) {
    id
    models {
      ...TranslatedStringData
    }
    submodels {
      name {
        ...TranslatedStringData
      }
      parentModelId
      parentModel {
        id
        name {
          ...TranslatedStringData
        }
      }
    }
    dailyRates {
      from
      to
    }
  }
}
    ${TranslatedStringDataFragmentDoc}`;

/**
 * __useGetMobilityVehicleFiltersQuery__
 *
 * To run a query within a React component, call `useGetMobilityVehicleFiltersQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetMobilityVehicleFiltersQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetMobilityVehicleFiltersQuery({
 *   variables: {
 *      moduleId: // value for 'moduleId'
 *      productionOnly: // value for 'productionOnly'
 *   },
 * });
 */
export function useGetMobilityVehicleFiltersQuery(baseOptions: Apollo.QueryHookOptions<GetMobilityVehicleFiltersQuery, GetMobilityVehicleFiltersQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetMobilityVehicleFiltersQuery, GetMobilityVehicleFiltersQueryVariables>(GetMobilityVehicleFiltersDocument, options);
      }
export function useGetMobilityVehicleFiltersLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetMobilityVehicleFiltersQuery, GetMobilityVehicleFiltersQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetMobilityVehicleFiltersQuery, GetMobilityVehicleFiltersQueryVariables>(GetMobilityVehicleFiltersDocument, options);
        }
export type GetMobilityVehicleFiltersQueryHookResult = ReturnType<typeof useGetMobilityVehicleFiltersQuery>;
export type GetMobilityVehicleFiltersLazyQueryHookResult = ReturnType<typeof useGetMobilityVehicleFiltersLazyQuery>;
export type GetMobilityVehicleFiltersQueryResult = Apollo.QueryResult<GetMobilityVehicleFiltersQuery, GetMobilityVehicleFiltersQueryVariables>;