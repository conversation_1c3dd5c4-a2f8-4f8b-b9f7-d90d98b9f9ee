import type * as SchemaTypes from '../types';

import type { LabelsPublicDataFragment } from '../fragments/LabelsPublicData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import type { VehicleSpecs_FinderVehicle_Fragment, VehicleSpecs_LocalMake_Fragment, VehicleSpecs_LocalModel_Fragment, VehicleSpecs_LocalVariant_Fragment } from '../fragments/VehicleSpecs';
import type { LocalVariantSpecsFragment } from '../fragments/LocalVariantSpecs';
import type { UploadFileWithPreviewFormDataFragment } from '../fragments/UploadFileWithPreviewFormData';
import type { LocalModelSpecsFragment } from '../fragments/LocalModelSpecs';
import type { LocalMakeSpecsFragment } from '../fragments/LocalMakeSpecs';
import type { AdvancedVersioningDataFragment } from '../fragments/AdvancedVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from '../fragments/AuthorData';
import type { FinderVehicleSpecsFragment } from '../fragments/FinderVehicleSpecs';
import type { FullListingValueFragment, FormattedDateDataFragment, LocalizedStringDataFragment, LocalizedValueDataFragment, NumberUnitDataFragment } from '../fragments/finderListing.fragment';
import { gql } from '@apollo/client';
import { LabelsPublicDataFragmentDoc } from '../fragments/LabelsPublicData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import { VehicleSpecsFragmentDoc } from '../fragments/VehicleSpecs';
import { LocalVariantSpecsFragmentDoc } from '../fragments/LocalVariantSpecs';
import { UploadFileWithPreviewFormDataFragmentDoc } from '../fragments/UploadFileWithPreviewFormData';
import { LocalModelSpecsFragmentDoc } from '../fragments/LocalModelSpecs';
import { LocalMakeSpecsFragmentDoc } from '../fragments/LocalMakeSpecs';
import { AdvancedVersioningDataFragmentDoc } from '../fragments/AdvancedVersioningData';
import { AuthorDataFragmentDoc } from '../fragments/AuthorData';
import { FinderVehicleSpecsFragmentDoc } from '../fragments/FinderVehicleSpecs';
import { FullListingValueFragmentDoc, FormattedDateDataFragmentDoc, LocalizedStringDataFragmentDoc, LocalizedValueDataFragmentDoc, NumberUnitDataFragmentDoc } from '../fragments/finderListing.fragment';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GetLabelsByFeatureModuleIdQueryVariables = SchemaTypes.Exact<{
  moduleId: SchemaTypes.Scalars['ObjectID']['input'];
}>;


export type GetLabelsByFeatureModuleIdQuery = (
  { __typename: 'Query' }
  & { label: Array<(
    { __typename: 'Labels' }
    & LabelsPublicDataFragment
  )> }
);


export const GetLabelsByFeatureModuleIdDocument = /*#__PURE__*/ gql`
    query getLabelsByFeatureModuleId($moduleId: ObjectID!) {
  label: getLabelsByFeatureModuleId(moduleId: $moduleId) {
    ...LabelsPublicData
  }
}
    ${LabelsPublicDataFragmentDoc}
${TranslatedStringDataFragmentDoc}
${VehicleSpecsFragmentDoc}
${LocalVariantSpecsFragmentDoc}
${UploadFileWithPreviewFormDataFragmentDoc}
${LocalModelSpecsFragmentDoc}
${LocalMakeSpecsFragmentDoc}
${AdvancedVersioningDataFragmentDoc}
${AuthorDataFragmentDoc}
${FinderVehicleSpecsFragmentDoc}
${FullListingValueFragmentDoc}
${FormattedDateDataFragmentDoc}
${LocalizedStringDataFragmentDoc}
${LocalizedValueDataFragmentDoc}
${NumberUnitDataFragmentDoc}`;

/**
 * __useGetLabelsByFeatureModuleIdQuery__
 *
 * To run a query within a React component, call `useGetLabelsByFeatureModuleIdQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetLabelsByFeatureModuleIdQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetLabelsByFeatureModuleIdQuery({
 *   variables: {
 *      moduleId: // value for 'moduleId'
 *   },
 * });
 */
export function useGetLabelsByFeatureModuleIdQuery(baseOptions: Apollo.QueryHookOptions<GetLabelsByFeatureModuleIdQuery, GetLabelsByFeatureModuleIdQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetLabelsByFeatureModuleIdQuery, GetLabelsByFeatureModuleIdQueryVariables>(GetLabelsByFeatureModuleIdDocument, options);
      }
export function useGetLabelsByFeatureModuleIdLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetLabelsByFeatureModuleIdQuery, GetLabelsByFeatureModuleIdQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetLabelsByFeatureModuleIdQuery, GetLabelsByFeatureModuleIdQueryVariables>(GetLabelsByFeatureModuleIdDocument, options);
        }
export type GetLabelsByFeatureModuleIdQueryHookResult = ReturnType<typeof useGetLabelsByFeatureModuleIdQuery>;
export type GetLabelsByFeatureModuleIdLazyQueryHookResult = ReturnType<typeof useGetLabelsByFeatureModuleIdLazyQuery>;
export type GetLabelsByFeatureModuleIdQueryResult = Apollo.QueryResult<GetLabelsByFeatureModuleIdQuery, GetLabelsByFeatureModuleIdQueryVariables>;