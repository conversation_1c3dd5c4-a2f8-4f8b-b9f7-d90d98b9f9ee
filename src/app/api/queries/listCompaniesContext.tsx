import type * as SchemaTypes from '../types';

import type { CompanyWithPermissionsContextDataFragment } from '../fragments/CompanyWithPermissionsContextData';
import type { CompanyContextDataFragment } from '../fragments/CompanyContextData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import type { LanguagePackContextDataFragment } from '../fragments/LanguagePackContextData';
import type { UploadFileWithPreviewFormDataFragment } from '../fragments/UploadFileWithPreviewFormData';
import type { AvailableModulesDataFragment } from '../fragments/AvailableModulesData';
import type { CompanyDealerDataFragment } from '../fragments/CompanyDealerData';
import type { MaintenanceUpdateFragment } from '../fragments/MaintenanceUpdate';
import type { SimpleVersioningDataFragment } from '../fragments/SimpleVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from '../fragments/AuthorData';
import type { UserAvatarSpecsFragment } from '../fragments/UserAvatarSpecs';
import type { EdmEmailFooterPublicDataFragment } from '../fragments/EdmEmailFooterPublicData';
import type { EdmSocialMediaDataFragment } from '../fragments/EdmSocialMediaData';
import { gql } from '@apollo/client';
import { CompanyWithPermissionsContextDataFragmentDoc } from '../fragments/CompanyWithPermissionsContextData';
import { CompanyContextDataFragmentDoc } from '../fragments/CompanyContextData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import { LanguagePackContextDataFragmentDoc } from '../fragments/LanguagePackContextData';
import { UploadFileWithPreviewFormDataFragmentDoc } from '../fragments/UploadFileWithPreviewFormData';
import { AvailableModulesDataFragmentDoc } from '../fragments/AvailableModulesData';
import { CompanyDealerDataFragmentDoc } from '../fragments/CompanyDealerData';
import { MaintenanceUpdateFragmentDoc } from '../fragments/MaintenanceUpdate';
import { SimpleVersioningDataFragmentDoc } from '../fragments/SimpleVersioningData';
import { AuthorDataFragmentDoc } from '../fragments/AuthorData';
import { UserAvatarSpecsFragmentDoc } from '../fragments/UserAvatarSpecs';
import { EdmEmailFooterPublicDataFragmentDoc } from '../fragments/EdmEmailFooterPublicData';
import { EdmSocialMediaDataFragmentDoc } from '../fragments/EdmSocialMediaData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListCompaniesContextQueryVariables = SchemaTypes.Exact<{ [key: string]: never; }>;


export type ListCompaniesContextQuery = (
  { __typename: 'Query' }
  & { list: (
    { __typename: 'PaginatedCompanies' }
    & Pick<SchemaTypes.PaginatedCompanies, 'count'>
    & { items: Array<(
      { __typename: 'Company' }
      & CompanyWithPermissionsContextDataFragment
    )> }
  ) }
);


export const ListCompaniesContextDocument = /*#__PURE__*/ gql`
    query listCompaniesContext {
  list: listCompanies {
    count
    items {
      ...CompanyWithPermissionsContextData
    }
  }
}
    ${CompanyWithPermissionsContextDataFragmentDoc}
${CompanyContextDataFragmentDoc}
${TranslatedStringDataFragmentDoc}
${LanguagePackContextDataFragmentDoc}
${UploadFileWithPreviewFormDataFragmentDoc}
${AvailableModulesDataFragmentDoc}
${CompanyDealerDataFragmentDoc}
${MaintenanceUpdateFragmentDoc}
${SimpleVersioningDataFragmentDoc}
${AuthorDataFragmentDoc}
${UserAvatarSpecsFragmentDoc}
${EdmEmailFooterPublicDataFragmentDoc}
${EdmSocialMediaDataFragmentDoc}`;

/**
 * __useListCompaniesContextQuery__
 *
 * To run a query within a React component, call `useListCompaniesContextQuery` and pass it any options that fit your needs.
 * When your component renders, `useListCompaniesContextQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListCompaniesContextQuery({
 *   variables: {
 *   },
 * });
 */
export function useListCompaniesContextQuery(baseOptions?: Apollo.QueryHookOptions<ListCompaniesContextQuery, ListCompaniesContextQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListCompaniesContextQuery, ListCompaniesContextQueryVariables>(ListCompaniesContextDocument, options);
      }
export function useListCompaniesContextLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListCompaniesContextQuery, ListCompaniesContextQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListCompaniesContextQuery, ListCompaniesContextQueryVariables>(ListCompaniesContextDocument, options);
        }
export type ListCompaniesContextQueryHookResult = ReturnType<typeof useListCompaniesContextQuery>;
export type ListCompaniesContextLazyQueryHookResult = ReturnType<typeof useListCompaniesContextLazyQuery>;
export type ListCompaniesContextQueryResult = Apollo.QueryResult<ListCompaniesContextQuery, ListCompaniesContextQueryVariables>;