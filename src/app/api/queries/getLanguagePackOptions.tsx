import type * as SchemaTypes from '../types';

import type { LanguagePackOptionDataFragment } from '../fragments/LanguagePackOptionData';
import { gql } from '@apollo/client';
import { LanguagePackOptionDataFragmentDoc } from '../fragments/LanguagePackOptionData';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type GetLanguagePackOptionsQueryVariables = SchemaTypes.Exact<{
  filter?: SchemaTypes.InputMaybe<SchemaTypes.LanguagePackFilteringRule>;
}>;


export type GetLanguagePackOptionsQuery = (
  { __typename: 'Query' }
  & { languages: (
    { __typename: 'PaginatedLanguagePacks' }
    & { items: Array<(
      { __typename: 'LanguagePack' }
      & LanguagePackOptionDataFragment
    )> }
  ) }
);


export const GetLanguagePackOptionsDocument = /*#__PURE__*/ gql`
    query getLanguagePackOptions($filter: LanguagePackFilteringRule) {
  languages: listLanguagePacks(filter: $filter) {
    items {
      ...LanguagePackOptionData
    }
  }
}
    ${LanguagePackOptionDataFragmentDoc}`;

/**
 * __useGetLanguagePackOptionsQuery__
 *
 * To run a query within a React component, call `useGetLanguagePackOptionsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetLanguagePackOptionsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetLanguagePackOptionsQuery({
 *   variables: {
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useGetLanguagePackOptionsQuery(baseOptions?: Apollo.QueryHookOptions<GetLanguagePackOptionsQuery, GetLanguagePackOptionsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetLanguagePackOptionsQuery, GetLanguagePackOptionsQueryVariables>(GetLanguagePackOptionsDocument, options);
      }
export function useGetLanguagePackOptionsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetLanguagePackOptionsQuery, GetLanguagePackOptionsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetLanguagePackOptionsQuery, GetLanguagePackOptionsQueryVariables>(GetLanguagePackOptionsDocument, options);
        }
export type GetLanguagePackOptionsQueryHookResult = ReturnType<typeof useGetLanguagePackOptionsQuery>;
export type GetLanguagePackOptionsLazyQueryHookResult = ReturnType<typeof useGetLanguagePackOptionsLazyQuery>;
export type GetLanguagePackOptionsQueryResult = Apollo.QueryResult<GetLanguagePackOptionsQuery, GetLanguagePackOptionsQueryVariables>;