import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type GetLanguagePackListFiltersQueryVariables = SchemaTypes.Exact<{
  filter?: SchemaTypes.InputMaybe<SchemaTypes.LanguagePackFilteringRule>;
}>;


export type GetLanguagePackListFiltersQuery = (
  { __typename: 'Query' }
  & { languagePackListFilters: (
    { __typename: 'LanguagePackFilters' }
    & Pick<SchemaTypes.LanguagePackFilters, 'orientation'>
  ) }
);


export const GetLanguagePackListFiltersDocument = /*#__PURE__*/ gql`
    query getLanguagePackListFilters($filter: LanguagePackFilteringRule) {
  languagePackListFilters: getLanguagePackListFilters(filter: $filter) {
    orientation
  }
}
    `;

/**
 * __useGetLanguagePackListFiltersQuery__
 *
 * To run a query within a React component, call `useGetLanguagePackListFiltersQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetLanguagePackListFiltersQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetLanguagePackListFiltersQuery({
 *   variables: {
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useGetLanguagePackListFiltersQuery(baseOptions?: Apollo.QueryHookOptions<GetLanguagePackListFiltersQuery, GetLanguagePackListFiltersQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetLanguagePackListFiltersQuery, GetLanguagePackListFiltersQueryVariables>(GetLanguagePackListFiltersDocument, options);
      }
export function useGetLanguagePackListFiltersLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetLanguagePackListFiltersQuery, GetLanguagePackListFiltersQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetLanguagePackListFiltersQuery, GetLanguagePackListFiltersQueryVariables>(GetLanguagePackListFiltersDocument, options);
        }
export type GetLanguagePackListFiltersQueryHookResult = ReturnType<typeof useGetLanguagePackListFiltersQuery>;
export type GetLanguagePackListFiltersLazyQueryHookResult = ReturnType<typeof useGetLanguagePackListFiltersLazyQuery>;
export type GetLanguagePackListFiltersQueryResult = Apollo.QueryResult<GetLanguagePackListFiltersQuery, GetLanguagePackListFiltersQueryVariables>;