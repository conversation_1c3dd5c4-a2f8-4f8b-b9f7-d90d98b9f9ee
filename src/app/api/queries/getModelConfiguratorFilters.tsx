import type * as SchemaTypes from '../types';

import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import { gql } from '@apollo/client';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type GetModelConfiguratorFiltersQueryVariables = SchemaTypes.Exact<{
  moduleId: SchemaTypes.Scalars['ObjectID']['input'];
  productionOnly?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['Boolean']['input']>;
}>;


export type GetModelConfiguratorFiltersQuery = (
  { __typename: 'Query' }
  & { configuratorFilters: (
    { __typename: 'ModelConfiguratorFilters' }
    & Pick<SchemaTypes.ModelConfiguratorFilters, 'bodyTypes'>
    & { engines: Array<(
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    )> }
  ) }
);


export const GetModelConfiguratorFiltersDocument = /*#__PURE__*/ gql`
    query getModelConfiguratorFilters($moduleId: ObjectID!, $productionOnly: Boolean) {
  configuratorFilters: getModelConfiguratorFilters(
    moduleId: $moduleId
    productionOnly: $productionOnly
  ) {
    bodyTypes
    engines {
      ...TranslatedStringData
    }
  }
}
    ${TranslatedStringDataFragmentDoc}`;

/**
 * __useGetModelConfiguratorFiltersQuery__
 *
 * To run a query within a React component, call `useGetModelConfiguratorFiltersQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetModelConfiguratorFiltersQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetModelConfiguratorFiltersQuery({
 *   variables: {
 *      moduleId: // value for 'moduleId'
 *      productionOnly: // value for 'productionOnly'
 *   },
 * });
 */
export function useGetModelConfiguratorFiltersQuery(baseOptions: Apollo.QueryHookOptions<GetModelConfiguratorFiltersQuery, GetModelConfiguratorFiltersQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetModelConfiguratorFiltersQuery, GetModelConfiguratorFiltersQueryVariables>(GetModelConfiguratorFiltersDocument, options);
      }
export function useGetModelConfiguratorFiltersLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetModelConfiguratorFiltersQuery, GetModelConfiguratorFiltersQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetModelConfiguratorFiltersQuery, GetModelConfiguratorFiltersQueryVariables>(GetModelConfiguratorFiltersDocument, options);
        }
export type GetModelConfiguratorFiltersQueryHookResult = ReturnType<typeof useGetModelConfiguratorFiltersQuery>;
export type GetModelConfiguratorFiltersLazyQueryHookResult = ReturnType<typeof useGetModelConfiguratorFiltersLazyQuery>;
export type GetModelConfiguratorFiltersQueryResult = Apollo.QueryResult<GetModelConfiguratorFiltersQuery, GetModelConfiguratorFiltersQueryVariables>;