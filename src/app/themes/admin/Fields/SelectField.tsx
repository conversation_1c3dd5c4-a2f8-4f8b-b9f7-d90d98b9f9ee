import { useField } from 'formik';
import { useEffect, useMemo } from 'react';
import FormItem from '../../../components/fields/FormItem';
import BaseSelectField, {
    SelectFieldProps,
    getSingleOptionValue,
    isSelectOptionGroup,
} from '../../../components/fields/SelectField';
import DisplayField from './DisplayField';

const SelectField = (props: SelectFieldProps) => {
    const {
        disabled,
        options = [],
        label,
        name,
        required,
        mode,
        onChange,
        showTooltipWhenDisabled = false,
        hideValueWhenDisabled = false,
        tooltip,
    } = props;
    const [field, meta, { setValue }] = useField({ name });

    // only value available in options provided
    const onlyValue = useMemo(() => getSingleOptionValue(options), [options]);

    useEffect(() => {
        if (!options || !onlyValue || !required) {
            return;
        }

        if (mode === 'multiple' && Array.isArray(field.value)) {
            // Ensure we only update if the value is not already set
            if (!field.value.includes(onlyValue)) {
                // call form set value and on change
                setValue([onlyValue]);
                onChange?.([onlyValue]);
            }
        } else if (mode !== 'multiple' && field.value !== onlyValue) {
            // Ensure we only update if the value is different
            // call form set value and on change
            setValue(onlyValue);
            onChange?.(onlyValue);
        }
    }, [options, setValue, required, mode, field.value, onChange, onlyValue]);

    if (disabled || (onlyValue && required)) {
        const displayValue =
            options.length === 1 && required
                ? options[0]?.label.toString()
                : options
                      .find(option => !isSelectOptionGroup(option) && option.value === field.value)
                      ?.label.toString();

        // Determine the value to display based on hideValueWhenDisabled prop
        const valueToDisplay = disabled && hideValueWhenDisabled ? '' : displayValue?.toString();

        // Determine tooltip behavior based on showTooltipWhenDisabled prop
        const shouldShowTooltip = !disabled || showTooltipWhenDisabled;
        const tooltipToShow = shouldShowTooltip ? tooltip : undefined;

        // If we need tooltip support, wrap with FormItem, otherwise use DisplayField directly
        if (tooltipToShow) {
            return (
                <FormItem label={label} meta={meta} tooltip={tooltipToShow}>
                    <DisplayField value={valueToDisplay || ''} />
                </FormItem>
            );
        }

        return <DisplayField label={label} value={valueToDisplay || ''} />;
    }

    return <BaseSelectField {...props} applyDefaultStyles={false} />;
};

export default SelectField;
