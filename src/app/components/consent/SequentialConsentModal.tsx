import React from 'react';
import { useTranslation } from 'react-i18next';
import type { ConsentsAndDeclarations } from '../../api/types';
import { useThemeComponents } from '../../themes/hooks';
import useTranslatedString from '../../utilities/useTranslatedString';

export type SequentialConsentModalProps = {
    open: boolean;
    consents: ConsentsAndDeclarations[];
    currentIndex: number;
    totalCount: number;
    onAgree: () => void;
    onCancel: () => void;
    onClose: () => void;
};

const SequentialConsentModal: React.FC<SequentialConsentModalProps> = ({
    open,
    consents,
    currentIndex,
    totalCount,
    onAgree,
    onCancel,
    onClose,
}) => {
    const { t } = useTranslation(['consentsAndDeclarations']);
    const { ConsentModal } = useThemeComponents();
    const currentConsent = consents[currentIndex];

    if (!currentConsent) {
        return null;
    }

    // eslint-disable-next-line max-len
    const title = `${t('consentsAndDeclarations:modal.consent')} ${currentIndex + 1} ${t('consentsAndDeclarations:modal.of')} ${totalCount}`;

    return (
        <ConsentModal
            key={`sequential-modal-${currentConsent.id}`}
            disabled={false}
            onCancel={onCancel}
            onOk={onAgree}
            open={open}
            title={title}
        >
            <div>
                <h3>{translatedString(currentConsent.title) || currentConsent.displayName}</h3>
                {'legalMarkup' in currentConsent && currentConsent.legalMarkup && (
                    <div dangerouslySetInnerHTML={{ __html: translatedString(currentConsent.legalMarkup) || '' }} />
                )}
            </div>
        </ConsentModal>
    );
};

export default SequentialConsentModal;
