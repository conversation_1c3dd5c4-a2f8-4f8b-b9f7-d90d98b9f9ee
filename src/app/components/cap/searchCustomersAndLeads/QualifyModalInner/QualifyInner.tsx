import { Col, Row } from 'antd';
import { Formik, FormikProps, useFormikContext } from 'formik';
import { useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { GetLocalVariantsOptionsQuery, KycFieldSpecsFragment, LaunchPadModuleSpecsFragment } from '../../../../api';
import { useGetLocalVariantsOptionsQuery } from '../../../../api/queries/getLocalVariantsOptions';
import { usePrefetchKycFieldsForQualifyQuery } from '../../../../api/queries/prefetchKYCFieldsForQualify';
import { Purpose, CustomerKind, LocalCustomerManagementModule, FinderVehicleCondition } from '../../../../api/types';
// eslint-disable-next-line max-len
import CustomerDetails from '../../../../pages/portal/MobilityApplicationEntrypoint/ApplicantKycPage/CustomerDetails';
import { FeatureKYCPageLabel } from '../../../../pages/portal/MobilityApplicationEntrypoint/ApplicantKycPage/shared';
import { getApplicantKyc } from '../../../../pages/portal/StandardApplicationEntrypoint/KYCPage/getKyc';
import { useThemeComponents } from '../../../../themes/hooks';
import { getInitialValues } from '../../../../utilities/kycPresets';
import useKYCFormValidator from '../../../../utilities/kycPresets/useKYCValidators';
import useSystemOptions from '../../../../utilities/useSystemOptions';
import useValidator from '../../../../utilities/useValidator';
import validators from '../../../../utilities/validators';
import FormAutoTouch from '../../../FormAutoTouch';
import LoadingElement from '../../../LoadingElement';
import Form from '../../../fields/Form';
import { defaultFilterOption } from '../../../fields/SelectField';
import { defaultColSpan, InfoMessageBoxContainer } from '../../../leads/shared';
import renderAlertDescription from '../../../leads/shared/renderAlertDescription';
import useModelVariantOptions from '../../../leads/shared/useModelVariantOptions';
import { SearchCapCustomerKYCProps, SubmissionPurpose } from '../types';
import useCampaignOptions from '../useCampaignOptions';
import type { StateAndDispatch } from '../useReducer';
import useSubmitKyc from '../useSubmitKyc';
import type { QualifyContactValues } from './types';

type QualifyContactFormProps = StateAndDispatch & {
    formName: string;
    onClose: SearchCapCustomerKYCProps['onClose'];
    formRef?: React.RefObject<FormikProps<QualifyContactValues>>;
};

/** Props for the Inner form component */
type InnerProps = {
    formName: string;
    /** KYC field specifications */
    kycState: KycFieldSpecsFragment[];
    kycExtraSettings: LocalCustomerManagementModule['extraSettings'];
    /** List of available assignees */
    availableAssignees: { label: string; value: string }[];
    /** Available campaign options */
    campaignOptions: { label: string; value: string }[];
    /** Vehicle condition options */
    finderVehicleConditionOptions: { label: string; value: string }[];
    /** ID of salesperson from CAP if available */
    salespersonFromCap: string | null;
    /** Alert message for CAP salesperson */
    salespersonAlert: { title: string | null; description: React.ReactNode | null };
    /** Dispatch function for state updates */
    dispatch: StateAndDispatch['dispatch'];
    dealerId: string;
    availableLocalVariantsForModule: GetLocalVariantsOptionsQuery;
    launchpadModule: LaunchPadModuleSpecsFragment;
};

const Inner = ({
    kycState,
    kycExtraSettings,
    availableAssignees,
    campaignOptions,
    finderVehicleConditionOptions,
    salespersonFromCap,
    salespersonAlert,
    dispatch,
    dealerId,
    availableLocalVariantsForModule,
    launchpadModule,
}: InnerProps) => {
    const { t } = useTranslation('launchpadLeadDetails');
    const {
        FormFields: { SelectField, DatePickerField },
        InfoMessageBox,
    } = useThemeComponents();
    const { values, setFieldValue, handleSubmit } = useFormikContext<QualifyContactValues>();

    const [modelOptions, variantOptions] = useModelVariantOptions({
        dealerId,
        modelId: values.vehicleModel,
        availableLocalVariantsForModule,
        dealerVehicles: launchpadModule.dealerVehicles,
    });

    const onVehicleModelChange = useCallback(() => setFieldValue('vehicleId', null, false), [setFieldValue]);

    return (
        <Form data-cy="qualifyForm" id="qualifyForm" name="qualifyForm" onSubmitCapture={handleSubmit} noValidate>
            <FormAutoTouch />
            <Row gutter={[0, 24]} style={{ width: '100%' }}>
                <Col span={24}>
                    <Row gutter={[16, 16]}>
                        <Col key="vehicleModel" {...defaultColSpan}>
                            <SelectField
                                key="vehicleModel"
                                filterOption={defaultFilterOption}
                                label={t('launchpadLeadDetails:qualifyModal.fields.vehicleModel')}
                                name="vehicleModel"
                                onChange={onVehicleModelChange}
                                options={modelOptions}
                                required
                                showSearch
                            />
                        </Col>
                        <Col key="vehicleId" {...defaultColSpan}>
                            <SelectField
                                key="vehicleId"
                                disabled={!values.vehicleModel}
                                filterOption={defaultFilterOption}
                                label={t('launchpadLeadDetails:qualifyModal.fields.variant')}
                                name="vehicleId"
                                options={variantOptions}
                                required
                                showSearch
                            />
                        </Col>
                        <Col key="assigneeId" {...defaultColSpan}>
                            <SelectField
                                key="assigneeId"
                                disabled={!!salespersonFromCap}
                                filterOption={defaultFilterOption}
                                label={t('launchpadLeadDetails:qualifyModal.fields.salesConsultant')}
                                name="assigneeId"
                                options={availableAssignees}
                                showSearch
                            />
                        </Col>
                        <Col key="vehicleCondition" {...defaultColSpan}>
                            <SelectField
                                key="vehicleCondition"
                                filterOption={defaultFilterOption}
                                label={t('launchpadLeadDetails:qualifyModal.fields.vehicleCondition')}
                                name="vehicleCondition"
                                options={finderVehicleConditionOptions}
                                required
                                showSearch
                            />
                        </Col>
                        <Col key="campaignId" {...defaultColSpan}>
                            <SelectField
                                key="campaignId"
                                autoSelected={false}
                                filterOption={defaultFilterOption}
                                label={t('launchpadLeadDetails:qualifyModal.fields.campaignId')}
                                name="campaignId"
                                options={campaignOptions}
                                required
                                showSearch
                            />
                        </Col>
                        <Col key="purchaseIntention" {...defaultColSpan}>
                            <DatePickerField
                                key="purchaseIntention"
                                format="MMM YYYY"
                                label={t('launchpadLeadDetails:qualifyModal.fields.purchaseIntention')}
                                name="purchaseIntention"
                                picker="month"
                                required
                            />
                        </Col>
                    </Row>
                    {salespersonAlert && (
                        <InfoMessageBoxContainer>
                            <Row gutter={24}>
                                <Col span={24}>
                                    <InfoMessageBox
                                        description={salespersonAlert.description}
                                        message={salespersonAlert.title}
                                    />
                                </Col>
                            </Row>
                        </InfoMessageBoxContainer>
                    )}
                </Col>

                {kycState.length > 0 && (
                    <Col span={24}>
                        <CustomerDetails
                            customerKind={CustomerKind.Local}
                            fieldsColSpan={defaultColSpan}
                            gutter={[16, 16]}
                            hasGuarantorPreset={false}
                            kycExtraSettings={kycExtraSettings}
                            kycPresets={kycState}
                            removeDocument={null}
                            resetFormHandler={() => {}}
                            setIsCorporate={() => {}}
                            setPrefill={() => {}}
                            showTabs={false}
                            typeKYCPageTitle={FeatureKYCPageLabel.Default}
                            uploadDocument={null}
                        />
                    </Col>
                )}
            </Row>
        </Form>
    );
};

const QualifyModalInner = ({ formName, state, dispatch, onClose, formRef }: QualifyContactFormProps) => {
    const { t } = useTranslation(['launchpadLeadDetails', 'capApplication']);
    const { lead, dealerId, submissionPurpose, selectedBusinessPartner, selectedLead } = state;

    const { submitKyc, qualifyLead } = useSubmitKyc({ state, dispatch, onClose });

    const launchpadModule = lead.leadModule;

    const { finderVehicleConditionOptions } = useSystemOptions();

    const applicationModuleId = lead?.moduleId;

    // KYC Fields Query
    const { data: kycFieldsData, loading: kycFieldsLoading } = usePrefetchKycFieldsForQualifyQuery({
        variables: { applicationModuleId },
        skip: !applicationModuleId,
    });

    // Variants Query
    const { data: availableLocalVariantsForModule, loading: variantsLoading } = useGetLocalVariantsOptionsQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            filter: {
                moduleId: launchpadModule.vehicleModuleId,
                dealerIds: [dealerId],
                applicationModuleId: launchpadModule.id,
                purpose: Purpose.Production,
            },
        },
        skip: !launchpadModule,
    });

    const kycState = useMemo(
        () => (kycFieldsData && !kycFieldsLoading ? getApplicantKyc(kycFieldsData.applicantKyc ?? []) : []),
        [kycFieldsData, kycFieldsLoading]
    );

    const kycExtraSettings = useMemo(
        () =>
            launchpadModule?.customerModule.__typename === 'LocalCustomerManagementModule'
                ? launchpadModule?.customerModule.extraSettings
                : null,
        [launchpadModule]
    );

    const availableAssignees = useMemo(
        () => (lead?.availableAssignees || []).map(assignee => ({ label: assignee.displayName, value: assignee.id })),
        [lead?.availableAssignees]
    );

    const salespersonFromCap = useMemo(() => {
        const salesPersonId = selectedBusinessPartner?.responsibleSalesPersonId || lead?.capValues?.salesPersonId;

        if (!lead?.availableAssignees.length || !salesPersonId) {
            return null;
        }

        return lead.availableAssignees.find(assignee => assignee.alias === salesPersonId)?.id;
    }, [selectedBusinessPartner?.responsibleSalesPersonId, lead.availableAssignees, lead?.capValues?.salesPersonId]);

    const { hasExistingSalespersonFromCap, currentAssignee, originSalesConsultant } = useMemo(
        () => ({
            hasExistingSalespersonFromCap: salespersonFromCap,
            currentAssignee: lead.assignee?.displayName,
            originSalesConsultant: lead?.originSalesConsultant?.displayName,
        }),
        [lead.assignee?.displayName, lead?.originSalesConsultant?.displayName, salespersonFromCap]
    );

    const salespersonAlertDescription = useMemo(() => {
        if (hasExistingSalespersonFromCap) {
            return null;
        }

        const description = [
            t('capApplication:availableBPModal.information.description.currentAssignee', {
                currentAssignee,
            }),
            originSalesConsultant
                ? t('capApplication:availableBPModal.information.description.originSalesConsultant', {
                      originSalesConsultant,
                  })
                : null,
        ].filter(Boolean);

        return renderAlertDescription(description.join('<br>'));
    }, [currentAssignee, hasExistingSalespersonFromCap, originSalesConsultant, t]);

    const salespersonAlert = useMemo(() => {
        const title = hasExistingSalespersonFromCap
            ? // On new design, not display salespersonId in the title
              t('launchpadLeadDetails:qualifyModal.alert.hasExistingSalespersonFromCap', { salespersonId: '' })
            : t('capApplication:availableBPModal.information.title.selectResponsibleSalesForNewBp');

        return {
            title,
            description: salespersonAlertDescription,
        };
    }, [hasExistingSalespersonFromCap, salespersonAlertDescription, t]);

    const campaignOptions = useCampaignOptions({ lead, launchpadModule });

    const customerValidations = useKYCFormValidator({
        field: kycState,
        extraSettings: kycExtraSettings,
        moduleCountryCode: lead.module.company.countryCode,
        prefix: 'customer.fields',
    });
    const validateQualifyContact = useValidator(
        validators.compose(
            validators.requiredObjectId('vehicleModel'),
            validators.requiredObjectId('vehicleId'),
            validators.requiredString('assigneeId'),
            validators.requiredString('vehicleCondition'),
            validators.requiredString('campaignId'),
            validators.requiredDate('purchaseIntention'),
            customerValidations
        )
    );

    const { campaignId, purchaseIntention, vehicleCondition } = useMemo(() => {
        let vehicleCondition = null;
        if (selectedLead?.vehicleCondition) {
            if (selectedLead?.vehicleCondition === FinderVehicleCondition.New.toUpperCase()) {
                vehicleCondition = FinderVehicleCondition.New;
            } else if (
                selectedLead?.vehicleCondition === FinderVehicleCondition.Preowned.toUpperCase() ||
                selectedLead?.vehicleCondition === 'CPO' // Used Car (Finder preowned) => CPO
            ) {
                vehicleCondition = FinderVehicleCondition.Preowned;
            }
        }

        return {
            campaignId: selectedLead?.campaignId,
            purchaseIntention: selectedLead?.purchaseIntention,
            vehicleCondition,
        };
    }, [selectedLead?.campaignId, selectedLead?.purchaseIntention, selectedLead?.vehicleCondition]);

    const initialValues: QualifyContactValues = useMemo(() => {
        const intention = purchaseIntention || lead.purchaseIntention;

        return {
            vehicleModel: lead.vehicle?.__typename === 'LocalVariant' ? lead.vehicle.model.id : null,
            vehicleId: lead.vehicle?.id || null,
            assigneeId: salespersonFromCap || lead.assignee?.id,
            vehicleCondition: vehicleCondition || lead.vehicleCondition,
            campaignId: campaignId || lead.campaignValues?.capCampaignId || campaignOptions[0]?.value,
            purchaseIntention: intention ? new Date(intention) : undefined,
            customer: { fields: getInitialValues(lead.customer.fields, kycState) },
            tradeInVehicle: lead.tradeInVehicle,
        };
    }, [purchaseIntention, lead, salespersonFromCap, vehicleCondition, campaignId, kycState, campaignOptions]);

    const onSubmit = submissionPurpose === SubmissionPurpose.Qualify ? qualifyLead : submitKyc;

    if (kycFieldsLoading || variantsLoading) {
        return (
            <Col span={24}>
                <center>
                    <LoadingElement />
                </center>
            </Col>
        );
    }

    return (
        <div className="launchpad-modal">
            <Formik
                key={formName}
                initialValues={initialValues}
                innerRef={formRef}
                onSubmit={onSubmit}
                validate={validateQualifyContact}
            >
                <Inner
                    availableAssignees={availableAssignees}
                    availableLocalVariantsForModule={availableLocalVariantsForModule}
                    campaignOptions={campaignOptions}
                    dealerId={dealerId}
                    dispatch={dispatch}
                    finderVehicleConditionOptions={finderVehicleConditionOptions}
                    formName={formName}
                    kycExtraSettings={kycExtraSettings}
                    kycState={kycState}
                    launchpadModule={launchpadModule}
                    salespersonAlert={salespersonAlert}
                    salespersonFromCap={salespersonFromCap}
                />
            </Formik>
        </div>
    );
};

export default QualifyModalInner;
