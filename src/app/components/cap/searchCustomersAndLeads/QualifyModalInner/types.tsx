import type { FinderVehicleCondition, TradeInVehiclePayload } from '../../../../api/types';
import type { KYCPresetFormFields } from '../../../../utilities/kycPresets';

export type QualifyContactValues = {
    vehicleModel: string;
    vehicleId: string;
    assigneeId: string;
    vehicleCondition: FinderVehicleCondition;
    campaignId: string;
    purchaseIntention: Date;
    customer: { fields: KYCPresetFormFields };
    tradeInVehicle: TradeInVehiclePayload[];
};
