import { useMemo } from 'react';
import type { LeadDataFragment } from '../../../api/fragments/LeadData';

type CampaignOption = {
    label: string;
    value: string;
};

type UseCampaignOptionsParams = {
    lead: LeadDataFragment;
    launchpadModule: LeadDataFragment['leadModule'];
};

/**
 * Custom hook to generate campaign options based on lead module type
 * @returns Array of campaign options with label and value
 */
export const useCampaignOptions = ({ lead, launchpadModule }: UseCampaignOptionsParams): CampaignOption[] =>
    useMemo(() => {
        switch (lead.module.__typename) {
            case 'LaunchPadModule':
                return launchpadModule.capCampaignIds.map(campaignId => ({ label: campaignId, value: campaignId }));

            case 'StandardApplicationModule':
            case 'FinderApplicationPublicModule':
            case 'FinderApplicationPrivateModule':
            case 'ConfiguratorModule':
                return lead.module.leadCampaignId
                    ? lead.module.leadCampaignId
                          .split(',')
                          .map(campaignId => ({ label: campaignId, value: campaignId }))
                    : [];

            case 'EventApplicationModule':
                if (lead.__typename !== 'EventLead') {
                    return launchpadModule.capCampaignIds.map(campaignId => ({ label: campaignId, value: campaignId }));
                }

                return [
                    {
                        label: lead.event?.utmParametersSettings?.defaultValue?.capCampaignId || '',
                        value: lead.event?.utmParametersSettings?.defaultValue?.capCampaignId || '',
                    },
                    ...(lead.event?.utmParametersSettings?.overrides || []).map(campaign => ({
                        label: campaign.capCampaignId || '',
                        value: campaign.capCampaignId || '',
                    })),
                ].filter(campaign => campaign.label && campaign.value);

            default:
                throw new Error(`Unsupported module type: ${lead.module.__typename}`);
        }
    }, [launchpadModule.capCampaignIds, lead]);

export default useCampaignOptions;
