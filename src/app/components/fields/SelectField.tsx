import { Select as AntdSelect, Checkbox, type SelectProps } from 'antd';
import type { CheckboxChangeEvent } from 'antd/lib/checkbox';
import { useField } from 'formik';
import { isEmpty, isEqual, isNil, isNumber, isUndefined } from 'lodash/fp';
import { type ReactNode, memo, useCallback, useEffect, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import useCompanyColorLuminance from '../../utilities/useCompanyColorLuminance';
import FormItem, { type FormItemProps } from './FormItem';
import Down from '../../icons/down.svg';

const CheckboxContainer = styled.div`
    padding: 5px 12px;

    > * {
        display: flex;
        width: 100%;
    }
`;

export const StyledSelect = styled(AntdSelect)`
    .comparison-grid-item & {
        &.ant-select:not(.ant-select-customize-input) {
            > .ant-select-selector {
                align-items: normal;
            }
            > .ant-select-arrow {
                top: 50%;
            }
        }
    }

    .calculator-form-item-label & {
        &.ant-select:not(.ant-select-customize-input) {
            & .ant-select-selection-item {
                line-height: normal;
            }
        }
    }

    &.ant-select {
        &:not(.ant-select-customize-input) {
            &.ant-select-disabled {
                & > .ant-select-arrow {
                    display: none;
                }

                & > .ant-select-selector {
                    background-color: transparent;
                    color: #888;
                }
            }

            & > .ant-select-selector {
                background-color: transparent;
                border-radius: 0;
                height: auto;
                min-height: auto;
                padding-top: 1px;
                padding-bottom: 2px;
                align-items: end;
            }

            & > .ant-select-arrow {
                top: 60%;
            }

            &.ant-select-focused:not(.ant-select-disabled) {
                & > .ant-select-selector {
                    box-shadow: none;
                }
            }

            &.ant-select-status-error.ant-select-focused:not(.ant-select-disabled) > .ant-select-selector {
                box-shadow: none;
            }

            & .ant-select-selection-item {
                font-size: var(--input-font-size, 16px);
            }

            & .ant-select-selection-placeholder {
                font-size: var(--input-font-size, 16px);
            }

            &.ant-select-single.ant-select-open .ant-select-selection-item {
                transition: none;
            }
        }
    }
`;

export const SelectDropdownContainer = styled.div<{ luminance: number }>`
    & .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
        color: ${({ luminance }) => (luminance && luminance > 0.5 ? '#000' : '#fff')};
        background-color: var(--ant-primary-color);

        & .ant-icon {
            color: ${({ luminance }) => (luminance && luminance > 0.5 ? '#000' : '#fff')};
        }

        & .ant-select-item-option-state {
            color: ${({ luminance }) => (luminance && luminance > 0.5 ? '#000' : '#fff')};
        }
    }

    & .ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled),
    .ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled):hover {
        color: ${({ luminance }) => (luminance && luminance > 0.5 ? '#000' : '#fff')};
        background-color: var(--ant-primary-color);
    }
`;

export const Select = ({
    dropdownRender: dropdownRenderProp,
    suffixIcon = <Down fill="var(--ant-primary-color)" />,
    applyDefaultStyles = true,
    ...props
}: SelectProps & { applyDefaultStyles?: boolean }) => {
    const luminance = useCompanyColorLuminance();
    const dropdownRender = useCallback(
        (menu: React.ReactElement) => (
            <SelectDropdownContainer luminance={luminance}>
                {dropdownRenderProp ? dropdownRenderProp(menu) : menu}
            </SelectDropdownContainer>
        ),
        [dropdownRenderProp, luminance]
    );

    const EnhancedInput = useMemo(() => (applyDefaultStyles ? StyledSelect : AntdSelect), [applyDefaultStyles]);

    return <EnhancedInput dropdownRender={dropdownRender} suffixIcon={suffixIcon} {...props} />;
};

export type SelectOptionSingle<T> = {
    label: JSX.Element | ReactNode;
    value: T;
};

export type SelectOptionGroup<T> = {
    label: JSX.Element | ReactNode;
    options: SelectOptionSingle<T>[];
};

export type SelectOption<T> = SelectOptionSingle<T> | SelectOptionGroup<T>;

export const isSelectOptionGroup = <T,>(option: SelectOption<T>): option is SelectOptionGroup<T> =>
    (option as SelectOptionGroup<T>).options !== undefined;

export const isSelectOptionSingle = <T,>(option: SelectOption<T>): option is SelectOptionSingle<T> =>
    (option as SelectOptionSingle<T>).value !== undefined;

/**
 * Check if the options array has only one option and return its value.
 * If the options array is empty or has more than one option, return null.
 */
export const getSingleOptionValue = <T,>(options: SelectOption<T>[]): T | null => {
    if (!options || options.length !== 1) {
        return null;
    }

    if (isSelectOptionSingle(options[0])) {
        return options[0].value;
    }

    if (isSelectOptionGroup(options[0]) && options[0].options.length === 1) {
        return options[0].options[0].value;
    }

    return null;
};

const flattenOptions = <T,>(options: SelectOption<T>[]): SelectOptionSingle<T>[] =>
    options?.reduce<SelectOptionSingle<T>[]>((acc, option) => {
        if (isSelectOptionGroup(option)) {
            return [...acc, ...option.options];
        }

        return [...acc, option];
    }, []);

export interface SelectFieldProps extends Omit<SelectProps, 'value' | 'onChange' | 'options'> {
    name: string;
    label?: string;
    itemProps?: Omit<FormItemProps, 'label' | 'meta' | 'required' | 'children'>;
    tooltip?: FormItemProps['tooltip'];
    required?: boolean;
    options: SelectOption<string | number | boolean>[];
    onChange?: (value: any) => void;
    enhanceOptions?: (options: SelectFieldProps['options']) => SelectFieldProps['options'];
    // if only have one option. then auto select that
    autoSelected?: boolean;
    applyDefaultStyles?: boolean;
    addSelectAll?: boolean;
    showTooltipWhenDisabled?: boolean;
    hideValueWhenDisabled?: boolean;
}

export const defaultFilterOption = (input: string, option: { label: string }) =>
    (option?.label ?? '').toLowerCase().includes(input.toLowerCase());

const SelectField = ({
    name,
    required,
    label,
    itemProps,
    tooltip,
    suffixIcon = <Down fill="var(--ant-primary-color)" />,
    disabled,
    onChange = null,
    enhanceOptions,
    autoSelected = true,
    applyDefaultStyles = true,
    addSelectAll = false,
    ...props
}: SelectFieldProps) => {
    const { t } = useTranslation('common');
    const [field, meta, { setValue }] = useField({ name });
    const enhancedOnChange = useCallback(
        value => {
            setValue(value);

            if (onChange) {
                onChange(value);
            }
        },
        [setValue, onChange]
    );
    const { options } = props;

    // track latest options
    const prevOptions = useRef(null);

    useEffect(() => {
        const onlyValue = getSingleOptionValue(options);

        if (
            onlyValue &&
            (!isEqual(options, prevOptions?.current) || isUndefined(field.value)) &&
            !props.allowClear &&
            ((!isNil(props.loading) && !props.loading) || isNil(props.loading))
        ) {
            const newValue = props.mode === 'multiple' ? [onlyValue] : onlyValue;

            // Should use the `setValue` function to update the field value
            // instead of enhanced one, because the enhanced one will trigger
            // the `onChange` callback, which is not desired here.
            // ie. onAssignedChange in Application details should not be triggered
            // on page load
            if (autoSelected) {
                enhancedOnChange(newValue);
            }

            prevOptions.current = options;
        }

        // reset previous options such that the previous condition can be fulfilled
        if (!isEqual(options, prevOptions?.current)) {
            prevOptions.current = null;
        }
    }, [props.mode, props.loading, options, required, enhancedOnChange, props.allowClear, field.value, autoSelected]);

    // account for both empty array and null values
    const hasValue = !isEmpty(field.value) || isNumber(field.value);

    const handleSelectAll = useCallback(
        (e: CheckboxChangeEvent) => {
            if (e.target.checked) {
                const allValues = flattenOptions(options).map(option => option.value);
                enhancedOnChange(allValues.flat());
            } else {
                enhancedOnChange([]);
            }
        },
        [options, enhancedOnChange]
    );

    const dropdownRender = useCallback<SelectProps['dropdownRender']>(
        menu => {
            const flattenedOptions = flattenOptions(options);

            return props.mode === 'multiple' && addSelectAll ? (
                <div>
                    <CheckboxContainer>
                        <Checkbox
                            checked={field.value.length === flattenedOptions.length}
                            indeterminate={field.value.length > 0 && field.value.length < flattenedOptions.length}
                            onChange={handleSelectAll}
                        >
                            {t('common:selectAll')}
                        </Checkbox>
                    </CheckboxContainer>
                    {menu}
                </div>
            ) : (
                menu
            );
        },
        [options, props.mode, addSelectAll, field?.value?.length, handleSelectAll, t]
    );

    return (
        <FormItem {...itemProps} label={label} meta={meta} required={required} tooltip={tooltip}>
            <Select
                // spread props
                {...props}
                // then spread the field properties itself
                {...field}
                applyDefaultStyles={applyDefaultStyles}
                disabled={disabled || (autoSelected && hasValue && options?.length === 1 && required)}
                dropdownRender={dropdownRender}
                onChange={enhancedOnChange}
                {...(!isEmpty(options) && { optionFilterProp: 'label' })}
                options={(enhanceOptions ? enhanceOptions(options) : options) as SelectProps['options']}
                placeholder={t('common:pleaseSelect')}
                suffixIcon={suffixIcon}
                showArrow
            />
        </FormItem>
    );
};

export default memo(SelectField);
