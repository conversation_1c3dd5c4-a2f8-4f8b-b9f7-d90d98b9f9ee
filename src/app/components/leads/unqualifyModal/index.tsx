import { ApolloError, useApolloClient } from '@apollo/client';
import { Typography } from 'antd';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { LaunchPadModuleSpecsFragment } from '../../../api/fragments/LaunchPadModuleSpecs';
import { LeadDataFragment } from '../../../api/fragments/LeadData';
import {
    UnqualifyLeadDocument,
    UnqualifyLeadMutationVariables,
    UnqualifyLeadMutation,
} from '../../../api/mutations/unqualifyLead';
import { useThemeComponents } from '../../../themes/hooks';

const Container = styled.div`
    margin-bottom: 32px;
`;

type UseUnqualifyContactModalProps = {
    launchpadModule: LaunchPadModuleSpecsFragment;
    lead: LeadDataFragment;
    dealerId: string;
    refetchLead?: () => void;
};

type UnqualifyContactModalProps = UseUnqualifyContactModalProps & {
    visible: boolean;
    onClose?: (isSave: boolean) => void;
};

const UnqualifyModal = ({ lead, onClose, visible, ...props }: UnqualifyContactModalProps) => {
    const [submissionLoading, setSubmissionLoading] = useState(false);

    const { t } = useTranslation(['launchpadLeadDetails']);
    const { Button, Modal, notification } = useThemeComponents();
    const apolloClient = useApolloClient();

    const onCancel = useCallback(() => {
        onClose(false);
    }, [onClose]);

    const unqualifyAction = useCallback(async () => {
        try {
            setSubmissionLoading(true);
            notification.loading({
                content: t('launchpadLeadDetails:unqualifyModal.unqualifyingContact'),
                duration: 0,
                key: 'primary',
            });

            const { errors } = await apolloClient.mutate<UnqualifyLeadMutation, UnqualifyLeadMutationVariables>({
                mutation: UnqualifyLeadDocument,
                variables: {
                    leadId: lead.id,
                },
            });

            if (!errors) {
                notification.success({
                    content: t('launchpadLeadDetails:unqualifyModal.submissionCompleteMessage'),
                    key: 'primary',
                });

                onClose(true);
            }
        } catch (error) {
            if (error instanceof ApolloError) {
                notification.error(error.graphQLErrors[0].message);
            } else {
                console.error(error);
            }
        } finally {
            notification.destroy('primary');
            setSubmissionLoading(false);
        }
    }, [apolloClient, lead?.id, notification, onClose, t]);

    const footerButton = useMemo(
        () => [
            <Button
                key="submit-unqualify"
                onClick={e => {
                    e.preventDefault();
                    unqualifyAction();
                }}
                type="primary"
                block
            >
                {t('launchpadLeadDetails:unqualifyModal.buttons.submit')}
            </Button>,
            <Button key="cancel" onClick={onCancel} type="tertiary" block>
                {t('launchpadLeadDetails:unqualifyModal.buttons.cancel')}
            </Button>,
        ],
        [Button, t, onCancel, unqualifyAction]
    );

    return (
        <Modal
            className="launchpad-modal"
            closable={false}
            data-cy="qualify-modal"
            footer={footerButton}
            maskClosable={!submissionLoading}
            onCancel={onCancel}
            open={visible}
            title={t('launchpadLeadDetails:unqualifyModal.title')}
            centered
            destroyOnClose
        >
            <Container>
                <Typography.Text>{t('launchpadLeadDetails:unqualifyModal.confirmationMessage')}</Typography.Text>
            </Container>
        </Modal>
    );
};

export default UnqualifyModal;

export const useUnqualifyModal = ({ refetchLead, ...props }: UseUnqualifyContactModalProps) => {
    const [visible, setVisible] = useState(false);

    const actions = useMemo(
        () => ({
            open: () => setVisible(true),
            close: (isSaved: boolean) => {
                setVisible(false);
                if (isSaved) {
                    refetchLead?.();
                }
            },
        }),
        [refetchLead]
    );

    return {
        ...actions,
        render: () => <UnqualifyModal onClose={actions.close} visible={visible} {...props} />,
    };
};
