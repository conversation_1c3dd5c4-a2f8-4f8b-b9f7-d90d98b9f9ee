import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useConfirmBookingApplicationMutation } from '../../../api/mutations/confirmBookingApplication';
import { ApplicationStage, type ApplicationUpdateAppointmentDetails, type CapValuesInput } from '../../../api/types';
import { useThemeComponents } from '../../../themes/hooks';
import getApolloErrors from '../../../utilities/getApolloErrors';

type ConfirmBookingModalInnerProps = {
    isOpen: boolean;
    onClose: () => void;
    submitForm: () => Promise<void>;
    isSubmitting: boolean;
};

const ConfirmBookingInnerModal = ({ submitForm, isSubmitting, isOpen, onClose }: ConfirmBookingModalInnerProps) => {
    const { t } = useTranslation(['applicationDetails']);

    const { Modal } = useThemeComponents();

    const handleClose = useCallback(() => {
        onClose();
    }, [onClose]);

    return (
        <Modal
            cancelText={t('applicationDetails:buttons.cancel')}
            closable={false}
            maskClosable={false}
            okButtonProps={{
                loading: isSubmitting,
            }}
            okText={t('applicationDetails:buttons.ok')}
            onCancel={handleClose}
            onOk={submitForm}
            open={isOpen}
            title={t(`applicationDetails:confirmModal.title.confirmBooking`)}
            width={480}
        />
    );
};

type ConfirmBookingModalSubmissionProps = {
    applicationId: string;
    stage: ApplicationStage.Appointment | ApplicationStage.VisitAppointment;
    capValues: CapValuesInput;
    appointmentDetails: ApplicationUpdateAppointmentDetails;
};

type ConfirmBookingModalProps = {
    confirmBookingSubmissionValues: ConfirmBookingModalSubmissionProps;
    isOpen: boolean;
    onClose: (isSaved: boolean) => void;
};

const ConfirmBookingModal = ({ confirmBookingSubmissionValues, isOpen, onClose }: ConfirmBookingModalProps) => {
    const { t } = useTranslation(['applicationDetails']);
    const [isSubmitting, setIsSubmitting] = useState(false);

    const [confirmBookingMutation] = useConfirmBookingApplicationMutation();

    const { notification } = useThemeComponents();

    const onSubmit = useCallback(async () => {
        notification.loading({
            content: t('applicationDetails:messages.confirmingBooking'),
            key: 'primary',
            duration: 0,
        });

        setIsSubmitting(true);

        try {
            await confirmBookingMutation({
                variables: confirmBookingSubmissionValues,
            });

            notification.success({
                content: t('applicationDetails:messages.confirmedBooking'),
                key: 'primary',
            });
            setIsSubmitting(false);
            onClose(true);
        } catch (error) {
            const apolloErrors = getApolloErrors(error);

            if (apolloErrors?.$root) {
                notification.error(apolloErrors?.$root);
            }

            setIsSubmitting(false);
        }
    }, [notification, t, confirmBookingMutation, confirmBookingSubmissionValues, onClose]);

    return (
        <ConfirmBookingInnerModal
            isOpen={isOpen}
            isSubmitting={isSubmitting}
            onClose={() => onClose(false)}
            submitForm={onSubmit}
        />
    );
};

const useConfirmBookingModal = ({ refetchApplication }: { refetchApplication: () => void }) => {
    const [isOpen, setIsOpen] = useState(false);
    const [confirmBookingSubmissionValues, setConfirmBookingSubmissionValues] =
        useState<ConfirmBookingModalSubmissionProps>();

    const actions = useMemo(
        () => ({
            open: (confirmBookingValues: ConfirmBookingModalSubmissionProps) => {
                setIsOpen(true);
                setConfirmBookingSubmissionValues(confirmBookingValues);
            },
            close: (isSaved: boolean) => {
                setIsOpen(false);
                if (isSaved) {
                    refetchApplication?.();
                }
            },
        }),
        [refetchApplication]
    );

    return useMemo(
        () =>
            [
                (confirmBookingValues: ConfirmBookingModalSubmissionProps) => actions.open(confirmBookingValues),
                () => (
                    <ConfirmBookingModal
                        confirmBookingSubmissionValues={confirmBookingSubmissionValues}
                        isOpen={isOpen}
                        onClose={actions.close}
                    />
                ),
            ] as const,
        [actions, confirmBookingSubmissionValues, isOpen]
    );
};

export default useConfirmBookingModal;
