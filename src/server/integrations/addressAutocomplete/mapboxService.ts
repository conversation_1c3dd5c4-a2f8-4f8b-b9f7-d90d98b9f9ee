import config from '../../core/config';
import {
    type AddressAutocompleteService,
    type AddressAutocompleteInput,
    type AddressAutocompleteResult,
    type AddressComponent,
    AddressComponentType,
} from './types';

type MapboxGeocodeFeature = {
    id: string;
    type: string;
    place_type: string[];
    properties: {
        short_code?: string;
        wikidata?: string;
        category?: string;
        landmark?: boolean;
        address?: string;
        maki?: string;
    };
    text: string;
    place_name: string;
    center: [number, number];
    geometry: {
        type: string;
        coordinates: [number, number];
    };
    context?: Array<{
        id: string;
        short_code?: string;
        wikidata?: string;
        text: string;
    }>;
};

type MapboxGeocodeResponse = {
    type: string;
    query: string[];
    features: MapboxGeocodeFeature[];
    attribution: string;
};

class MapboxAddressAutocompleteService implements AddressAutocompleteService {
    async searchAddresses(input: AddressAutocompleteInput): Promise<AddressAutocompleteResult[]> {
        try {
            const { query, limit = 5, types, country, proximity, bbox } = input;

            // Build query parameters for Mapbox Forward Geocoding API
            const params = new URLSearchParams({
                access_token: config.mapboxApi.accessToken,
                autocomplete: 'true',
                limit: limit.toString(),
                format: 'v5',
            });

            if (types && types.length > 0) {
                // Map our generic types to Mapbox place types
                const mapboxTypes = this.mapTypesToMapbox(types);
                if (mapboxTypes.length > 0) {
                    params.append('types', mapboxTypes.join(','));
                }
            }

            if (country && country.length > 0) {
                params.append('country', country.join(','));
            }

            if (proximity) {
                params.append('proximity', `${proximity.longitude},${proximity.latitude}`);
            }

            if (bbox) {
                params.append(
                    'bbox',
                    `${bbox.minLongitude},${bbox.minLatitude},${bbox.maxLongitude},${bbox.maxLatitude}`
                );
            }

            if (input.language) {
                params.append('language', input.language);
            } else if (country && country.includes('JP')) {
                // include Japanese language for Japan for better accuracy
                params.append('language', 'ja');
            }

            if (query) {
                params.append('q', query);
            }

            const url = `${config.mapboxApi.url}/search/geocode/v6/forward?${params.toString()}`;

            const response = await fetch(url);

            if (!response.ok) {
                const errorText = await response.text();
                console.error(`Mapbox API error: ${response.status} ${response.statusText}`, errorText);
                throw new Error(`Mapbox API error: ${response.status} ${response.statusText}`);
            }

            const data: MapboxGeocodeResponse = await response.json();

            return data.features.map(feature => this.mapFeatureToResult(feature));
        } catch (error) {
            console.error('Error in Mapbox address autocomplete:', error);
            if (error instanceof Error) {
                throw error;
            }
            throw new Error('Failed to fetch address suggestions');
        }
    }

    private mapFeatureToResult(feature: MapboxGeocodeFeature): AddressAutocompleteResult {
        const components = this.extractComponents(feature);

        return {
            id: feature.id,
            address: feature.place_name,
            coordinates: {
                latitude: feature.center[1],
                longitude: feature.center[0],
            },
            components,
        };
    }

    private extractComponents(feature: MapboxGeocodeFeature): AddressComponent[] {
        const components: AddressComponent[] = [];

        // Extract primary component from the feature itself
        if (feature.place_type && feature.place_type.length > 0) {
            const primaryType = this.mapboxTypeToComponentType(feature.place_type[0]);
            if (primaryType) {
                components.push({
                    longName: feature.text,
                    shortName: feature.properties?.short_code || feature.text,
                    type: primaryType,
                });
            }
        }

        // Extract components from context
        if (feature.context) {
            feature.context.forEach(contextItem => {
                const typeMatch = contextItem.id.match(/^([^.]+)/);
                if (typeMatch) {
                    const mapboxType = typeMatch[1];
                    const componentType = this.mapboxTypeToComponentType(mapboxType);
                    if (componentType) {
                        components.push({
                            longName: contextItem.text,
                            shortName: contextItem.short_code || contextItem.text,
                            type: componentType,
                        });
                    }
                }
            });
        }

        return components;
    }

    private mapboxTypeToComponentType(mapboxType: string): AddressComponentType | null {
        const typeMap: Record<string, AddressComponentType> = {
            address: AddressComponentType.ADDRESS,
            secondary_address: AddressComponentType.SECONDARY_ADDRESS,
            street: AddressComponentType.STREET,
            block: AddressComponentType.BLOCK,
            poi: AddressComponentType.PLACE,
            neighborhood: AddressComponentType.NEIGHBORHOOD,
            locality: AddressComponentType.LOCALITY,
            place: AddressComponentType.PLACE,
            district: AddressComponentType.DISTRICT,
            region: AddressComponentType.REGION,
            postcode: AddressComponentType.POSTCODE,
            country: AddressComponentType.COUNTRY,
        };

        return typeMap[mapboxType] || null;
    }

    private mapTypesToMapbox(types: AddressComponentType[]): string[] {
        const typeMap: Record<AddressComponentType, string> = {
            [AddressComponentType.ADDRESS]: 'address',
            [AddressComponentType.STREET]: 'street',
            [AddressComponentType.BLOCK]: 'block',
            [AddressComponentType.NEIGHBORHOOD]: 'neighborhood',
            [AddressComponentType.LOCALITY]: 'locality',
            [AddressComponentType.DISTRICT]: 'district',
            [AddressComponentType.REGION]: 'region',
            [AddressComponentType.POSTCODE]: 'postcode',
            [AddressComponentType.COUNTRY]: 'country',
            [AddressComponentType.PLACE]: 'place',
            [AddressComponentType.SECONDARY_ADDRESS]: 'secondary_address',
        };

        return types.map(type => typeMap[type]).filter(Boolean);
    }
}

export default MapboxAddressAutocompleteService;
