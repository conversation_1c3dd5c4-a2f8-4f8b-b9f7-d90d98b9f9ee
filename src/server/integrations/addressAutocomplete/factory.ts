import JapanPostalCodeAddressAutocompleteService from './japanPostalCodeService';
import MapboxAddressAutocompleteService from './mapboxService';
import { AddressAutocompleteService } from './types';

export enum AddressAutocompleteProvider {
    MAPBOX = 'mapbox',
    JAPAN_POSTAL_CODE = 'japanPostalCode',
    // GOOGLE = 'google', // Future implementation
}

export class AddressAutocompleteServiceFactory {
    private static services = new Map<AddressAutocompleteProvider, AddressAutocompleteService>();

    static getService(
        provider: AddressAutocompleteProvider = AddressAutocompleteProvider.MAPBOX
    ): AddressAutocompleteService {
        if (!this.services.has(provider)) {
            this.services.set(provider, this.createService(provider));
        }

        return this.services.get(provider)!;
    }

    private static createService(provider: AddressAutocompleteProvider): AddressAutocompleteService {
        switch (provider) {
            case AddressAutocompleteProvider.MAPBOX:
                return new MapboxAddressAutocompleteService();

            case AddressAutocompleteProvider.JAPAN_POSTAL_CODE:
                return new JapanPostalCodeAddressAutocompleteService();

            // case AddressAutocompleteProvider.GOOGLE:
            //   return new GoogleAddressAutocompleteService();

            default:
                throw new Error(`Unsupported address autocomplete provider: ${provider}`);
        }
    }

    static clearCache(): void {
        this.services.clear();
    }
}

// Export the default service for convenience
export const defaultAddressAutocompleteService = AddressAutocompleteServiceFactory.getService();
