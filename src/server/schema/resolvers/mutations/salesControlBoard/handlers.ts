import { isEmpty, isNil } from 'lodash/fp';
import { Collections } from '../../../../database/collections';
import { SalesControlBoard, SalesControlBoardDataType } from '../../../../database/documents';
import { ExcelOptions, StringMap } from '../../../../utils/excel/types';
import {
    BoardContext,
    getFeatureBasedOnDataType,
    getGroupByFeature,
    getModelHeader,
    prepareSalesControlBoardDocument,
    remapModel,
} from './shared';
import { ModelFlatten } from './typing';

export const formatErrorColumns = (columns: string[]): string => {
    if (columns.length === 0) {
        return '';
    }

    if (columns.length === 1) {
        return columns[0];
    }

    return `${columns.slice(0, -1).join(', ')} and ${columns[columns.length - 1]}`;
};

const validateRowData = (rowNumber: number, columns: string[], typeError: 'missing' | 'invalid') =>
    `Row ${rowNumber} has ${typeError} data in ${formatErrorColumns(columns)}`;

const validateVehicleError = (flattenModels: ModelFlatten, modelDescription: string): boolean => {
    const mappedModel = remapModel(modelDescription);

    if (typeof mappedModel !== 'string' || isEmpty(mappedModel.trim()) || isNil(mappedModel)) {
        return false;
    }

    const model = flattenModels.find(item => mappedModel.toLowerCase().includes(item.modelDescription.toLowerCase()));

    if (!model) {
        return false;
    }

    return true;
};
/**
 * validate each row of the data whether any missing values
 * @param data Feature data retrieved from the excel sheet
 * @param excelOptions headers of the excel sheet and start row
 * @returns all the errors of the missing values in the data
 */
export const validateMissingValueFromData = async (
    collections: Collections,
    data: StringMap[],
    { dataType, flattenModels }: BoardContext,
    excelOptions: ExcelOptions
) => {
    const headersKeys = Object.keys(excelOptions.headers);

    const errors: Array<{ error: string; row: number }> = [];
    data.forEach((item, index) => {
        const rowNumber = index + excelOptions.start;

        switch (dataType) {
            case SalesControlBoardDataType.Retails:
            case SalesControlBoardDataType.OrderIntakes: {
                const modelHeader = getModelHeader(dataType);
                const errorsKeys = headersKeys.filter(
                    key => isNil(item[key]) || isEmpty(item[key]) || item[key] === ''
                );

                const hasVehicleError = errorsKeys.filter(key => key === modelHeader);
                if (errorsKeys.length > 0) {
                    const vehicleError = validateVehicleError(flattenModels, item[modelHeader]);
                    if (hasVehicleError.length > 0 && !vehicleError) {
                        errors.push({
                            error: validateRowData(rowNumber, [`[${modelHeader}]`], 'invalid'),
                            row: rowNumber,
                        });
                    }
                    errors.push({
                        error: validateRowData(
                            rowNumber,
                            errorsKeys.filter(key => key !== modelHeader).map(error => `[${error}]`),
                            'missing'
                        ),
                        row: rowNumber,
                    });
                }

                break;
            }

            case SalesControlBoardDataType.Leads: {
                break;
            }

            default:
                throw new Error(`Unsupported SalesControlBoardDataType: ${dataType}`);
        }
    });

    const errorRows = errors.flatMap(error => error.row);

    return { errors, data: data.filter((item, index) => !errorRows.includes(index)) };
};

/**
 * ==============================
 * ::::::: CORE FEATURE :::::::::
 * ==============================
 */

/**
 * computation of thFe import data based on the data type
 * @param dataType Sales Control Board Data Type selected by user
 * @param data data in the excel retrieved from the excel sheet
 */
export const processImportDataBasedOnDataType = async (
    collections: Collections,
    data: StringMap[],
    boardContext: BoardContext,
    excelOptions: ExcelOptions
) => {
    const { dataType, dealerId, reportingDate } = boardContext;

    const { errors, data: validData } = await validateMissingValueFromData(
        collections,
        data,
        boardContext,
        excelOptions
    );
    let documents: SalesControlBoard[] | undefined;
    switch (dataType) {
        case SalesControlBoardDataType.Retails: {
            const monthData = getFeatureBasedOnDataType(dataType, validData, reportingDate, 'month');
            const ytdData = getFeatureBasedOnDataType(dataType, validData, reportingDate, 'year');
            const threeMonthsData = getFeatureBasedOnDataType(dataType, validData, reportingDate, '3months');

            const uniqueMonthData = getGroupByFeature(collections, monthData, boardContext, true);
            const uniqueYtdData = getGroupByFeature(collections, ytdData, boardContext);
            const uniqueThreeMonthsData = getGroupByFeature(collections, threeMonthsData, boardContext);

            documents = await prepareSalesControlBoardDocument(collections, validData, boardContext, {
                monthData: uniqueMonthData,
                ytdData: uniqueYtdData,
                threeMonthsData: uniqueThreeMonthsData,
            });

            break;
        }

        case SalesControlBoardDataType.Leads: {
            const monthData = getFeatureBasedOnDataType(dataType, validData, reportingDate, 'month');
            const ytdData = getFeatureBasedOnDataType(dataType, validData, reportingDate, 'year');

            const uniqueMonthData = getGroupByFeature(collections, monthData, boardContext, true);
            const uniqueYtdData = getGroupByFeature(collections, ytdData, boardContext);

            documents = await prepareSalesControlBoardDocument(collections, validData, boardContext, {
                monthData: uniqueMonthData,
                ytdData: uniqueYtdData,
            });

            break;
        }

        case SalesControlBoardDataType.OrderIntakes: {
            const monthData = getFeatureBasedOnDataType(dataType, validData, reportingDate, 'month');
            const ytdData = getFeatureBasedOnDataType(dataType, validData, reportingDate, 'year');

            const uniqueMonthData = getGroupByFeature(collections, monthData, boardContext, true);
            const uniqueYtdData = getGroupByFeature(collections, ytdData, boardContext);

            documents = await prepareSalesControlBoardDocument(collections, validData, boardContext, {
                monthData: uniqueMonthData,
                ytdData: uniqueYtdData,
            });

            break;
        }

        default:
            throw new Error(`Unsupported SalesControlBoardDataType: ${dataType}`);
    }

    if (documents?.length > 0) {
        // delete all Sales Control Boards of this Retails based on the source
        await collections.salesControlBoards.deleteMany({
            source: dataType,
            dealerId,
            importDate: reportingDate,
        });

        await collections.salesControlBoards.insertMany(documents);
    }

    return errors.flatMap(error => error.error);
};
