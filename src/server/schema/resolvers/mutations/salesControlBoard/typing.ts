import { type Dictionary } from 'lodash';
import type { ObjectId } from 'mongodb';
import type { LeadRatioTuple, SalesControlBoardWeeklyCount } from '../../../../database/documents';

export type FeatureDataActualCount = Dictionary<Dictionary<{ value: number; weeks?: SalesControlBoardWeeklyCount[] }>>;

export type ConsolidatedFeatureData = {
    actualCount: FeatureDataActualCount;
    inHouseFinanceInsurance?: Dictionary<Dictionary<{ appliedFinance: number; appliedInsurance: number }>>;
    leadRatios?: Dictionary<Dictionary<{ ulr: LeadRatioTuple; olr: LeadRatioTuple; lio: number }>>;
};

export type ModelFlatten = Array<{ modelId: ObjectId; modelDescription: string }>;
