// eslint-disable-next-line import/order
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { DealerPolicyAction } from '../../../../permissions';
import { ExcelOptions } from '../../../../utils/excel/types';
import { allowedExtensions } from '../../../../utils/extensions';
import validateFile from '../../../../utils/validateFile';
import { InvalidPermission } from '../../../errors';
import { requiresLoggedUser } from '../../../middlewares';
import { GraphQLMutationResolvers, ModuleType } from '../../definitions';
import { processImportDataBasedOnDataType } from './handlers';
import { fetchLocalModels, getHeadersSalesControlBoardDataType, retrieveFeatureExcelData } from './shared';

const mutate: GraphQLMutationResolvers['importSalesControlBoardData'] = async (
    root,
    { dataType, file, reportingPeriod, dealerId },
    { getPermissionController, getTranslations, loaders }
) => {
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();
    const { t } = await getTranslations(['errors']);

    const acceptedExtensions = allowedExtensions.excel;

    const dealer = await loaders.dealerById.load(dealerId);

    const module = await collections.modules.findOne({
        companyId: dealer.companyId,
        _type: ModuleType.SalesControlBoardModule,
    });

    // only sales manager can upload
    if (
        !module ||
        module._type !== ModuleType.SalesControlBoardModule ||
        !permissionController.dealers.mayOperateOn(dealer, DealerPolicyAction.ViewSalesControlBoardManager)
    ) {
        throw new InvalidPermission();
    }

    const { createReadStream, filename } = await file;
    const isFileValid = await validateFile(createReadStream(), filename, acceptedExtensions);
    if (!isFileValid) {
        throw new Error(t('errors:salesControlBoard.missingFile.label'));
    }

    const excelOptions: ExcelOptions = {
        headers: getHeadersSalesControlBoardDataType(dataType),
        start: 1,
    };

    const extension = filename.split('.').pop().toLowerCase();
    // retails
    const data = await retrieveFeatureExcelData(createReadStream(), excelOptions, extension);

    const salesConsultantIds =
        module.salesConsultantsAssignments?.overrides?.find(it => it.dealerId.equals(dealer._id))?.value ??
        module.salesConsultantsAssignments?.defaultValue ??
        [];
    const salesConsultants = await collections.users.find({ _id: { $in: salesConsultantIds } }).toArray();

    const flattenModels = await fetchLocalModels(collections, dealerId);

    const errors = await processImportDataBasedOnDataType(
        collections,
        data,
        { dealerId, dataType, reportingDate: reportingPeriod, flattenModels, salesConsultants },
        excelOptions
    );

    return errors;
};

export default requiresLoggedUser(mutate);
