import { escapeRegExp, isNil } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import {
    Condition,
    DealershipMyInfoSetting,
    DealershipPaymentSetting,
    DealershipPublicSalesPerson,
    DealershipSettingType,
    Event,
    ModuleType,
} from '../../../../database/documents';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { EventCounterType, getEventIdentifier } from '../../../../database/helpers/events';
import { hasPaymentScenario } from '../../../../journeys/common/helpers';
import { ModulePolicyAction } from '../../../../permissions';
import { getSimpleVersioningByUserForCreation } from '../../../../utils/versioning';
import { InvalidInput, InvalidPermission } from '../../../errors';
import { buildRateLimiterMiddleware, requiresLoggedUser } from '../../../middlewares';
import { GraphQLMutationResolvers } from '../../definitions';
import { checkAllConditionSettings } from '../consentsAndDeclarations/checkConditionSettings';
import generateConditions from '../consentsAndDeclarations/generateConditions';
import { urlSlugRegex, convertCustomTestDriveBookingSlotsInput } from './shared';
import { createOrUpdateBannerEvent, validateEventUTMParameter } from './updateEvent';

const mutate: GraphQLMutationResolvers['createEvent'] = async (
    root,
    { moduleId, eventInput, kycPresets: inputKycPresets },
    { getUser, getTranslations, getPermissionController }
) => {
    const { t } = await getTranslations(['errors']);
    const {
        publicSalesPerson,
        myInfoSetting,
        paymentSetting,
        eventLevelEmailSettings,
        banner,
        utmParametersSettings,
        customTestDriveBookingSlots,
        ...otherInput
    } = eventInput;
    const permissionController = await getPermissionController();

    const { collections } = await getDatabaseContext();
    const eventModule = await collections.modules.findOne({ _id: moduleId });

    if (!eventModule || eventModule._type !== ModuleType.EventApplicationModule) {
        throw new Error(t('errors:eventModuleNotFound'));
    }

    const user = await getUser();
    const simpleVersion = getSimpleVersioningByUserForCreation(user._id);

    if (!permissionController.modules.mayOperateOn(eventModule, ModulePolicyAction.CreateEvent)) {
        throw new InvalidPermission();
    }

    if (hasPaymentScenario(eventInput.scenarios) && !eventInput.paymentSetting?.defaultId) {
        throw new InvalidInput({ paymentSetting: t('errors:paymentModuleNotSelected') });
    }

    if (!eventInput.privateAccess && !eventInput.publicSalesPerson?.defaultId) {
        throw new InvalidInput({ publicSalesPerson: t('errors:noResponsibleSalesPerson') });
    }

    // in update, we need to exclude current configurator in query
    const existingUrlSlug = await collections.events.countDocuments({
        urlSlug: { $regex: new RegExp(`^${escapeRegExp(eventInput.urlSlug)}$`, 'i') },
        moduleId,
        isDeleted: false,
    });

    if (!urlSlugRegex.test(eventInput.urlSlug)) {
        throw new InvalidInput({ urlSlug: 'URL only allow small letter alphanumeric and hyphen' });
    }

    if (existingUrlSlug > 0) {
        // check whether any same url identifier existed.
        throw new InvalidInput({ urlSlug: 'There is same URL Slug in same module' });
    }

    const validationUTMParameter = await validateEventUTMParameter(
        utmParametersSettings.overrides,
        otherInput.enableDynamicUtmTracking
    );

    if (!validationUTMParameter.result) {
        throw new InvalidInput({ utmParametersSettings: validationUTMParameter.message });
    }

    const updatedCustomizedField = eventInput.customizedFields.map(field => {
        if (isNil(field.id)) {
            return {
                ...field,
                _id: new ObjectId(),
            };
        }

        return { ...field, _id: field.id };
    });

    const kycPresets = inputKycPresets.map(preset => {
        let actualConditions: Condition[] = [];
        if (preset.conditions.length > 0 && preset.conditions) {
            const isConditionSettingsValid = checkAllConditionSettings(preset.conditions);

            if (!isConditionSettingsValid) {
                throw new InvalidInput({ conditionSettings: 'Invalid Condition Settings' });
            } else {
                actualConditions = preset.conditions.map(setting => generateConditions(setting));
            }
        }

        return {
            _id: new ObjectId(),
            conditions: actualConditions,
            displayName: preset.settings.displayName,
            isActive: preset.settings.isActive,
            fields: preset.fields,
        };
    });

    const porscheIdModule = await collections.modules.findOne({
        companyId: eventModule.companyId,
        _type: ModuleType.PorscheIdModule,
    });

    const bannerId = await createOrUpdateBannerEvent(undefined, banner, moduleId, user._id);

    const identifier = await getEventIdentifier(eventModule, EventCounterType.EventCounter);
    const submitOrderEmailContent = eventLevelEmailSettings?.submitOrder;
    const customerTestDriveEmailContent = eventLevelEmailSettings?.testDrive?.customer;

    const document: Event = {
        ...otherInput,
        _id: new ObjectId(),
        moduleId,
        identifier,
        isDeleted: false,
        _versioning: simpleVersion,
        userIds: [],
        ...(customTestDriveBookingSlots && {
            customTestDriveBookingSlots: convertCustomTestDriveBookingSlotsInput(customTestDriveBookingSlots),
        }),
        ...(!eventInput.privateAccess &&
            publicSalesPerson && {
                publicSalesPerson: {
                    ...publicSalesPerson,
                    _type: DealershipSettingType.PublicSalesPerson,
                } as DealershipPublicSalesPerson,
            }),
        ...(myInfoSetting && {
            myInfoSetting: { ...myInfoSetting, _type: DealershipSettingType.MyInfoSetting } as DealershipMyInfoSetting,
        }),
        ...(hasPaymentScenario(eventInput.scenarios) &&
            paymentSetting && {
                paymentSetting: {
                    ...paymentSetting,
                    _type: DealershipSettingType.PaymentSetting,
                } as DealershipPaymentSetting,
            }),
        kycPresets,
        // Skips email update when custom email contents is not enabled
        ...(eventInput.hasCustomiseEmail && {
            emailContents: {
                submitOrder: submitOrderEmailContent,
                testDrive: {
                    customer: {
                        submitConfirmation: customerTestDriveEmailContent?.submitConfirmation,
                        bookingAmendment: customerTestDriveEmailContent?.bookingAmendment,
                        bookingCancellation: customerTestDriveEmailContent?.bookingCancellation,
                        bookingConfirmation: customerTestDriveEmailContent?.bookingConfirmation,
                        completeTestDriveWithoutProcess: customerTestDriveEmailContent?.completeTestDriveWithoutProcess,
                        endTestDriveWithProcess: customerTestDriveEmailContent?.endTestDriveWithProcess,
                    },
                },
            },
        }),
        hasCustomiseBanner: eventInput?.hasCustomiseBanner || false,
        ...(eventInput?.hasCustomiseBanner && { bannerId }),
        customizedFields: updatedCustomizedField,
        isSearchCapCustomerOptional: eventInput.isSearchCapCustomerOptional || false,
        capPrequalification: eventInput.capPrequalification || false,
        utmParametersSettings,
    };

    if (porscheIdModule) {
        document.porscheIdModuleId = porscheIdModule._id;
    }

    await collections.events.insertOne(document);

    return document;
};

export default buildRateLimiterMiddleware({ operation: 'createEvent' })(requiresLoggedUser(mutate));
