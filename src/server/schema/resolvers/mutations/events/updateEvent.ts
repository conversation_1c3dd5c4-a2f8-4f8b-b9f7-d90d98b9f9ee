import { URL, URLSearchParams } from 'url';
import type { TFunction } from 'i18next';
import { escapeRegExp, isEmpty, isNil, trim } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import {
    DealershipSettingType,
    type Banner,
    type Event,
    type EventApplicationModule,
    type EventUtmParametersOverride,
} from '../../../../database/documents';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { hasPaymentScenario } from '../../../../journeys/common/helpers';
import { EventPolicyAction } from '../../../../permissions';
import { getSimpleVersioningByUserForCreation, getSimpleVersioningByUserForUpdate } from '../../../../utils/versioning';
import { InvalidInput, InvalidPermission } from '../../../errors';
import { requiresLoggedUser } from '../../../middlewares';
import {
    type GraphQLBannerInput,
    type GraphQLEventUtmParametersOverrideInput,
    type GraphQLMutationResolvers,
    ModuleType,
} from '../../definitions';
import { upsertDealerAssignments } from '../modules/updateApplicationDealershipAssignmentsByDealer';
import { urlSlugRegex, convertCustomTestDriveBookingSlotsInput } from './shared';

export const createOrUpdateBannerEvent = async (
    bannerId: ObjectId | undefined,
    bannerData: GraphQLBannerInput,
    moduleId: ObjectId,
    userId: ObjectId
) => {
    const { collections } = await getDatabaseContext();

    if (!bannerData) {
        return bannerId;
    }

    if (bannerId) {
        const existingBanner = await collections.banners.findOne({ _id: bannerId });
        if (!existingBanner) {
            throw new Error('Banner not found');
        }

        await collections.banners.findOneAndUpdate(
            { _id: bannerId },
            { $set: { ...bannerData, ...getSimpleVersioningByUserForUpdate(userId) } },
            { returnDocument: 'after' }
        );

        return bannerId;
    }

    const document: Banner = {
        _id: new ObjectId(),
        ...bannerData,
        _versioning: getSimpleVersioningByUserForCreation(userId),
        moduleId,
    };

    const newBanner = await collections.banners.insertOne(document);

    return newBanner.insertedId;
};

type UTMParameterResult = {
    message: string;
    result: boolean;
};

export const retrieveUTMParameterQueryParameter = (
    override: GraphQLEventUtmParametersOverrideInput | EventUtmParametersOverride
) => {
    if (isEmpty(override.utmUrl)) {
        return null;
    }
    try {
        const url = new URL(override.utmUrl);
        const parametersString = new URLSearchParams(url.searchParams);

        return {
            utmUrl: override.utmUrl,
            utm_source: parametersString.get('utm_source'),
            utm_medium: parametersString.get('utm_medium'),
            utm_campaign: parametersString.get('utm_campaign'),
        };
    } catch (error) {
        // constructing URL may be wrong when the URL is invalid
        console.error(error);

        return null;
    }
};

export const validateEventUTMParameter = async (
    utmParametersSettingOverrides: GraphQLEventUtmParametersOverrideInput[],
    isDynamicEnable: boolean
): Promise<UTMParameterResult> => {
    if (!utmParametersSettingOverrides.length || !isDynamicEnable) {
        return {
            message: '',
            result: true,
        };
    }

    const validation: UTMParameterResult[] = await Promise.all(
        utmParametersSettingOverrides.map(async (override, index) => {
            const parametersString = retrieveUTMParameterQueryParameter(override);

            if (override.utmUrl.length > 2048) {
                return {
                    message: 'URL exceeds maximum length of 2048 characters',
                    result: false,
                };
            }

            if (!(override.utmUrl.includes('https://') || override.utmUrl.includes('http://'))) {
                return {
                    message: 'Invalid URL format. URL must start with http:// or https://',
                    result: false,
                };
            }

            // validate all the UTM Parameter Urls
            const hasDuplicateUrls = utmParametersSettingOverrides.some(
                (currentOverride, currentIndex) => override.utmUrl === currentOverride.utmUrl && currentIndex !== index
            );

            if (hasDuplicateUrls) {
                return {
                    message: `Duplicate UTM parameters detected`,
                    result: false,
                };
            }

            // validate isEmpty
            const utmParametersMissing = [];

            if (isNil(parametersString)) {
                return {
                    message: 'Invalid URL',
                    result: false,
                };
            }

            if (isEmpty(parametersString.utm_source)) {
                utmParametersMissing.push('utm_source');
            }

            if (isEmpty(parametersString.utm_medium)) {
                utmParametersMissing.push('utm_medium');
            }

            if (isEmpty(parametersString.utm_campaign)) {
                utmParametersMissing.push('utm_campaign');
            }

            if (utmParametersMissing.length) {
                return {
                    message: `${utmParametersMissing.toString()} is missing`,
                    result: false,
                };
            }

            return {
                message: '',
                result: true,
            };
        })
    );

    if (validation.every(validate => validate.result)) {
        return {
            message: '',
            result: true,
        };
    }

    return {
        message: validation.map(error => error.message).toString(),
        result: false,
    };
};

export const validateRedirectUrl = async (url: string, t: TFunction): Promise<string> => {
    try {
        // check accessible URL
        const response = await fetch(url, { method: 'HEAD' });

        if (response.ok) {
            return '';
        }
    } catch (error) {
        console.error(error);
    }

    return t('common:formErrors.redirectUrlBrokenOrInaccessible');
};

const mutate: GraphQLMutationResolvers['updateEvent'] = async (
    root,
    { id, eventInput },
    { getUser, getTranslations, getPermissionController, loaders }
) => {
    const { t } = await getTranslations(['errors', 'common']);

    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();
    const user = await getUser();
    const simpleVersion = getSimpleVersioningByUserForUpdate(user._id);
    const {
        publicSalesPerson,
        myInfoSetting,
        paymentSetting,
        dealerVehicles,
        eventLevelEmailSettings,
        banner,
        utmParametersSettings,
        customTestDriveBookingSlots,
        ...otherInput
    } = eventInput;

    if (hasPaymentScenario(eventInput.scenarios) && !eventInput.paymentSetting?.defaultId) {
        throw new InvalidInput({ paymentSetting: t('errors:paymentModuleNotSelected') });
    }

    if (!eventInput.privateAccess && !eventInput.publicSalesPerson?.defaultId) {
        throw new InvalidInput({ publicSalesPerson: t('errors:noResponsibleSalesPerson') });
    }

    const currentEvent = (await collections.events.findOne({
        _id: id,
        isDeleted: false,
    })) as Event;

    if (!currentEvent) {
        throw new InvalidInput({ id: t('errors:invalidEvent') });
    }

    const eventModule = (await collections.modules.findOne({
        _id: currentEvent.moduleId,
        _type: ModuleType.EventApplicationModule,
    })) as EventApplicationModule;

    if (!permissionController.events.mayOperateOn(currentEvent, EventPolicyAction.Update, eventModule)) {
        throw new InvalidPermission();
    }

    const hasUnauthorizedVehicles =
        (
            dealerVehicles
                ?.filter(inputDealer => {
                    const moduleVehicleIds = eventModule.dealerVehicles.find(dealer =>
                        dealer.dealerId.equals(inputDealer.dealerId)
                    )?.vehicleSuiteIds;

                    return !inputDealer.vehicleSuiteIds.every(id =>
                        moduleVehicleIds?.some(moduleVehicleId => moduleVehicleId.equals(id))
                    );
                })
                .filter(Boolean) || []
        ).length > 0;
    if (hasUnauthorizedVehicles) {
        throw new InvalidInput({ dealerVehicles: 'Unauthorized vehicle assignment!' });
    }

    const updatedCustomizedField = eventInput.customizedFields.map(field => {
        if (isNil(field.id)) {
            return {
                ...field,
                _id: new ObjectId(),
            };
        }

        return { ...field, _id: field.id };
    });

    //  exclude the current event to check if there are other events
    //  with the same url slug
    const existingUrlSlug = await collections.events.countDocuments({
        moduleId: currentEvent.moduleId,
        urlSlug: { $regex: new RegExp(`^${escapeRegExp(eventInput.urlSlug)}$`, 'i') },
        _id: { $ne: id },
        isDeleted: false,
    });

    if (!urlSlugRegex.test(eventInput.urlSlug)) {
        throw new InvalidInput({ urlSlug: 'URL only allow small letter alphanumeric and hyphen' });
    }

    if (existingUrlSlug > 0) {
        // check whether any same url identifier existed.
        throw new InvalidInput({ urlSlug: 'There is same URL Slug in same module' });
    }

    const validationUTMParameter: UTMParameterResult = await validateEventUTMParameter(
        utmParametersSettings.overrides,
        currentEvent.enableDynamicUtmTracking
    );

    if (!validationUTMParameter.result) {
        throw new InvalidInput({ $root: validationUTMParameter.message });
    }

    // check accessible redirect URL
    if (
        eventInput.thankYouPageContent?.isCustomRedirectionButton &&
        eventInput.thankYouPageContent?.redirectButton?.url
    ) {
        const validateRedirectUrlMessage = await validateRedirectUrl(
            eventInput.thankYouPageContent.redirectButton.url,
            t
        );
        if (validateRedirectUrlMessage) {
            throw new InvalidInput({ 'thankYouPageContent.redirectButton.url': validateRedirectUrlMessage });
        }
    }

    const updatedDealerVehicles = upsertDealerAssignments(currentEvent.dealerVehicles, dealerVehicles);

    const currentSubmitOrderEmailContent = currentEvent.emailContents?.submitOrder;
    const updatedSubmitOrderEmailContent = eventLevelEmailSettings?.submitOrder;

    const currentCustomerTestDriveEmailContent = currentEvent.emailContents?.testDrive?.customer;
    const updatedCustomerTestDriveEmailContent = eventLevelEmailSettings?.testDrive?.customer;

    const porscheIdModule = await collections.modules.findOne({
        companyId: eventModule.companyId,
        _type: ModuleType.PorscheIdModule,
    });

    const bannerId = await createOrUpdateBannerEvent(currentEvent.bannerId, banner, currentEvent.moduleId, user._id);

    return collections.events.findOneAndUpdate(
        { _id: id, isDeleted: false },
        {
            $set: {
                ...otherInput,
                ...simpleVersion,
                ...(customTestDriveBookingSlots && {
                    customTestDriveBookingSlots: convertCustomTestDriveBookingSlotsInput(customTestDriveBookingSlots),
                }),
                ...(updatedDealerVehicles && { dealerVehicles: updatedDealerVehicles }),
                ...(!eventInput.privateAccess && {
                    publicSalesPerson: {
                        ...publicSalesPerson,
                        _type: DealershipSettingType.PublicSalesPerson,
                    },
                }),
                ...(myInfoSetting?.defaultId && {
                    myInfoSetting: {
                        ...myInfoSetting,
                        _type: DealershipSettingType.MyInfoSetting,
                    },
                }),
                ...(hasPaymentScenario(eventInput.scenarios) && {
                    paymentSetting: {
                        ...paymentSetting,
                        _type: DealershipSettingType.PaymentSetting,
                    },
                }),
                // Skips email update when custom email contents is not enabled
                ...(eventInput.hasCustomiseEmail && {
                    emailContents: {
                        submitOrder: {
                            introImage: currentSubmitOrderEmailContent?.introImage,
                            ...updatedSubmitOrderEmailContent,
                        },
                        testDrive: {
                            customer: {
                                submitConfirmation: {
                                    introImage: currentCustomerTestDriveEmailContent?.submitConfirmation?.introImage,
                                    ...updatedCustomerTestDriveEmailContent?.submitConfirmation,
                                },
                                bookingAmendment: {
                                    introImage: currentCustomerTestDriveEmailContent?.bookingAmendment?.introImage,
                                    ...updatedCustomerTestDriveEmailContent?.bookingAmendment,
                                },
                                bookingCancellation: {
                                    introImage: currentCustomerTestDriveEmailContent?.bookingCancellation?.introImage,
                                    ...updatedCustomerTestDriveEmailContent?.bookingCancellation,
                                },
                                bookingConfirmation: {
                                    introImage: currentCustomerTestDriveEmailContent?.bookingConfirmation?.introImage,
                                    ...updatedCustomerTestDriveEmailContent?.bookingConfirmation,
                                },
                                completeTestDriveWithoutProcess: {
                                    introImage:
                                        currentCustomerTestDriveEmailContent?.completeTestDriveWithoutProcess
                                            ?.introImage,
                                    ...updatedCustomerTestDriveEmailContent?.completeTestDriveWithoutProcess,
                                },
                                endTestDriveWithProcess: {
                                    introImage:
                                        currentCustomerTestDriveEmailContent?.endTestDriveWithProcess?.introImage,
                                    ...updatedCustomerTestDriveEmailContent?.endTestDriveWithProcess,
                                },
                            },
                        },
                    },
                }),
                customizedFields: updatedCustomizedField,
                ...(porscheIdModule &&
                    !currentEvent.porscheIdModuleId &&
                    !eventInput.privateAccess && {
                        porscheIdModuleId: porscheIdModule._id,
                    }),
                bannerId,
                utmParametersSettings: {
                    ...utmParametersSettings,
                    overrides: utmParametersSettings.overrides.map(override => ({
                        ...override,
                        utmUrl: trim(override.utmUrl),
                    })),
                },
            },
            $unset: {
                ...(eventInput.privateAccess && { publicSalesPerson: true }),
                ...(!myInfoSetting?.defaultId && { myInfoSetting: true }),
                ...(!hasPaymentScenario(eventInput.scenarios) && { paymentSetting: true }),
            },
        },
        { returnDocument: 'after' }
    );
};

export default requiresLoggedUser(mutate);
