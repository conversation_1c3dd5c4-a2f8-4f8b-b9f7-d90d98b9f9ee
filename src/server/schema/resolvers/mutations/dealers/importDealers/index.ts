import { extname } from 'path';
import { ObjectId } from 'mongodb';
import { Dealer } from '../../../../../database/documents/Dealer';
import getDatabaseContext from '../../../../../database/getDatabaseContext';
import { getImportDealerExcelHeaders } from '../../../../../export/exportDealerTemplate';
import { upsertPermissions } from '../../../../../permissions/core';
import { generateDealerOrigins, generateDealerPermissions } from '../../../../../permissions/dealer';
import { getDataFromExcel } from '../../../../../utils/excel';
import { ALLOWED_WORKBOOK_EXTS } from '../../../../../utils/excel/constants';
import { allowedExtensions } from '../../../../../utils/extensions';
import validateFile from '../../../../../utils/validateFile';
import { getSimpleVersioningByUserForCreation } from '../../../../../utils/versioning';
import type { FileUploadPromise } from '../../../../context';
import { InvalidInput, InvalidPermission } from '../../../../errors';
import { buildRateLimiterMiddleware, requiresLoggedUser } from '../../../../middlewares';
import type { GraphQLMutationResolvers } from '../../../definitions';
import { ImportDealerResponseType } from '../../../typings';
import createDealersRoles from './createDealerRoles';
import createDealerUserGroups from './createUserGroups';
import { transformExcelRowToDealerData } from './transformDealerData';
import validateDealerData from './validateDealerData';

const validateAndProcessUpload = async (upload: FileUploadPromise, mappedHeaders: Record<string, string>) => {
    const acceptedExtensions = allowedExtensions.excel;
    const { filename, createReadStream } = await upload;
    const ext = extname(filename);

    if (!ALLOWED_WORKBOOK_EXTS.includes(ext.toLowerCase())) {
        throw new Error(
            `Unsupported file extension: ${ext}. Allowed extensions are: ${ALLOWED_WORKBOOK_EXTS.join(', ')}`
        );
    }

    const isFileValid = await validateFile(createReadStream(), filename, acceptedExtensions);
    if (!isFileValid) {
        throw new Error('Invalid File Format');
    }
    const { data, errors } = await getDataFromExcel({ excel: createReadStream(), mappedHeaders });

    if (errors.length > 0) {
        throw new Error(errors.join('\n'));
    }

    if (!data || data.length === 0) {
        throw new Error('No data found in the uploaded file.');
    }

    return data;
};

const mutation: GraphQLMutationResolvers['importDealers'] = async (
    root,
    { companyId, upload },
    { getUser, getPermissionController }
) => {
    const user = await getUser();
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();

    if (!permissionController.hasRootPermission()) {
        throw new InvalidPermission();
    }

    const company = await collections.companies.findOne({ _id: new ObjectId(companyId), isDeleted: false });
    if (!company) {
        throw new InvalidInput({ companyId: 'invalid company ID' });
    }

    try {
        const mappedHeaders = await getImportDealerExcelHeaders(company._id);

        const data = await validateAndProcessUpload(upload, mappedHeaders);

        const validationErrors = validateDealerData(data);

        if (Object.keys(validationErrors).length > 0) {
            const errorMessages = Object.entries(validationErrors).map(
                ([rowKey, rowErrors]) => `${rowKey}: ${rowErrors.join(', ')}`
            );

            return {
                _kind: ImportDealerResponseType.Fail,
                message: 'Validation failed. Please fix the following errors and try again.',
                errors: errorMessages,
            };
        }

        const versioning = getSimpleVersioningByUserForCreation(user._id);
        const dealers: Dealer[] = data.map(row =>
            transformExcelRowToDealerData({
                row,
                companyId: company._id,
                versioning,
                countryCode: company.countryCode,
            })
        );

        const result = await collections.dealers.insertMany(dealers, { ordered: false });

        await Promise.all(
            dealers.map(dealer => upsertPermissions(generateDealerPermissions(dealer), generateDealerOrigins(dealer)))
        );

        const createdRoleCount = await createDealersRoles({
            newDealers: dealers,
            company,
            versioning,
        });

        const createdUserGroupCount = await createDealerUserGroups({
            newDealers: dealers,
            company,
            simpleVersioning: versioning,
        });

        return {
            _kind: ImportDealerResponseType.Success,
            createdCount: result.insertedCount,
            createdRoleCount,
            createdUserGroupCount,
        };
    } catch (error) {
        return {
            _kind: ImportDealerResponseType.Fail,
            message: error.message || 'An unexpected error occurred during import',
            errors: error.errors || [],
        };
    }
};

export default buildRateLimiterMiddleware({ operation: 'importDealers' })(requiresLoggedUser(mutation));
