import getDatabaseContext from '../../../../database/getDatabaseContext';
import { CampaignPolicyAction } from '../../../../permissions/types/campaigns';
import { getSimpleVersioningByUserForUpdate } from '../../../../utils/versioning';
import { requiresLoggedUser } from '../../../middlewares';
import type { GraphQLMutationResolvers } from '../../definitions';

const mutation: GraphQLMutationResolvers['deleteCampaign'] = async (
    root,
    { id },
    { getPermissionController, getUser }
) => {
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();
    const user = await getUser();

    const versioning = getSimpleVersioningByUserForUpdate(user._id);

    // TODO (VF-1414): Add validation for campaign usage in modules and events
    // Before deleting, check if campaign is being used by:
    // 1. Modules - throw error: "{{Campaign}} is used by {{Module}}. Contact system administrator for..."
    // 2. LCF/Events - throw error: "{{Campaign}} is used by {{LCF}}. Change the Campaign Id in this LCF..."

    const deletedCampaign = await collections.campaigns.findOneAndUpdate(
        {
            $and: [
                { _id: id, isDeleted: false },
                permissionController.campaigns.getFilterQueryForAction(CampaignPolicyAction.Delete),
            ],
        },
        {
            $set: {
                isDeleted: true,
                ...versioning,
            },
        },
        { returnDocument: 'after' }
    );

    return !!deletedCampaign;
};

export default requiresLoggedUser(mutation);
