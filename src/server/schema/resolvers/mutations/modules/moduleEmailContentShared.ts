import { get, isEmpty, isNil, set } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import {
    AppointmentModule,
    AppointmentModuleEmailContents,
    ConfiguratorEmailContents,
    ConfiguratorModule,
    EmailWithScenarioOverrides,
    EventApplicationModule,
    EventApplicationModuleConfirmEmailContents,
    FinderApplicationModuleConfirmEmailContents,
    FinderApplicationModuleReminderEmailContents,
    FinderApplicationPrivateModule,
    FinderApplicationPublicModule,
    GiftVoucherModule,
    MobilityModule,
    SalesOfferModule,
    SalesOfferModuleEmailContents,
    StandardApplicationModule,
    StandardApplicationModuleEmailContents,
    VisitAppointmentModule,
    VisitAppointmentModuleEmailContents,
} from '../../../../database/documents';
import type {
    GraphQLAppointmentModuleEmailContentsInput,
    GraphQLConfiguratorModuleEmailContentsSetting,
    GraphQLEventApplicationModuleEmailContentsSetting,
    GraphQLEventApplicationModuleEmailWithScenarioOverridesSubmitOrderInput,
    GraphQLFinderApplicationEmailWithScenarioOverridesReminderInput,
    GraphQLFinderApplicationEmailWithScenarioOverridesSubmitOrderInput,
    GraphQLFinderApplicationModuleEmailContentsSetting,
    GraphQLGiftVoucherModuleEmailContentsInput,
    GraphQLMobilityEmailScenarioContentInput,
    GraphQLSalesOfferModuleDealerSpecificEmailContentInput,
    GraphQLStandardApplicationModuleEmailContentsInput,
    GraphQLVisitAppointmentModuleEmailContentsInput,
} from '../../definitions';

const emailContentType = ['subject', 'introTitle', 'contentText'];

const updateMobilityLocationEmailContent = (
    emailContentPath: string[],
    dealerId: ObjectId,
    module: MobilityModule,
    payloadEmailContentList: GraphQLMobilityEmailScenarioContentInput[]
) => {
    let updatedModule = module;

    /**
     * the payload IDs should not different from the database IDs
     * :: we always set predefined value and there is an ID for all location included home delivery
     *
     * id - all services block ID ( delivery and home delivery)
     * locationId  = non-home-delivery
     */
    const emailContentIds = payloadEmailContentList.map(content => content.id);

    // assuming ID have no error / missing
    emailContentIds.forEach(blockId => {
        const storedEmailContent = updatedModule.emailContents.find(content => content._id.equals(blockId));
        const payloadEmailContent = payloadEmailContentList.find(content => content.id.equals(blockId));

        /**
         * actions below is planned to update/push the latest value from payload into storedValue
         * yet we don't cover the defaultValue.
         */

        emailContentPath.forEach((contentPath, index) => {
            const storedValue = get(contentPath, storedEmailContent);
            const payloadValue = get(contentPath, payloadEmailContent);

            const storedIndex = storedValue.overrides.findIndex(override => override.dealerId.equals(dealerId));
            const payloadIndex = payloadValue.overrides.findIndex(override => override.dealerId.equals(dealerId));

            const isStoredExisted = storedIndex >= 0;
            const isPayloadExisted = payloadIndex >= 0;

            /**
             * try to replace the payload value into database override
             */

            /**
             * :: database ❌
             * :: payload ✅
             */
            if (!isStoredExisted && isPayloadExisted) {
                storedValue.overrides.push(payloadValue.overrides[payloadIndex]);
            }

            /**
             * :: database ✅
             * :: payload ✅
             */
            if (isStoredExisted && isPayloadExisted) {
                storedValue.overrides[storedIndex] = payloadValue.overrides[payloadIndex];
            }

            /**
             * :: database ✅
             * :: payload ❌
             */
            if (isStoredExisted && !isPayloadExisted) {
                storedValue.overrides = storedValue.overrides.filter(override => override.dealerId.equals(dealerId));
            }

            const blockIndexToUpdate = updatedModule.emailContents.findIndex(content => content._id.equals(blockId));

            updatedModule = set(`emailContents.${blockIndexToUpdate}.${contentPath}`, storedValue, updatedModule);
        });
    });

    return updatedModule;
};

export const updateMobilityEmailContentPayload = (
    emailContent: string[],
    dealerId: ObjectId,
    module: MobilityModule,
    payloadEmailContent: GraphQLMobilityEmailScenarioContentInput[]
) => {
    let updatedMobilityLocationModule = module;

    updatedMobilityLocationModule = updateMobilityLocationEmailContent(
        emailContent,
        dealerId,
        module,
        payloadEmailContent
    );

    return updatedMobilityLocationModule;
};

export const mergeScenarioEmailContentsWithOverride = (
    original:
        | EmailWithScenarioOverrides<FinderApplicationModuleConfirmEmailContents>
        | EmailWithScenarioOverrides<FinderApplicationModuleReminderEmailContents>
        | EmailWithScenarioOverrides<EventApplicationModuleConfirmEmailContents>,
    updated:
        | GraphQLFinderApplicationEmailWithScenarioOverridesReminderInput
        | GraphQLFinderApplicationEmailWithScenarioOverridesSubmitOrderInput
        | GraphQLEventApplicationModuleEmailWithScenarioOverridesSubmitOrderInput,
    excludeKeys: string[]
) => {
    if (!original || isEmpty(original)) {
        return updated;
    }

    const mergedDefaultValue = {
        ...original.defaultValue,
        ...updated.defaultValue,
        ...excludeKeys.reduce((acc, key) => {
            if (original.defaultValue[key] !== undefined) {
                acc[key] = original.defaultValue[key];
            }

            return acc;
        }, {}),
    };

    const mergedOverrides =
        updated.overrides?.map((updatedOverride, index) => {
            const originalOverride = original.overrides[index];

            if (!updatedOverride) {
                return updatedOverride;
            }

            return {
                ...originalOverride,
                ...updatedOverride,
                ...excludeKeys.reduce((acc, key) => {
                    if (originalOverride?.[key] !== undefined) {
                        acc[key] = originalOverride[key];
                    }

                    return acc;
                }, {}),
            };
        }) ?? original.overrides;

    return {
        defaultValue: mergedDefaultValue,
        overrides: mergedOverrides,
    };
};

const updateEmailContentPayload = (
    emailContent: string[],
    dealerId: ObjectId,
    module:
        | MobilityModule
        | ConfiguratorModule
        | EventApplicationModule
        | FinderApplicationPublicModule
        | FinderApplicationPrivateModule
        | GiftVoucherModule
        | AppointmentModule
        | StandardApplicationModule
        | VisitAppointmentModule
        | SalesOfferModule,
    payloadEmailContent:
        | GraphQLMobilityEmailScenarioContentInput
        | GraphQLGiftVoucherModuleEmailContentsInput
        | GraphQLEventApplicationModuleEmailContentsSetting
        | GraphQLConfiguratorModuleEmailContentsSetting
        | GraphQLAppointmentModuleEmailContentsInput
        | GraphQLFinderApplicationModuleEmailContentsSetting
        | GraphQLStandardApplicationModuleEmailContentsInput
        | GraphQLVisitAppointmentModuleEmailContentsInput
        | GraphQLSalesOfferModuleDealerSpecificEmailContentInput
):
    | MobilityModule
    | ConfiguratorModule
    | EventApplicationModule
    | FinderApplicationPublicModule
    | FinderApplicationPrivateModule
    | GiftVoucherModule
    | AppointmentModule
    | StandardApplicationModule
    | VisitAppointmentModule
    | SalesOfferModule => {
    let updatedModule = module;

    emailContent.forEach((content, index) => {
        // email content found in payload
        const payload = get(content, payloadEmailContent);
        // email content found in database
        const storedValue = get(content, updatedModule.emailContents);

        // index that payload is found the same dealer as the argument
        const payloadIndex = (payload?.overrides || []).findIndex(email =>
            new ObjectId(email.dealerId).equals(dealerId)
        );
        const storedIndex = (storedValue?.overrides || []).findIndex(email =>
            new ObjectId(email.dealerId).equals(dealerId)
        );

        const isPayloadExisted = payloadIndex >= 0;
        const isStoredExisted = storedIndex >= 0;

        // db no, payload yes
        if (!isStoredExisted && isPayloadExisted) {
            storedValue.overrides.push(payload.overrides[payloadIndex]);
        }
        // db yes , payload yes
        if (isStoredExisted && isPayloadExisted) {
            storedValue.overrides[storedIndex] = payload.overrides[payloadIndex];
        }

        // db yes , payload no
        if (isStoredExisted && !isPayloadExisted) {
            storedValue.overrides.filter(content => !new ObjectId(content.dealerId).equals(dealerId));
        }

        updatedModule = set(`emailContents.${content}`, storedValue, updatedModule);
    });

    return updatedModule;
};

const mergeEmailContentWithExclusion = <T>(
    existingEmailContents: T,
    payloadEmailContents:
        | GraphQLAppointmentModuleEmailContentsInput
        | GraphQLStandardApplicationModuleEmailContentsInput
        | GraphQLConfiguratorModuleEmailContentsSetting
        | GraphQLVisitAppointmentModuleEmailContentsInput
        | GraphQLSalesOfferModuleDealerSpecificEmailContentInput,

    fieldsToExclude: string[]
): T => {
    const mergedEmailContents = { ...existingEmailContents };

    Object.keys(existingEmailContents).forEach(userType => {
        const userEmails = existingEmailContents[userType];

        Object.keys(userEmails).forEach(emailType => {
            const existingEmail = userEmails[emailType];
            const payloadEmail = payloadEmailContents[userType]?.[emailType];

            if (!payloadEmail) {
                return;
            }

            const mergedEmailContent = { ...existingEmail, ...payloadEmail };

            fieldsToExclude.forEach(field => {
                if (existingEmail[field] !== undefined) {
                    mergedEmailContent[field] = existingEmail[field];
                }
            });

            mergedEmailContents[userType][emailType] = mergedEmailContent;
        });
    });

    return mergedEmailContents;
};

export const retrieveAppointmentModuleEmailContent = (
    dealerId: ObjectId,
    module: AppointmentModule,
    payloadEmailContent: GraphQLAppointmentModuleEmailContentsInput
) => {
    const testDriveAppointmentCustomerEmail = module.hasTestDriveProcess
        ? ['endTestDriveWithProcess']
        : ['completeTestDriveWithoutProcess'];
    const appointmentCustomerEmail = [
        'submitConfirmation',
        'bookingConfirmation',
        'bookingAmendment',
        ...testDriveAppointmentCustomerEmail,
    ];

    const testDriveAppointmentSalesPersonEmail = module.hasTestDriveProcess
        ? ['finderReservation', 'endTestDriveReminder']
        : [];

    const appointmentSalesPersonEmail = [
        'submitConfirmation',
        'bookingAmendment',
        ...testDriveAppointmentSalesPersonEmail,
    ];
    const appointmentEmailContentType = [...emailContentType, 'isSummaryVehicleVisible'];

    if (isNil(dealerId)) {
        const updatedModule: AppointmentModule = {
            ...module,
            emailContents: mergeEmailContentWithExclusion<AppointmentModuleEmailContents>(
                module.emailContents,
                payloadEmailContent,
                ['introImage']
            ),
        };

        return updatedModule;
    }

    const customerEmailContent = appointmentCustomerEmail
        .map(email => appointmentEmailContentType.map(type => `customer.${email}.${type}`))
        .flatMap(type => type);

    const salesPersonEmailContent = appointmentSalesPersonEmail
        .map(email => appointmentEmailContentType.map(contentType => `salesPerson.${email}.${contentType}`))
        .flatMap(type => type);

    const updatedCustomerEmailContentModule = updateEmailContentPayload(
        customerEmailContent,
        dealerId,
        module,
        payloadEmailContent
    );
    const updatedSalesPersonEmailContentModule = updateEmailContentPayload(
        salesPersonEmailContent,
        dealerId,
        updatedCustomerEmailContentModule,
        payloadEmailContent
    );

    return updatedSalesPersonEmailContentModule;
};

export const retrieveVisitAppointmentModuleEmailContent = (
    dealerId: ObjectId,
    module: VisitAppointmentModule,
    payloadEmailContent: GraphQLVisitAppointmentModuleEmailContentsInput
) => {
    const appointmentCustomerEmail = [
        'submitConfirmation',
        'bookingConfirmation',
        'bookingAmendment',
        'bookingComplete',
    ];
    const appointmentSalesPersonEmail = ['submitConfirmation', 'bookingAmendment'];
    const appointmentEmailContentType = [...emailContentType, 'isSummaryVehicleVisible'];

    if (isNil(dealerId)) {
        const updatedModule: VisitAppointmentModule = {
            ...module,
            emailContents: mergeEmailContentWithExclusion<VisitAppointmentModuleEmailContents>(
                module.emailContents,
                payloadEmailContent,
                ['introImage']
            ),
        };

        return updatedModule;
    }

    const customerEmailContent = appointmentCustomerEmail
        .map(email => appointmentEmailContentType.map(type => `customer.${email}.${type}`))
        .flatMap(type => type);

    const salesPersonEmailContent = appointmentSalesPersonEmail
        .map(email => appointmentEmailContentType.map(contentType => `salesPerson.${email}.${contentType}`))
        .flatMap(type => type);

    const updatedCustomerEmailContentModule = updateEmailContentPayload(
        customerEmailContent,
        dealerId,
        module,
        payloadEmailContent
    );
    const updatedSalesPersonEmailContentModule = updateEmailContentPayload(
        salesPersonEmailContent,
        dealerId,
        updatedCustomerEmailContentModule,
        payloadEmailContent
    );

    return updatedSalesPersonEmailContentModule;
};

export const retrieveStandardApplicationEmailContent = (
    dealerId: ObjectId,
    module: StandardApplicationModule,
    payloadEmailContent: GraphQLStandardApplicationModuleEmailContentsInput
) => {
    const standardApplicationCustomerEmail = [
        'share',
        'comparisonShare',
        'proceedWithCustomerDevice',
        'submissionConfirmation',
        'approved',
        'rejected',
        'cancelled',
    ];
    const standardApplicationSalesPersonEmail = ['approved', 'rejected'];
    const standardApplicationEmailContentType = [...emailContentType, 'isSummaryVehicleVisible'];

    if (isNil(dealerId)) {
        const updatedModule: StandardApplicationModule = {
            ...module,
            emailContents: mergeEmailContentWithExclusion<StandardApplicationModuleEmailContents>(
                module.emailContents,
                payloadEmailContent,
                ['introImage']
            ),
        };

        return updatedModule;
    }

    const customerEmailContent = standardApplicationCustomerEmail
        .map(email => standardApplicationEmailContentType.map(type => `customer.${email}.${type}`))
        .flatMap(type => type);

    const salesPersonEmailContent = standardApplicationSalesPersonEmail
        .map(email => standardApplicationEmailContentType.map(contentType => `salesPerson.${email}.${contentType}`))
        .flatMap(type => type);

    const updatedCustomerEmailContentModule = updateEmailContentPayload(
        customerEmailContent,
        dealerId,
        module,
        payloadEmailContent
    );
    const updatedSalesPersonEmailContentModule = updateEmailContentPayload(
        salesPersonEmailContent,
        dealerId,
        updatedCustomerEmailContentModule,
        payloadEmailContent
    );

    return updatedSalesPersonEmailContentModule;
};

export const retrieveGiftVoucherModuleEmailContent = (
    dealerId: ObjectId,
    module: GiftVoucherModule,
    payloadEmailContent: GraphQLGiftVoucherModuleEmailContentsInput
) => {
    if (isNil(dealerId)) {
        const updatedModule: GiftVoucherModule = {
            ...module,
            emailContents: {
                customer: payloadEmailContent.customer,
            },
        };

        return updatedModule;
    }
    const customerEmailContent = emailContentType.map(type => `customer.confirmation.${type}`);

    const updatedModule = updateEmailContentPayload(customerEmailContent, dealerId, module, payloadEmailContent);

    return updatedModule;
};

export const retrieveConfiguratorModuleEmailContent = (
    dealerId: ObjectId,
    module: ConfiguratorModule,
    payloadEmailContent: GraphQLConfiguratorModuleEmailContentsSetting
) => {
    const configuratorEmail = ['saveOrder', 'submitOrder'];

    if (isNil(dealerId)) {
        const updatedModule: ConfiguratorModule = {
            ...module,
            emailContents: mergeEmailContentWithExclusion<ConfiguratorEmailContents>(
                module.emailContents,
                payloadEmailContent,
                ['introImage', 'bannerImage']
            ),
        };

        return updatedModule;
    }
    const customerEmailContent = configuratorEmail
        .map(feature => emailContentType.map(type => `${feature}.${type}`))
        .flatMap(type => type);

    const updatedModule = updateEmailContentPayload(customerEmailContent, dealerId, module, payloadEmailContent);

    return updatedModule;
};

export const retrieveEventApplicationModuleEmailContent = (
    dealerId: ObjectId,
    module: EventApplicationModule,
    payloadEmailContent: GraphQLEventApplicationModuleEmailContentsSetting
) => {
    if (isNil(dealerId)) {
        const updatedModule: EventApplicationModule = {
            ...module,
            emailContents: {
                submitOrder: mergeScenarioEmailContentsWithOverride(
                    module.emailContents.submitOrder,
                    payloadEmailContent.submitOrder,
                    ['introImage']
                ),
            },
        };

        return updatedModule;
    }

    const eventEmail = ['submitOrder'];

    const emailContentDefault = eventEmail.flatMap(feature =>
        emailContentType.map(type => `${feature}.defaultValue.${type}`)
    );

    eventEmail.forEach(feature => {
        const featureOverrides = payloadEmailContent[feature]?.overrides;
        if (featureOverrides?.length) {
            featureOverrides.forEach((_, index) => {
                emailContentDefault.push(...emailContentType.map(type => `${feature}.overrides[${index}].${type}`));
            });
        }
    });

    return updateEmailContentPayload(emailContentDefault, dealerId, module, payloadEmailContent);
};

export const retrieveMobilityModuleEmailContent = (
    dealerId: ObjectId,
    module: MobilityModule,
    payloadEmailContent: GraphQLMobilityEmailScenarioContentInput[]
) => {
    if (isNil(dealerId)) {
        const updatedModule = {
            ...module,
            emailContents: payloadEmailContent.map(({ id, ...content }) => ({
                ...content,
                _id: id,
            })),
        };

        return updatedModule;
    }

    const mobilityCustomerEmail = [
        'bookingConfirmation',
        'bookingAmendment',
        'bookingCancellation',
        'bookingReminder',
        'bookingCheckedIn',
        'bookingComplete',
    ];
    const mobilityOperatorEmail = ['bookingConfirmation', 'bookingAmendment', 'bookingCancellation'];

    const mobilityCustomerEmailContent = mobilityCustomerEmail
        .map(email => emailContentType.map(type => `customers.${email}.${type}`))
        .flatMap(type => type);

    const mobilityOperatorEmailContent = mobilityOperatorEmail
        .map(email => emailContentType.map(contentType => `operators.${email}.${contentType}`))
        .flatMap(type => type);

    const updatedCustomerEmailContentModule = updateMobilityEmailContentPayload(
        mobilityCustomerEmailContent,
        dealerId,
        module,
        payloadEmailContent
    );
    const updatedOperatoEmailContentModule = updateMobilityEmailContentPayload(
        mobilityOperatorEmailContent,
        dealerId,
        updatedCustomerEmailContentModule,
        payloadEmailContent
    );

    return updatedOperatoEmailContentModule;
};

export const retrieveSalesOfferModuleEmailContent = (
    dealerId: ObjectId,
    module: SalesOfferModule,
    payloadEmailContent: GraphQLSalesOfferModuleDealerSpecificEmailContentInput
) => {
    if (isNil(dealerId)) {
        const updatedModule: SalesOfferModule = {
            ...module,
            emailContents: mergeEmailContentWithExclusion<SalesOfferModuleEmailContents>(
                module.emailContents,
                payloadEmailContent,
                []
            ),
        };

        return updatedModule;
    }

    const salesOfferEmail = [
        'combinedTemplate',
        'singlePreOfferTemplate',
        'salesOfferTemplate.customer',
        'salesOfferTemplate.salesManager',
        'shareTemplate',
    ];

    const emailContentDefault = salesOfferEmail.flatMap(feature => emailContentType.map(type => `${feature}.${type}`));

    salesOfferEmail.forEach(feature => {
        const featureOverrides = payloadEmailContent[feature]?.overrides;
        if (featureOverrides?.length) {
            featureOverrides.forEach((_, index) => {
                emailContentDefault.push(...emailContentType.map(type => `${feature}.overrides[${index}].${type}`));
            });
        }
    });

    return updateEmailContentPayload(emailContentDefault, dealerId, module, payloadEmailContent);
};

export const retrieveFinderApplicationModuleEmailContent = (
    dealerId: ObjectId,
    module: FinderApplicationPrivateModule | FinderApplicationPublicModule,
    payloadEmailContent: GraphQLFinderApplicationModuleEmailContentsSetting
) => {
    const finderEmail = ['submitOrder', 'reminderEmail'];

    if (isNil(dealerId)) {
        return {
            ...module,
            emailContents: {
                reminderEmail: payloadEmailContent.reminderEmail,
                submitOrder: payloadEmailContent.submitOrder,
            },
        };
    }

    const emailContentDefault = finderEmail.flatMap(feature =>
        emailContentType.map(type => `${feature}.defaultValue.${type}`)
    );

    finderEmail.forEach(feature => {
        const featureOverrides = payloadEmailContent[feature]?.overrides;
        if (featureOverrides?.length) {
            featureOverrides.forEach((_, index) => {
                emailContentDefault.push(...emailContentType.map(type => `${feature}.overrides[${index}].${type}`));
            });
        }
    });

    return updateEmailContentPayload(emailContentDefault, dealerId, module, payloadEmailContent);
};
