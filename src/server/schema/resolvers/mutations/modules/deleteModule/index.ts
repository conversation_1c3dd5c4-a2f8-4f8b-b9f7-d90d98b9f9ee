import { ModuleType, SettingId, type Module } from '../../../../../database/documents';
import getDatabaseContext from '../../../../../database/getDatabaseContext';
import { deleteModulePermissions } from '../../../../../permissions/modules';
import { ModulePolicyAction } from '../../../../../permissions/types/modules';
import { requiresLoggedUser } from '../../../../middlewares';
import type { GetLoggedUser } from '../../../../user';
import type { GraphQLMutationResolvers } from '../../../definitions';
import { deleteSingleBank } from '../../bank/deleteBank';
import { deleteSingleInsurer } from '../../insurers/deleteInsurer';
import { validateModule } from './validateModuleRelations';

const deleteRelatedData = async (module: Module, getUser: GetLoggedUser) => {
    const { collections } = await getDatabaseContext();
    switch (module._type) {
        case ModuleType.BankModule: {
            const banks = await collections.banks
                .find({
                    moduleId: module._id,
                    isDeleted: false,
                })
                .toArray();

            return Promise.all(banks.map(i => deleteSingleBank(i._id, getUser)));
        }

        case ModuleType.InsuranceModule: {
            const insurers = await collections.insurers
                .find({
                    moduleId: module._id,
                    isDeleted: false,
                })
                .toArray();

            return Promise.all(insurers.map(i => deleteSingleInsurer(i._id, getUser)));
        }

        case ModuleType.AutoplayModule: {
            await collections.settings.deleteOne({
                settingId: SettingId.Autoplay,
                moduleId: module._id,
            });

            return true;
        }

        case ModuleType.TradeInModule: {
            await collections.settings.deleteOne({
                settingId: SettingId.TradeIn,
                moduleId: module._id,
            });

            return true;
        }

        case ModuleType.PorscheIdModule: {
            await collections.settings.deleteOne({ porscheIdModuleId: module._id, settingId: SettingId.PorscheId });

            await collections.modules.updateMany(
                {
                    porscheIdModuleId: module._id,
                    _type: { $in: [ModuleType.ConfiguratorModule, ModuleType.FinderApplicationPublicModule] },
                },
                {
                    $unset: {
                        porscheIdModuleId: 1,
                        isCustomerDataRetreivalByPorscheId: 1,
                        isPorscheIdLoginMandatory: 1,
                    },
                }
            );

            await collections.events.updateMany(
                { porscheIdModuleId: module._id },
                {
                    $unset: {
                        porscheIdModuleId: 1,
                        isCustomerDataRetreivalByPorscheId: 1,
                        isPorscheIdLoginMandatory: 1,
                    },
                }
            );

            return [];
        }

        case ModuleType.PorscheRetainModule: {
            await collections.settings.deleteOne({
                settingId: SettingId.porscheRetainModuleIntegration,
                moduleId: module._id,
            });

            return true;
        }

        case ModuleType.VehicleDataWithPorscheCodeIntegrationModule: {
            await collections.settings.deleteMany({
                moduleId: module._id,
                settingId: SettingId.VehicleDataWithPorscheCodeIntegration,
            });

            return true;
        }

        default:
            return true;
    }
};

const mutation: GraphQLMutationResolvers['deleteModule'] = async (
    root,
    { moduleId },
    { loaders, getPermissionController, getTranslations, getUser }
) => {
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();
    const { t } = await getTranslations(['errors']);

    const module = await loaders.moduleById.load(moduleId);

    const moduleData = await validateModule(module);

    if (moduleData.length > 0) {
        throw new Error(
            t('errors:cannotDeleteModule', { moduleData: moduleData.join(', ').replace(/,([^,]*)$/, ' and $1') })
        );
    }

    const { deletedCount } = await collections.modules.deleteOne({
        $and: [{ _id: moduleId }, permissionController.modules.getFilterQueryForAction(ModulePolicyAction.Delete)],
    });

    if (deletedCount > 0) {
        await deleteModulePermissions(module);
        await deleteRelatedData(module, getUser);
    }

    return deletedCount > 0;
};

export default requiresLoggedUser(mutation);
