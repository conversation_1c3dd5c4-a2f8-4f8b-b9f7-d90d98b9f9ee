import { ObjectId } from 'mongodb';
import {
    ModuleType,
    TimeSlotCore,
    TimeSlotEnum,
    VisitAppointmentModule,
    VisitAppointmentModuleEmailContent,
} from '../../../../../database/documents';
import getDatabaseContext from '../../../../../database/getDatabaseContext';
import { CompanyPolicyAction } from '../../../../../permissions';
import { mainQueue } from '../../../../../queues';
import { getSimpleVersioningByUserForCreation } from '../../../../../utils/versioning';
import { InvalidInput } from '../../../../errors';
import { buildRateLimiterMiddleware, requiresLoggedUser } from '../../../../middlewares';
import { GraphQLMutationResolvers } from '../../../definitions';

const getDefaultEmailContentItem = (defaultValue: string) => ({
    defaultValue: {
        defaultValue,
        overrides: [],
    },
    overrides: [],
});

const getDefaultEmailContent = (
    subject: string,
    introTitle: string,
    contentText: string
): VisitAppointmentModuleEmailContent => ({
    subject: getDefaultEmailContentItem(subject),
    introTitle: getDefaultEmailContentItem(introTitle),
    contentText: getDefaultEmailContentItem(contentText),
    isSummaryVehicleVisible: { defaultValue: true, overrides: [] },
});

const mutation: GraphQLMutationResolvers['createVisitAppointmentModule'] = async (
    root,
    { companyId, settings },
    { getPermissionController, getUser, getTranslations }
) => {
    const user = await getUser();
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();

    const { t } = await getTranslations(['emails']);

    // check the company ID validity
    const company = await collections.companies.findOne({
        $and: [
            { _id: companyId },
            permissionController.companies.getFilterQueryForAction(CompanyPolicyAction.CreateModule),
        ],
    });

    if (!company) {
        throw new InvalidInput({ companyId: 'invalid company ID' });
    }

    const simpleVersioning = await getSimpleVersioningByUserForCreation(user._id);

    const { bookingTimeSlot, ...values } = settings;
    const visitAppointmentTimeSlot: TimeSlotCore<TimeSlotEnum.Appointment>[] = bookingTimeSlot.map(slot => ({
        ...slot,
        _type: TimeSlotEnum.Appointment,
    }));

    // compose document
    const document: VisitAppointmentModule = {
        _id: new ObjectId(),
        _type: ModuleType.VisitAppointmentModule,
        companyId,
        emailContents: {
            customer: {
                submitConfirmation: getDefaultEmailContent(
                    t('emails:showroomVisitAppointment.customerSubmitConfirmation.subject'),
                    t('emails:showroomVisitAppointment.customerSubmitConfirmation.introTitle'),
                    t('emails:showroomVisitAppointment.customerSubmitConfirmation.contentText')
                ),
                bookingAmendment: getDefaultEmailContent(
                    t('emails:showroomVisitAppointment.customerBookingAmendment.subject'),
                    t('emails:showroomVisitAppointment.customerBookingAmendment.introTitle'),
                    t('emails:showroomVisitAppointment.customerBookingAmendment.contentText')
                ),
                bookingConfirmation: getDefaultEmailContent(
                    t('emails:showroomVisitAppointment.customerBookingConfirmation.subject'),
                    t('emails:showroomVisitAppointment.customerBookingConfirmation.introTitle'),
                    t('emails:showroomVisitAppointment.customerBookingConfirmation.contentText')
                ),
                bookingCancellation: getDefaultEmailContent(
                    t('emails:showroomVisitAppointment.customerBookingCancellation.subject'),
                    t('emails:showroomVisitAppointment.customerBookingCancellation.introTitle'),
                    t('emails:showroomVisitAppointment.customerBookingCancellation.contentText')
                ),
                bookingComplete: getDefaultEmailContent(
                    t('emails:showroomVisitAppointment.customerBookingComplete.subject'),
                    t('emails:showroomVisitAppointment.customerBookingComplete.introTitle'),
                    t('emails:showroomVisitAppointment.customerBookingComplete.contentText')
                ),
            },
            salesPerson: {
                submitConfirmation: getDefaultEmailContent(
                    t('emails:showroomVisitAppointment.salesPersonSubmitConfirmation.subject'),
                    t('emails:showroomVisitAppointment.salesPersonSubmitConfirmation.introTitle'),
                    t('emails:showroomVisitAppointment.salesPersonSubmitConfirmation.contentText')
                ),
                bookingAmendment: getDefaultEmailContent(
                    t('emails:showroomVisitAppointment.salesPersonBookingAmendment.subject'),
                    t('emails:showroomVisitAppointment.salesPersonBookingAmendment.introTitle'),
                    t('emails:showroomVisitAppointment.salesPersonBookingAmendment.contentText')
                ),
                bookingConfirmation: getDefaultEmailContent(
                    t('emails:showroomVisitAppointment.salesPersonBookingConfirmation.subject'),
                    t('emails:showroomVisitAppointment.salesPersonBookingConfirmation.introTitle'),
                    t('emails:showroomVisitAppointment.salesPersonBookingConfirmation.contentText')
                ),
                bookingCancellation: getDefaultEmailContent(
                    t('emails:showroomVisitAppointment.salesPersonBookingCancellation.subject'),
                    t('emails:showroomVisitAppointment.salesPersonBookingCancellation.introTitle'),
                    t('emails:showroomVisitAppointment.salesPersonBookingCancellation.contentText')
                ),
            },
        },
        ...values,
        bookingTimeSlot: visitAppointmentTimeSlot,
        _versioning: simpleVersioning,
    };

    // insert document in the database
    await collections.modules.insertOne(document);
    await mainQueue.add({ type: 'upsertPermissions', target: 'module', moduleId: document._id });

    return document;
};

export default buildRateLimiterMiddleware({ operation: 'createvisitAppointmentModule' })(requiresLoggedUser(mutation));
