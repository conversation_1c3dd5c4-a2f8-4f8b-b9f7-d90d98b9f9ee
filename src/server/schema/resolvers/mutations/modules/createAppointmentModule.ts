import { ObjectId } from 'mongodb';
import {
    ModuleType,
    AppointmentModule,
    TimeSlotEnum,
    TimeSlotCore,
    AppointmentModuleEmailContent,
} from '../../../../database/documents';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { CompanyPolicyAction } from '../../../../permissions';
import { mainQueue } from '../../../../queues';
import { getSimpleVersioningByUserForCreation } from '../../../../utils/versioning';
import { InvalidInput } from '../../../errors';
import { buildRateLimiterMiddleware, requiresLoggedUser } from '../../../middlewares';
import { GraphQLMutationResolvers } from '../../definitions';

const getDefaultEmailContentItem = (defaultValue: string) => ({
    defaultValue: {
        defaultValue,
        overrides: [],
    },
    overrides: [],
});

const getDefaultEmailContent = (
    subject: string,
    introTitle: string,
    contentText: string
): AppointmentModuleEmailContent => ({
    subject: getDefaultEmailContentItem(subject),
    introTitle: getDefaultEmailContentItem(introTitle),
    contentText: getDefaultEmailContentItem(contentText),
    isSummaryVehicleVisible: { defaultValue: true, overrides: [] },
});

const mutation: GraphQLMutationResolvers['createAppointmentModule'] = async (
    root,
    { companyId, settings },
    { getPermissionController, getUser, getTranslations }
) => {
    const user = await getUser();
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();

    const { t } = await getTranslations(['emails']);

    // check the company ID validity
    const company = await collections.companies.findOne({
        $and: [
            { _id: companyId },
            permissionController.companies.getFilterQueryForAction(CompanyPolicyAction.CreateModule),
        ],
    });

    if (!company) {
        throw new InvalidInput({ companyId: 'invalid company ID' });
    }

    const simpleVersioning = await getSimpleVersioningByUserForCreation(user._id);

    const { bookingTimeSlot, ...values } = settings;
    const appointmentTimeSlot: TimeSlotCore<TimeSlotEnum.Appointment>[] = bookingTimeSlot.map(slot => ({
        ...slot,
        _type: TimeSlotEnum.Appointment,
    }));

    // compose document
    const document: AppointmentModule = {
        _id: new ObjectId(),
        _type: ModuleType.AppointmentModule,
        companyId,
        emailContents: {
            customer: {
                submitConfirmation: getDefaultEmailContent(
                    t('emails:appointment.customerSubmitConfirmation.subject'),
                    t('emails:appointment.customerSubmitConfirmation.introTitle'),
                    t('emails:appointment.customerSubmitConfirmation.contentText')
                ),
                endTestDriveWithProcess: getDefaultEmailContent(
                    t('emails:appointment.customerEndTestDriveWithProcess.subject'),
                    t('emails:appointment.customerEndTestDriveWithProcess.introTitle'),
                    t('emails:appointment.customerEndTestDriveWithProcess.contentText')
                ),
                completeTestDriveWithoutProcess: getDefaultEmailContent(
                    t('emails:appointment.customerCompleteTestDriveWithoutProcess.subject'),
                    t('emails:appointment.customerCompleteTestDriveWithoutProcess.introTitle'),
                    t('emails:appointment.customerCompleteTestDriveWithoutProcess.contentText')
                ),
                bookingAmendment: getDefaultEmailContent(
                    t('emails:appointment.customerBookingAmendment.subject'),
                    t('emails:appointment.customerBookingAmendment.introTitle'),
                    t('emails:appointment.customerBookingAmendment.contentText')
                ),
                bookingConfirmation: getDefaultEmailContent(
                    t('emails:appointment.customerBookingConfirmation.subject'),
                    t('emails:appointment.customerBookingConfirmation.introTitle'),
                    t('emails:appointment.customerBookingConfirmation.contentText')
                ),
                bookingCancellation: getDefaultEmailContent(
                    t('emails:appointment.customerBookingCancellation.subject'),
                    t('emails:appointment.customerBookingCancellation.introTitle'),
                    t('emails:appointment.customerBookingCancellation.contentText')
                ),
            },
            salesPerson: {
                submitConfirmation: getDefaultEmailContent(
                    t('emails:appointment.salesPersonSubmitConfirmation.subject'),
                    t('emails:appointment.salesPersonSubmitConfirmation.introTitle'),
                    t('emails:appointment.salesPersonSubmitConfirmation.contentText')
                ),
                bookingAmendment: getDefaultEmailContent(
                    t('emails:appointment.salesPersonBookingAmendment.subject'),
                    t('emails:appointment.salesPersonBookingAmendment.introTitle'),
                    t('emails:appointment.salesPersonBookingAmendment.contentText')
                ),
                bookingConfirmation: getDefaultEmailContent(
                    t('emails:appointment.salesPersonBookingConfirmation.subject'),
                    t('emails:appointment.salesPersonBookingConfirmation.introTitle'),
                    t('emails:appointment.salesPersonBookingConfirmation.contentText')
                ),
                bookingCancellation: getDefaultEmailContent(
                    t('emails:appointment.salesPersonBookingCancellation.subject'),
                    t('emails:appointment.salesPersonBookingCancellation.introTitle'),
                    t('emails:appointment.salesPersonBookingCancellation.contentText')
                ),
                finderReservation: {
                    ...getDefaultEmailContent(
                        t('emails:appointment.salesPersonFinderReservation.subject'),
                        t('emails:appointment.salesPersonFinderReservation.introTitle'),
                        t('emails:appointment.salesPersonFinderReservation.contentText')
                    ),
                    listTestDriveTitle: getDefaultEmailContentItem(
                        t('emails:appointment.salesPersonFinderReservation.listTestDriveTitle')
                    ),
                    listTestDriveItem: getDefaultEmailContentItem(
                        t('emails:appointment.salesPersonFinderReservation.listTestDriveItem')
                    ),
                },
                endTestDriveReminder: getDefaultEmailContent(
                    t('emails:appointment.salesPersonEndTestDriveReminder.subject'),
                    t('emails:appointment.salesPersonEndTestDriveReminder.introTitle'),
                    t('emails:appointment.salesPersonEndTestDriveReminder.contentText')
                ),
            },
        },
        ...values,
        bookingTimeSlot: appointmentTimeSlot,
        _versioning: simpleVersioning,
    };

    // insert document in the database
    await collections.modules.insertOne(document);
    await mainQueue.add({ type: 'upsertPermissions', target: 'module', moduleId: document._id });

    return document;
};

export default buildRateLimiterMiddleware({ operation: 'createAppointmentModule' })(requiresLoggedUser(mutation));
