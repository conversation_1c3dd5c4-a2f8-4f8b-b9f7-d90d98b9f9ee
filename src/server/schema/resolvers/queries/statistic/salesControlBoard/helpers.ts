import dayjs from 'dayjs';
import { AggregationCursor, ObjectId } from 'mongodb';
import { PeriodPayload } from '../../../../../../app/api';
import { Collections } from '../../../../../database';
import {
    ApplicationStatus,
    SalesControlBoardDataType,
    SalesControlBoardModule,
    User,
    VehicleKind,
} from '../../../../../database/documents';

const parseCount = (value: number) => parseFloat(value.toFixed(1));

const getPercentage = (applied: number, total: number) =>
    total > 0 ? parseFloat(((applied / total) * 100).toFixed(0)) : 0;

const getPercentageRound2 = (applied: number, total: number) =>
    total > 0 ? parseFloat(((applied / total) * 100).toFixed(2)) : 0;

type SalesControlBoardSetting = {
    testDriveMonthlyTarget: number;
    orderIntakesMonthlyTarget: number;
    retailsMonthlyTarget: number;
    financeCommissionMonthlyTarget: number;
    insuranceCommissionMonthlyTarget: number;
    allSalesConsultantIds: ObjectId[];
};

export const getSalesControlBoardSettingFromDealer = (
    dealerId: ObjectId,
    salesControlBoardModule: SalesControlBoardModule
): SalesControlBoardSetting => {
    const allSalesConsultantIds =
        salesControlBoardModule.salesConsultantsAssignments.overrides.find(({ dealerId: overrideDealerId }) =>
            overrideDealerId?.equals(dealerId)
        ).value ?? [];

    const orderIntakesMonthlyTarget =
        salesControlBoardModule.orderIntakesMonthlyTarget.overrides.find(({ dealerId: overrideDealerId }) =>
            overrideDealerId?.equals(dealerId)
        )?.value ?? 0;

    const testDriveMonthlyTarget =
        salesControlBoardModule.testDriveMonthlyTarget.overrides.find(({ dealerId: overrideDealerId }) =>
            overrideDealerId?.equals(dealerId)
        )?.value ?? 0;

    const retailsMonthlyTarget =
        salesControlBoardModule.retailsMonthlyTarget.overrides.find(({ dealerId: overrideDealerId }) =>
            overrideDealerId?.equals(dealerId)
        )?.value ?? 0;

    const financeCommissionMonthlyTarget =
        salesControlBoardModule.financeCommissionMonthlyTarget.overrides.find(({ dealerId: overrideDealerId }) =>
            overrideDealerId?.equals(dealerId)
        )?.value ?? 0;

    const insuranceCommissionMonthlyTarget =
        salesControlBoardModule.insuranceCommissionMonthlyTarget.overrides.find(({ dealerId: overrideDealerId }) =>
            overrideDealerId?.equals(dealerId)
        )?.value ?? 0;

    return {
        allSalesConsultantIds,
        retailsMonthlyTarget,
        financeCommissionMonthlyTarget,
        insuranceCommissionMonthlyTarget,
        orderIntakesMonthlyTarget,
        testDriveMonthlyTarget,
    };
};

type ModelAverageTarget = {
    orderIntakesMonthlyModelAverageTarget: number;
    testDriveMonthlyModelAverageTarget: number;
    retailsMonthlyModelAverageTarget: number;
};

export const getModelAverageTarget = (
    allModelLength: number,
    salesControlBoardSetting: SalesControlBoardSetting
): ModelAverageTarget => {
    const { orderIntakesMonthlyTarget, testDriveMonthlyTarget, retailsMonthlyTarget } = salesControlBoardSetting;

    const orderIntakesMonthlyModelAverageTarget = allModelLength === 0 ? 0 : orderIntakesMonthlyTarget / allModelLength;
    const testDriveMonthlyModelAverageTarget = allModelLength === 0 ? 0 : testDriveMonthlyTarget / allModelLength;
    const retailsMonthlyModelAverageTarget = allModelLength === 0 ? 0 : retailsMonthlyTarget / allModelLength;

    return {
        orderIntakesMonthlyModelAverageTarget,
        testDriveMonthlyModelAverageTarget,
        retailsMonthlyModelAverageTarget,
    };
};

export const getFICommissions = async ({
    dealerId,
    salesControlBoardSetting,
    hasManagerPermission,
    monthOfImport,
    selectedSalesConsultants,
    selectedVehicleModelIds,
    collections,
}: {
    dealerId: ObjectId;
    salesControlBoardSetting: SalesControlBoardSetting;
    hasManagerPermission: boolean;
    monthOfImport: string;
    selectedSalesConsultants: User[];
    selectedVehicleModelIds: ObjectId[];
    collections: Collections;
}) => {
    if (!hasManagerPermission) {
        return [];
    }

    const { financeCommissionMonthlyTarget, insuranceCommissionMonthlyTarget } = salesControlBoardSetting;

    const sumCommissions = await (
        collections.salesControlBoards.aggregate([
            {
                $match: {
                    dealerId,
                    source: SalesControlBoardDataType.Retails,
                    importDate: monthOfImport,
                    modelId: { $in: selectedVehicleModelIds },
                    salesConsultantId: { $in: selectedSalesConsultants.map(i => i._id) },
                },
            },
            {
                $group: {
                    _id: '$salesConsultantId',
                    ytdActual: {
                        $sum: '$ytd.actual',
                    },
                    ytdFinanceApplied: {
                        $sum: '$ytd.appliedFinance',
                    },
                    ytdInsuranceApplied: {
                        $sum: '$ytd.appliedInsurance',
                    },
                    monthActual: {
                        $sum: '$month.actual',
                    },
                    monthFinanceApplied: {
                        $sum: '$month.appliedFinance',
                    },
                    monthInsuranceApplied: {
                        $sum: '$month.appliedInsurance',
                    },
                    threeMonthsActual: {
                        $sum: '$threeMonths.actual',
                    },
                    threeMonthsFinanceApplied: {
                        $sum: '$threeMonths.appliedFinance',
                    },
                    threeMonthsInsuranceApplied: {
                        $sum: '$threeMonths.appliedInsurance',
                    },
                },
            },
        ]) as AggregationCursor<{
            _id: ObjectId;
            ytdActual: number;
            monthActual: number;
            threeMonthsActual: number;
            monthFinanceApplied: number;
            ytdFinanceApplied: number;
            threeMonthsFinanceApplied: number;
            monthInsuranceApplied: number;
            ytdInsuranceApplied: number;
            threeMonthsInsuranceApplied: number;
        }>
    ).toArray();

    const fiCommissions = selectedSalesConsultants
        .map((salesConsultant, index) => {
            const find = sumCommissions.find(i => i._id.equals(salesConsultant._id));

            return {
                salesConsultantName: salesConsultant.displayName,
                inHouseFinanceTarget: financeCommissionMonthlyTarget,
                inHouseFinanceMtd: find ? getPercentage(find.monthFinanceApplied, find.monthActual) : 0,
                inHouseFinanceYtd: find ? getPercentage(find.ytdFinanceApplied, find.ytdActual) : 0,
                inHouseFinance3MAvg: find ? getPercentage(find.threeMonthsFinanceApplied, find.threeMonthsActual) : 0,
                inHouseInsuranceTarget: insuranceCommissionMonthlyTarget,
                inHouseInsuranceMtd: find ? getPercentage(find.monthInsuranceApplied, find.monthActual) : 0,
                inHouseInsuranceYtd: find ? getPercentage(find.ytdInsuranceApplied, find.ytdActual) : 0,
                inHouseInsurance3MAvg: find
                    ? getPercentage(find.threeMonthsInsuranceApplied, find.threeMonthsActual)
                    : 0,
            };
        })
        .filter(Boolean);

    return fiCommissions;
};

const getWeeksInCurrentMonth = (monthOfImport: string) => {
    if (!monthOfImport) {
        return [];
    }
    const firstDay = dayjs(monthOfImport).startOf('month');
    const lastDay = dayjs(monthOfImport).endOf('month');

    let current = firstDay.startOf('week').add(1, 'day');

    if (current.isBefore(firstDay, 'day')) {
        current = current.add(7, 'day');
    }

    const weeks = [];

    if (current.isAfter(firstDay)) {
        weeks.push({
            start: firstDay.toDate(),
            end: current.subtract(1, 'day').toDate(),
        });
    }

    while (current.isBefore(lastDay) || current.isSame(lastDay, 'day')) {
        const weekEnd = current.add(6, 'day');

        const startOfWeek = current.isAfter(firstDay) ? current : firstDay;
        const endOfWeek = weekEnd.isBefore(lastDay) ? weekEnd : lastDay;

        weeks.push({
            start: startOfWeek.toDate(),
            end: endOfWeek.toDate(),
        });

        current = current.add(7, 'day');
    }

    return weeks;
};

export const getWeekFunnels = async ({
    dealerId,
    monthOfImport,
    collections,
    selectedSalesConsultantIds,
    selectedVehicleModelIds,
}: {
    dealerId: ObjectId;
    monthOfImport: string;
    collections: Collections;
    selectedSalesConsultantIds: ObjectId[];
    selectedVehicleModelIds: ObjectId[];
}) => {
    if (selectedSalesConsultantIds.length === 0) {
        return [];
    }

    const weeks = getWeeksInCurrentMonth(monthOfImport);

    const salesControlBoards = await (
        collections.salesControlBoards.aggregate([
            {
                $match: {
                    dealerId,
                    importDate: monthOfImport,
                    modelId: { $in: selectedVehicleModelIds },
                    salesConsultantId: { $in: selectedSalesConsultantIds },
                },
            },
            { $unwind: '$weeks' },
            {
                $group: {
                    _id: {
                        source: '$source',
                        week: '$weeks.numberOfWeek',
                    },
                    totalCount: { $sum: '$weeks.count' },
                },
            },
            {
                $project: {
                    _id: 0,
                    source: '$_id.source',
                    week: '$_id.week',
                    totalCount: 1,
                },
            },
            { $sort: { source: 1, week: 1 } },
        ]) as AggregationCursor<{
            source: SalesControlBoardDataType;
            week: number;
            totalCount: number;
        }>
    ).toArray();

    const weekFunnels = await Promise.all(
        weeks.map(async (week, index) => {
            const leadsCreated =
                salesControlBoards.find(i => i.source === SalesControlBoardDataType.Leads && i.week === index + 1)
                    ?.totalCount ?? 0;

            const orderIntakes =
                salesControlBoards.find(
                    i => i.source === SalesControlBoardDataType.OrderIntakes && i.week === index + 1
                )?.totalCount ?? 0;

            const retails =
                salesControlBoards.find(i => i.source === SalesControlBoardDataType.Retails && i.week === index + 1)
                    ?.totalCount ?? 0;

            const testDrivePipeline = getTestDrivePipeline(
                dealerId,
                week,
                selectedSalesConsultantIds,
                selectedVehicleModelIds
            );
            const testDrives = await collections.applications.aggregate(testDrivePipeline).toArray();

            const salesOfferPipeline = getSalesOfferPipeline(
                dealerId,
                week,
                selectedSalesConsultantIds,
                selectedVehicleModelIds
            );
            const salesOffers = await collections.salesOffers.aggregate(salesOfferPipeline).toArray();

            return {
                ...week,
                leadsCreated,
                testDrives: testDrives.length,
                salesOffers: salesOffers.length,
                orderIntakes,
                retails,
                leadToTestDriveRate: getPercentageRound2(testDrives.length, leadsCreated),
                testDriveToSalesOfferRate: getPercentageRound2(salesOffers.length, testDrives.length),
                salesOfferToOrderIntakeRate: getPercentageRound2(orderIntakes, salesOffers.length),
                orderIntakeToRetailRate: getPercentageRound2(retails, orderIntakes),
            };
        })
    );

    return weekFunnels;
};

export const getProgressGoal = async ({
    dealerId,
    salesControlBoardSetting,
    modelAverageTarget,
    hasManagerPermission,
    hasConsultantPermission,
    monthOfImport,
    collections,
    user,
    selectedSalesConsultantIds,
    selectedVehicleModelIds,
    isYtd,
}: {
    dealerId: ObjectId;
    salesControlBoardSetting: SalesControlBoardSetting;
    modelAverageTarget: ModelAverageTarget;
    hasManagerPermission: boolean;
    hasConsultantPermission: boolean;
    monthOfImport: string;
    collections: Collections;
    user: User;
    selectedSalesConsultantIds: ObjectId[];
    selectedVehicleModelIds: ObjectId[];
    isYtd: boolean;
}) => {
    if (hasManagerPermission || !hasConsultantPermission || selectedSalesConsultantIds.length === 0) {
        return undefined;
    }

    const { financeCommissionMonthlyTarget, insuranceCommissionMonthlyTarget } = salesControlBoardSetting;

    const { retailsMonthlyModelAverageTarget } = modelAverageTarget;

    const retailsMonthlyTarget = parseCount(retailsMonthlyModelAverageTarget * selectedVehicleModelIds.length);

    const sumCommissions = await (
        collections.salesControlBoards.aggregate([
            {
                $match: {
                    dealerId,
                    source: SalesControlBoardDataType.Retails,
                    importDate: monthOfImport,
                    modelId: { $in: selectedVehicleModelIds },
                    salesConsultantId: { $in: selectedSalesConsultantIds },
                },
            },
            {
                $group: {
                    _id: '$salesConsultantId',
                    ytdActual: {
                        $sum: '$ytd.actual',
                    },
                    ytdFinanceApplied: {
                        $sum: '$ytd.appliedFinance',
                    },
                    ytdInsuranceApplied: {
                        $sum: '$ytd.appliedInsurance',
                    },
                    monthActual: {
                        $sum: '$month.actual',
                    },
                    monthFinanceApplied: {
                        $sum: '$month.appliedFinance',
                    },
                    monthInsuranceApplied: {
                        $sum: '$month.appliedInsurance',
                    },
                },
            },
        ]) as AggregationCursor<{
            _id: ObjectId;
            monthActual: number;
            ytdActual: number;
            monthFinanceApplied: number;
            ytdFinanceApplied: number;
            monthInsuranceApplied: number;
            ytdInsuranceApplied: number;
        }>
    ).toArray();

    const currentCommission = sumCommissions.find(i => i._id.equals(user._id));

    const months = dayjs(monthOfImport).endOf('month').diff(dayjs(monthOfImport).startOf('year'), 'month') + 1;

    if (!currentCommission) {
        return {
            retailTargetMonth: retailsMonthlyTarget ?? 0,
            retailActualMonth: 0,
            retailMonthRate: 0,
            retailMonthDev: 0 - (retailsMonthlyTarget ?? 0),
            retailTargetYtd: (retailsMonthlyTarget ?? 0) * months,
            retailActualYtd: 0,
            retailYtdRate: 0,
            retailYtdDev: 0 - (retailsMonthlyTarget ?? 0) * months,
            financeTarget: financeCommissionMonthlyTarget,
            financeActualRate: 0,
            insuranceTarget: insuranceCommissionMonthlyTarget,
            insuranceActualRate: 0,
        };
    }

    const financeTargetCountMonth = currentCommission.monthActual * (financeCommissionMonthlyTarget / 100);
    const financeTargetCountYtd = currentCommission.ytdActual * (financeCommissionMonthlyTarget / 100);
    const financeActualRate = (() => {
        if (isYtd) {
            return getPercentage(currentCommission.ytdFinanceApplied, financeTargetCountYtd);
        }

        return getPercentage(currentCommission.monthFinanceApplied, financeTargetCountMonth);
    })();

    const insuranceTargetCountMonth = currentCommission.monthActual * (insuranceCommissionMonthlyTarget / 100);
    const insuranceTargetCountYtd = currentCommission.ytdActual * (insuranceCommissionMonthlyTarget / 100);
    const insuranceActualRate = (() => {
        if (isYtd) {
            return getPercentage(currentCommission.ytdInsuranceApplied, insuranceTargetCountYtd);
        }

        return getPercentage(currentCommission.monthInsuranceApplied, insuranceTargetCountMonth);
    })();

    return {
        retailTargetMonth: retailsMonthlyTarget ?? 0,
        retailActualMonth: currentCommission.monthActual ?? 0,
        retailMonthRate: getPercentage(currentCommission.monthActual ?? 0, retailsMonthlyTarget ?? 0),
        retailMonthDev: (currentCommission.monthActual ?? 0) - (retailsMonthlyTarget ?? 0),
        retailTargetYtd: (retailsMonthlyTarget ?? 0) * months,
        retailActualYtd: currentCommission.ytdActual ?? 0,
        retailYtdRate: getPercentage(currentCommission.ytdActual ?? 0, (retailsMonthlyTarget ?? 0) * months),
        retailYtdDev: (currentCommission.ytdActual ?? 0) - (retailsMonthlyTarget ?? 0) * months,
        financeTarget: financeCommissionMonthlyTarget,
        financeActualRate,
        insuranceTarget: insuranceCommissionMonthlyTarget,
        insuranceActualRate,
    };
};

const getTestDrivePipeline = (
    dealerId: ObjectId,
    convertedPeriod: PeriodPayload,
    selectedSalesConsultantIds: ObjectId[],
    selectedVehicleModelIds: ObjectId[]
) => [
    {
        $match: {
            dealerId,
            'configuration.testDrive': true,
            'appointmentStage.status': ApplicationStatus.Completed,
            'appointmentStage.assigneeId': { $in: selectedSalesConsultantIds },
            '_versioning.isLatest': true,
            $and: [
                { '_versioning.updatedAt': { $gte: convertedPeriod.start } },
                { '_versioning.updatedAt': { $lte: convertedPeriod.end } },
            ],
        },
    },
    {
        $lookup: {
            from: 'vehicles',
            localField: 'vehicleId',
            foreignField: '_id',
            pipeline: [
                {
                    $match: {
                        _kind: VehicleKind.LocalVariant,
                        isDeleted: false,
                        '_versioning.isLatest': true,
                    },
                },
            ],
            as: 'variant',
        },
    },
    { $unwind: { path: '$variant', preserveNullAndEmptyArrays: false } },
    {
        $lookup: {
            from: 'vehicles',
            localField: 'variant.modelId',
            foreignField: '_id',
            pipeline: [
                {
                    $match: {
                        _kind: VehicleKind.LocalModel,
                        parentModelId: { $exists: false },
                        isDeleted: false,
                        '_versioning.isLatest': true,
                    },
                },
            ],
            as: 'model',
        },
    },
    { $unwind: { path: '$model', preserveNullAndEmptyArrays: false } },
    { $match: { 'model._id': { $in: selectedVehicleModelIds } } },
];

const getSalesOfferPipeline = (
    dealerId: ObjectId,
    convertedPeriod: PeriodPayload,
    selectedSalesConsultantIds: ObjectId[],
    selectedVehicleModelIds: ObjectId[]
) => [
    {
        $match: {
            $and: [
                { '_versioning.createdAt': { $gte: convertedPeriod.start } },
                { '_versioning.createdAt': { $lte: convertedPeriod.end } },
            ],
        },
    },
    {
        $lookup: {
            from: 'leads',
            localField: 'leadSuiteId',
            foreignField: '_versioning.suiteId',
            pipeline: [
                {
                    $match: {
                        '_versioning.isLatest': true,
                    },
                },
            ],
            as: 'lead',
        },
    },
    { $unwind: { path: '$lead', preserveNullAndEmptyArrays: false } },
    { $match: { 'lead.dealerId': dealerId, 'lead.assigneeId': { $in: selectedSalesConsultantIds } } },
    {
        $lookup: {
            from: 'vehicles',
            localField: 'vehicle.vehicleId',
            foreignField: '_id',
            pipeline: [
                {
                    $match: {
                        _kind: VehicleKind.LocalVariant,
                        isDeleted: false,
                        '_versioning.isLatest': true,
                    },
                },
            ],
            as: 'variant',
        },
    },
    { $unwind: { path: '$variant', preserveNullAndEmptyArrays: false } },
    {
        $lookup: {
            from: 'vehicles',
            localField: 'variant.modelId',
            foreignField: '_id',
            pipeline: [
                {
                    $match: {
                        _kind: VehicleKind.LocalModel,
                        parentModelId: { $exists: false },
                        isDeleted: false,
                        '_versioning.isLatest': true,
                    },
                },
            ],
            as: 'model',
        },
    },
    { $unwind: { path: '$model', preserveNullAndEmptyArrays: false } },
    { $match: { 'model._id': { $in: selectedVehicleModelIds } } },
];

export const getSalesPerformanceOverview = async ({
    dealerId,
    monthOfImport,
    collections,
    convertedPeriod,
    selectedSalesConsultantIds,
    selectedVehicleModelIds,
    isYtd,
}: {
    dealerId: ObjectId;
    monthOfImport: string;
    collections: Collections;
    convertedPeriod: PeriodPayload;
    selectedSalesConsultantIds: ObjectId[];
    selectedVehicleModelIds: ObjectId[];
    isYtd: boolean;
}) => {
    if (selectedSalesConsultantIds.length === 0) {
        return {
            leadsCreated: 0,
            testDrives: 0,
            salesOffers: 0,
            orderIntakes: 0,
            retails: 0,
        };
    }

    const testDrivePipeline = getTestDrivePipeline(
        dealerId,
        convertedPeriod,
        selectedSalesConsultantIds,
        selectedVehicleModelIds
    );
    const testDrives = await collections.applications.aggregate(testDrivePipeline).toArray();

    const salesOfferPipeline = getSalesOfferPipeline(
        dealerId,
        convertedPeriod,
        selectedSalesConsultantIds,
        selectedVehicleModelIds
    );
    const salesOffers = await collections.salesOffers.aggregate(salesOfferPipeline).toArray();

    const salesControlBoards = await (
        collections.salesControlBoards.aggregate([
            {
                $match: {
                    dealerId,
                    importDate: monthOfImport,
                    modelId: { $in: selectedVehicleModelIds },
                    salesConsultantId: { $in: selectedSalesConsultantIds },
                },
            },
            {
                $group: {
                    _id: '$source',
                    ytdActual: { $sum: '$ytd.actual' },
                    monthActual: { $sum: '$month.actual' },
                },
            },
        ]) as AggregationCursor<{
            _id: SalesControlBoardDataType;
            monthActual: number;
            ytdActual: number;
        }>
    ).toArray();

    const leadsData = salesControlBoards.find(i => i._id === SalesControlBoardDataType.Leads);
    const orderIntakesData = salesControlBoards.find(i => i._id === SalesControlBoardDataType.OrderIntakes);
    const retailData = salesControlBoards.find(i => i._id === SalesControlBoardDataType.Retails);

    return {
        leadsCreated: isYtd ? (leadsData?.ytdActual ?? 0) : (leadsData?.monthActual ?? 0),
        testDrives: testDrives.length,
        salesOffers: salesOffers.length,
        orderIntakes: isYtd ? (orderIntakesData?.ytdActual ?? 0) : (orderIntakesData?.monthActual ?? 0),
        retails: isYtd ? (retailData?.ytdActual ?? 0) : (retailData?.monthActual ?? 0),
    };
};
