import getDatabaseContext from '../../../../database/getDatabaseContext';
import { CampaignPolicyAction } from '../../../../permissions/types/campaigns';
import { requiresLoggedUser } from '../../../middlewares';
import type { GraphQLQueryResolvers } from '../../definitions';

const query: GraphQLQueryResolvers['getActiveCampaigns'] = async (root, { companyId }, { getPermissionController }) => {
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();

    const campaigns = await collections.campaigns
        .find({
            $and: [
                {
                    companyId,
                    isActive: true,
                    isDeleted: false,
                },
                permissionController.campaigns.getFilterQueryForAction(CampaignPolicyAction.View),
            ],
        })
        .toArray();

    return campaigns;
};

export default requiresLoggedUser(query);
