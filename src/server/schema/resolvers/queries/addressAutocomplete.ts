import type { TFunction } from 'i18next';
import {
    AddressAutocompleteProvider,
    AddressAutocompleteServiceFactory,
    defaultAddressAutocompleteService,
} from '../../../integrations/addressAutocomplete/factory';
import { InvalidInput } from '../../errors';
import { buildRateLimiterMiddleware } from '../../middlewares';
import {
    AddressSearchType,
    GraphQLAddressAutocompleteResult,
    GraphQLQueryResolvers,
    type GraphQLAddressAutocompleteInput,
} from '../definitions';

const formatQueryForCountry = (query: string, countryCode?: string): string => {
    if (countryCode === 'JP') {
        const digitsOnly = query.replace(/\D/g, '');
        if (digitsOnly.length >= 4 && query === digitsOnly) {
            return `${digitsOnly.slice(0, 3)}-${digitsOnly.slice(3)}`;
        }
    }

    return query;
};

// Rate limiter for address autocomplete - allow 20 requests per minute per user/IP
const addressAutocompleteRateLimit = buildRateLimiterMiddleware({
    duration: 60, // 1 minute
    points: 20, // 20 requests
    operation: 'address-autocomplete',
});

const getAddressAutocomplete = async (input: GraphQLAddressAutocompleteInput, t: TFunction) => {
    if (!input.query || input.query.trim().length < 2) {
        throw new InvalidInput({
            query: t('errors:addressAutocomplete.invalidQuery'),
        });
    }

    if (input.limit !== undefined && input.limit < 1) {
        throw new InvalidInput({
            limit: t('errors:addressAutocomplete.invalidLimit'),
        });
    }

    if (
        input.proximity &&
        (!Array.isArray(input.proximity) ||
            input.proximity.length !== 2 ||
            !input.proximity.every(coord => typeof coord === 'number' && !Number.isNaN(coord)))
    ) {
        throw new InvalidInput({
            proximity: t('errors:addressAutocomplete.invalidProximity'),
        });
    }

    // Convert GraphQL input to service input format
    const serviceInput = {
        query: formatQueryForCountry(input.query.trim(), input.countryCode),
        country: input.countryCode ? [input.countryCode] : undefined,
        language: input.language || undefined,
        limit: input.limit || 5,
        proximity: input.proximity
            ? {
                  latitude: input.proximity[1],
                  longitude: input.proximity[0],
              }
            : undefined,
        types: input.types || undefined,
    };

    const results = await defaultAddressAutocompleteService.searchAddresses(serviceInput);

    // Convert service results to GraphQL format
    return results.map(result => ({
        id: result.id,
        address: result.address,
        components: result.components,
        coordinates: result.coordinates,
    })) as GraphQLAddressAutocompleteResult[];
};

const getJapanPostalCodeAddressAutocomplete = async (input: GraphQLAddressAutocompleteInput) => {
    if (!input.query || input.query.trim().length !== 7) {
        return []; // Invalid postal code format for Japan
    }

    // Convert GraphQL input to service input format
    const serviceInput = {
        query: input.query.trim(),
    };

    const service = AddressAutocompleteServiceFactory.getService(AddressAutocompleteProvider.JAPAN_POSTAL_CODE);

    const results = await service.searchAddresses(serviceInput);

    // Convert service results to GraphQL format
    return (results || []).map(result => ({
        id: result.id,
        address: result.address,
        components: result.components,
    })) as GraphQLAddressAutocompleteResult[];
};

const query: GraphQLQueryResolvers['getAddressAutocomplete'] = async (_, { input }, { getTranslations }) => {
    const { t } = await getTranslations(['errors']);

    if (input.searchType === AddressSearchType.POSTALCODE && input.countryCode === 'JP') {
        return getJapanPostalCodeAddressAutocomplete(input);
    }

    return getAddressAutocomplete(input, t);
};

export default addressAutocompleteRateLimit(query);
