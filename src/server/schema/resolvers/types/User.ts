import { flatten, uniqWith } from 'lodash/fp';
import { Filter, ObjectId } from 'mongodb';
import * as permissionKind from '../../../../shared/permissions';
import { Phone, Role, UserGroup } from '../../../database/documents';
import getDatabaseContext from '../../../database/getDatabaseContext';
import createLoaders from '../../../loaders';
import {
    AgreementPolicyAction,
    ApplicationPolicyAction,
    BankPolicyAction,
    CampaignPolicyAction,
    CompanyPolicyAction,
    ConfiguratorPolicyAction,
    CustomerPolicyAction,
    DealerPolicyAction,
    EventPolicyAction,
    FinanceProductPolicyAction,
    FinderVehiclePolicyAction,
    GiftVoucherPolicyAction,
    InsuranceProductPolicyAction,
    InsurerPolicyAction,
    InventoryPolicyAction,
    LabelsPolicyAction,
    LanguagePolicyAction,
    LeadPolicyAction,
    MobilityPolicyAction,
    ModulePolicyAction,
    PermissionController,
    PromoCodePolicyAction,
    RolePolicyAction,
    RouterPolicyAction,
    TradeInPolicyAction,
    UserGroupPolicyAction,
    UserPolicyAction,
    WebsitePolicyAction,
} from '../../../permissions';
import { getPermissionsForUser } from '../../../permissions/shared';
import { getPasswordExpirationDate, isPasswordExpiredOn } from '../../../utils/passwords';
import { createPermissionsResolver, GetGeneralPermissionsFnReturnType } from '../../../utils/permissionResolvers';
import { getFriendlyUserAgent } from '../../../utils/userAgent';
import { InvalidPermission } from '../../errors';
import { GraphQLUserResolvers } from '../definitions';

const UserGraphQL: GraphQLUserResolvers = {
    id: root => root._id,

    email: async (root, args, { getUser, getPermissionController }) => {
        const user = await getUser(true);
        const permissionController = await getPermissionController();

        if (
            root._id.equals(user?._id) ||
            permissionController.companies.hasPolicyForAction(CompanyPolicyAction.ManageUsers)
        ) {
            return root.email;
        }

        // return an empty value rather than reject the request
        return '';
    },

    mobile: async (root, args, { getUser, getPermissionController }) => {
        const user = await getUser();
        const permissionController = await getPermissionController();
        const hasManagement = permissionController.companies.hasPolicyForAction(CompanyPolicyAction.ManageUsers);

        if (root._id.equals(user?._id) || hasManagement) {
            return root.mobile;
        }

        const defaultPhone: Phone = {
            value: '',
            prefix: 0,
        };

        return defaultPhone;
    },

    isPasswordExpired: async (root, args, { getUser, getPermissionController }) => {
        const user = await getUser();
        const permissionController = await getPermissionController();
        const hasManagement = permissionController.companies.hasPolicyForAction(CompanyPolicyAction.ManageUsers);

        if (root._id.equals(user?._id) || hasManagement) {
            return isPasswordExpiredOn(root.passwordFrom);
        }

        // return an empty value rather than reject the request
        return false;
    },

    isAuthenticatorEnabled: async (root, args, { getUser, getPermissionController }) => {
        const user = await getUser();
        const permissionController = await getPermissionController();
        const hasManagement = permissionController.companies.hasPolicyForAction(CompanyPolicyAction.ManageUsers);

        if (root._id.equals(user?._id) || hasManagement) {
            return !!root.otpSetup;
        }

        // return an empty value rather than reject the request
        return true;
    },

    passwordExpiresAt: async (root, args, { getUser, getPermissionController }) => {
        const user = await getUser();
        const permissionController = await getPermissionController();
        const hasManagement = permissionController.companies.hasPolicyForAction(CompanyPolicyAction.ManageUsers);

        if (root._id.equals(user?._id) || hasManagement) {
            return getPasswordExpirationDate(root.passwordFrom).toDate();
        }

        // return the current date rather than reject the request
        return new Date();
    },

    webAuthnKeys: async (root, args, { getUser, getPermissionController }) => {
        const user = await getUser();
        const permissionController = await getPermissionController();
        const hasManagement = permissionController.companies.hasPolicyForAction(CompanyPolicyAction.ManageUsers);

        if (!root._id.equals(user?._id) || !hasManagement) {
            // return an empty list for other users
            return [];
        }

        const { collections } = await getDatabaseContext();
        const keys = await collections.userWebCredentials.find({ type: 'fido2', userId: root._id }).toArray();

        return keys.map(key => ({
            id: key._id,
            rawKeyId: key.credentialId,
            userAgent: getFriendlyUserAgent(key.userAgent),
            expiresAt: key.expiresAt,
            lastUsed: key.lastUsed,
        }));
    },

    sessions: async (root, args, { getUser, getPermissionController }) => {
        const user = await getUser();
        const permissionController = await getPermissionController();
        const hasManagement = permissionController.companies.hasPolicyForAction(CompanyPolicyAction.ManageUsers);

        if (!root._id.equals(user?._id) || !hasManagement) {
            // return an empty list for other users
            return [];
        }

        const { collections } = await getDatabaseContext();

        return collections.userSessions.find({ userId: root._id }).toArray();
    },

    permissions: async (root, args, context): Promise<string[]> =>
        createPermissionsResolver(context, async () => {
            const {
                agreements: agreementPermission,
                applications: applicationPermission,
                banks: bankPermission,
                campaigns: campaignPermission,
                companies: companyPermission,
                configurators: configuratorPermission,
                customers: customerPermission,
                dealers: dealerPermission,
                events: eventPermission,
                financeProducts: financeProductPermission,
                finderVehicles: finderVehiclePermission,
                giftVouchers: giftVoucherPermission,
                inventories: inventoryPermission,
                insurers: insurerPermission,
                insuranceProducts: insuranceProductPermission,
                labels: labelPermission,
                languages: languagePermission,
                leads: leadPermission,
                mobilities: mobilityPermission,
                modules: modulePermission,
                promoCodes: promoCodePermission,
                roles: rolePermission,
                routers: routerPermission,
                tradeIns: tradeInPermission,
                users: userPermission,
                userGroups: userGroupPermission,
                websites: webpagePermission,
            } = await context.getPermissionController();
            const user = await context.getUser();

            const hasManagement = companyPermission.hasPolicyForAction(CompanyPolicyAction.ManageUsers);

            if (!root._id.equals(user?._id) && !hasManagement) {
                throw new InvalidPermission();
            }

            /* permissions for agreement management */
            const agreementPermissions = [
                [
                    modulePermission.hasPolicyForAction(ModulePolicyAction.CreateAgreement),
                    permissionKind.createAgreement,
                ],
                [agreementPermission.hasPolicyForAction(AgreementPolicyAction.View), permissionKind.viewAgreements],
            ];

            /* permissions for applications management */
            const applicationPermissions = [
                [
                    applicationPermission.hasPolicyForAction(ApplicationPolicyAction.View),
                    permissionKind.viewApplications,
                ],
                [
                    applicationPermission.hasPolicyForAction(ApplicationPolicyAction.Update),
                    permissionKind.updateApplication,
                ],
            ];

            /* permissions for bank management */
            const bankPermissions = [
                [modulePermission.hasPolicyForAction(ModulePolicyAction.CreateBank), permissionKind.createBank],
                [bankPermission.hasPolicyForAction(BankPolicyAction.View), permissionKind.viewBanks],
            ];

            /* permissions for banner management */
            const bannerPermissions = [
                [
                    configuratorPermission.hasPolicyForAction(ConfiguratorPolicyAction.BannerCreate),
                    permissionKind.createBanner,
                ],
                [
                    configuratorPermission.hasPolicyForAction(ConfiguratorPolicyAction.BannerView),
                    permissionKind.viewBanners,
                ],
                [
                    configuratorPermission.hasPolicyForAction(ConfiguratorPolicyAction.BannerUpdate),
                    permissionKind.updateBanner,
                ],
                [
                    configuratorPermission.hasPolicyForAction(ConfiguratorPolicyAction.BannerDelete),
                    permissionKind.deleteBanner,
                ],
            ];

            /* permissions for company management */
            const companyPermissions = [
                [companyPermission.hasPolicyForAction(CompanyPolicyAction.Create), permissionKind.createCompany],
                [companyPermission.hasPolicyForAction(CompanyPolicyAction.View), permissionKind.viewCompanies],
                [companyPermission.hasPolicyForAction(CompanyPolicyAction.Update), permissionKind.updateCompany],
            ];

            /* permissions for campaign management */
            const campaignPermissions = [
                [campaignPermission.hasPolicyForAction(CampaignPolicyAction.Create), permissionKind.manageCampaigns],
            ];

            /* permissions for configurator settings management */
            const configuratorPermissions = [
                [
                    modulePermission.hasPolicyForAction(ModulePolicyAction.CreateConfigurator),
                    permissionKind.createConfigurator,
                ],
                [
                    configuratorPermission.hasPolicyForAction(ConfiguratorPolicyAction.View),
                    permissionKind.viewConfigurators,
                ],
            ];

            /* permissions for customer management */
            const customerPermissions = [
                [customerPermission.hasPolicyForAction(CustomerPolicyAction.View), permissionKind.viewCustomers],
                [customerPermission.hasPolicyForAction(CustomerPolicyAction.Update), permissionKind.updateCustomer],
            ];

            /* permissions for dealer management */
            const dealerPermissions = [
                [companyPermission.hasPolicyForAction(CompanyPolicyAction.CreateDealer), permissionKind.createDealer],
                [dealerPermission.hasPolicyForAction(DealerPolicyAction.View), permissionKind.viewDealers],
            ];

            /* permissions for event management */
            const eventPermissions = [
                [modulePermission.hasPolicyForAction(ModulePolicyAction.CreateEvent), permissionKind.createEvent],
                [eventPermission.hasPolicyForAction(EventPolicyAction.View), permissionKind.viewEvents],
                [eventPermission.hasPolicyForAction(EventPolicyAction.Update), permissionKind.updateEvent],
                [eventPermission.hasPolicyForAction(EventPolicyAction.Delete), permissionKind.deleteEvent],
            ];

            /* permissions for finance product management */
            const financeProductPermissions = [
                [
                    bankPermission.hasPolicyForAction(BankPolicyAction.CreateFinanceProduct),
                    permissionKind.createFinanceProduct,
                ],
                [
                    financeProductPermission.hasPolicyForAction(FinanceProductPolicyAction.View),
                    permissionKind.viewFinanceProducts,
                ],
            ];

            /* permissions for finder vehicle management */
            const finderVehiclePermissions = [
                [
                    finderVehiclePermission.hasPolicyForAction(FinderVehiclePolicyAction.View),
                    permissionKind.viewFinderVehicles,
                ],
                [
                    finderVehiclePermission.hasPolicyForAction(FinderVehiclePolicyAction.Update),
                    permissionKind.updateFinderVehicle,
                ],
            ];

            /* permissions for gift code management */
            const giftCodePermissions = [
                [
                    giftVoucherPermission.hasPolicyForAction(GiftVoucherPolicyAction.View),
                    permissionKind.viewGiftVouchers,
                ],
            ];

            /* permissions for insurance product management */
            const insuranceProductPermissions = [
                [
                    insurerPermission.hasPolicyForAction(InsurerPolicyAction.CreateInsuranceProduct),
                    permissionKind.createInsuranceProduct,
                ],
                [
                    insuranceProductPermission.hasPolicyForAction(InsuranceProductPolicyAction.View),
                    permissionKind.viewInsuranceProducts,
                ],
            ];
            /* permissions for insurer management */
            const insurerPermissions = [
                [modulePermission.hasPolicyForAction(ModulePolicyAction.CreateInsurance), permissionKind.createInsurer],
            ];

            /* permissions for inventory management */
            const inventoryPermissions = [
                [inventoryPermission.hasPolicyForAction(InventoryPolicyAction.Create), permissionKind.createInventory],
                [inventoryPermission.hasPolicyForAction(InventoryPolicyAction.View), permissionKind.viewInventories],
                [inventoryPermission.hasPolicyForAction(InventoryPolicyAction.Update), permissionKind.updateInventory],
                [inventoryPermission.hasPolicyForAction(InventoryPolicyAction.Delete), permissionKind.deleteInventory],
            ];

            /* permissions for label management */
            const labelPermissions = [
                [labelPermission.hasPolicyForAction(LabelsPolicyAction.Create), permissionKind.createLabel],
                [labelPermission.hasPolicyForAction(LabelsPolicyAction.View), permissionKind.viewLabels],
                [labelPermission.hasPolicyForAction(LabelsPolicyAction.Update), permissionKind.updateLabel],
            ];

            /* permissions for language packs management */
            const languagePacksPermissions = [
                [languagePermission.hasPolicyForAction(LanguagePolicyAction.Create), permissionKind.createLanguagePack],
                [languagePermission.hasPolicyForAction(LanguagePolicyAction.View), permissionKind.viewLanguagePacks],
            ];

            /* permissions for lead management */
            const leadPermissions = [
                [modulePermission.hasPolicyForAction(ModulePolicyAction.CreateContact), permissionKind.createContact],
                [modulePermission.hasPolicyForAction(ModulePolicyAction.CreateLead), permissionKind.createLead],
                [leadPermission.hasPolicyForAction(LeadPolicyAction.View), permissionKind.viewLeads],
                [leadPermission.hasPolicyForAction(LeadPolicyAction.Update), permissionKind.updateLead],
                [leadPermission.hasPolicyForAction(LeadPolicyAction.ViewContact), permissionKind.viewContact],
                [leadPermission.hasPolicyForAction(LeadPolicyAction.UpdateContact), permissionKind.updateContact],
                [leadPermission.hasPolicyForAction(LeadPolicyAction.CreateFollowUp), permissionKind.createFollowUp],
                [leadPermission.hasPolicyForAction(LeadPolicyAction.CreateTestDrive), permissionKind.createTestDrive],
                [
                    leadPermission.hasPolicyForAction(LeadPolicyAction.CreateShowroomVisit),
                    permissionKind.createShowroomVisit,
                ],
            ];

            /* permissions for mobility management */
            const mobilityPermissions = [
                [
                    mobilityPermission.hasPolicyForAction(MobilityPolicyAction.CreateMobility),
                    permissionKind.createMobility,
                ],
                [
                    mobilityPermission.hasPolicyForAction(MobilityPolicyAction.ViewMobility),
                    permissionKind.viewMobilities,
                ],
                [
                    mobilityPermission.hasPolicyForAction(MobilityPolicyAction.UpdateMobility),
                    permissionKind.updateMobility,
                ],
                [
                    mobilityPermission.hasPolicyForAction(MobilityPolicyAction.DeleteMobility),
                    permissionKind.deleteMobility,
                ],
            ];

            /* permissions for module management */
            const modulePermissions = [
                [companyPermission.hasPolicyForAction(CompanyPolicyAction.CreateModule), permissionKind.createModule],
            ];

            /* permissions for promo code management */
            const promoCodePermissions = [
                [
                    modulePermission.hasPolicyForAction(ModulePolicyAction.CreatePromoCode),
                    permissionKind.createPromoCode,
                ],
                [promoCodePermission.hasPolicyForAction(PromoCodePolicyAction.View), permissionKind.viewPromoCodes],
            ];

            /* permissions for role management */
            const rolePermissions = [
                [companyPermission.hasPolicyForAction(CompanyPolicyAction.CreateRole), permissionKind.createRole],
                [rolePermission.hasPolicyForAction(RolePolicyAction.View), permissionKind.viewRoles],
            ];

            /* permissions for routers management */
            const routersPermissions = [
                [companyPermission.hasPolicyForAction(CompanyPolicyAction.CreateRouter), permissionKind.createRouter],
                [routerPermission.hasPolicyForAction(RouterPolicyAction.View), permissionKind.viewRouters],
            ];

            /* permissions for trade in management */
            const tradeInPermissions = [
                [tradeInPermission.hasPolicyForAction(TradeInPolicyAction.View), permissionKind.viewTradeIns],
                [tradeInPermission.hasPolicyForAction(TradeInPolicyAction.Update), permissionKind.updateTradeIn],
            ];

            /* permissions for user management */
            const userPermissions = [
                [userPermission.hasPolicyForAction(UserPolicyAction.Create), permissionKind.createUser],
                [userPermission.hasPolicyForAction(UserPolicyAction.View), permissionKind.viewUsers],
                [userPermission.hasPolicyForAction(UserPolicyAction.Update), permissionKind.updateUser],
                [userPermission.hasPolicyForAction(UserPolicyAction.Delete), permissionKind.deleteUser],
            ];

            /* permissions for user group management */
            const userGroupPermissions = [
                [
                    companyPermission.hasPolicyForAction(CompanyPolicyAction.CreateUserGroup),
                    permissionKind.createUserGroup,
                ],
                [userGroupPermission.hasPolicyForAction(UserGroupPolicyAction.View), permissionKind.viewUserGroups],
                [userGroupPermission.hasPolicyForAction(UserGroupPolicyAction.Update), permissionKind.updateUserGroup],
                [userGroupPermission.hasPolicyForAction(UserGroupPolicyAction.Delete), permissionKind.deleteUserGroup],
            ];

            /* permissions for webpage management */
            const webpagePermissions = [
                [webpagePermission.hasPolicyForAction(WebsitePolicyAction.CreateWebpage), permissionKind.createWebpage],
                [webpagePermission.hasPolicyForAction(WebsitePolicyAction.ViewWebpage), permissionKind.viewWebpages],
                [webpagePermission.hasPolicyForAction(WebsitePolicyAction.UpdateWebpage), permissionKind.updateWebpage],
                [webpagePermission.hasPolicyForAction(WebsitePolicyAction.DeleteWebpage), permissionKind.deleteWebpage],
            ];

            /* permissions for sales control board */
            const salesControlBoardPermissions = [
                [
                    dealerPermission.hasPolicyForAction(DealerPolicyAction.ViewSalesControlBoardManager),
                    permissionKind.viewSalesControlBoardManager,
                ],
                [
                    dealerPermission.hasPolicyForAction(DealerPolicyAction.ViewSalesControlBoardSalesConsultant),
                    permissionKind.viewSalesControlBoardSalesConsultant,
                ],
            ];

            return flatten([
                agreementPermissions,
                applicationPermissions,
                bankPermissions,
                bannerPermissions,
                companyPermissions,
                configuratorPermissions,
                customerPermissions,
                dealerPermissions,
                eventPermissions,
                financeProductPermissions,
                finderVehiclePermissions,
                giftCodePermissions,
                insuranceProductPermissions,
                insurerPermissions,
                inventoryPermissions,
                labelPermissions,
                languagePacksPermissions,
                leadPermissions,
                mobilityPermissions,
                modulePermissions,
                promoCodePermissions,
                rolePermissions,
                routersPermissions,
                tradeInPermissions,
                userPermissions,
                userGroupPermissions,
                webpagePermissions,
                salesControlBoardPermissions,
                campaignPermissions,
            ]) as unknown as GetGeneralPermissionsFnReturnType;
        }),

    permissionWithOrigins: async (root, { filter }) => {
        const loaders = createLoaders();
        const { collections } = await getDatabaseContext();

        const roleFilters: Filter<Role> = {
            userIds: { $in: [root._id] },
        };

        if (filter?.companyIds?.length) {
            roleFilters.companyId = { $in: filter.companyIds };
        }

        const roles = await collections.roles.find(roleFilters).toArray();

        const permissions = await getPermissionsForUser(root, roles, loaders);

        const result = permissions.map(permission => {
            const originRoles = roles.filter(item =>
                item.permissionIds.some(permissionId => permissionId.equals(permission._id))
            );

            return { permission, originRoles };
        });

        return result;
    },

    roles: async (root, { filter }, { getUser, getPermissionController, loaders }): Promise<Role[]> => {
        const user = await getUser();
        const permissionController = await getPermissionController();
        const hasManagement = permissionController.companies.hasPolicyForAction(CompanyPolicyAction.ManageUsers);

        if (root._id.equals(user?._id) || hasManagement) {
            const results = await loaders.rolesByUserId.load(root._id);
            if (filter?.companyIds?.length) {
                const companyIdKeys = filter.companyIds.reduce(
                    (acc, current) => ({
                        ...acc,
                        [current.toHexString()]: true,
                    }),
                    {}
                );

                return results.filter(role => companyIdKeys[role.companyId.toHexString()] ?? false);
            }

            return results;
        }

        return [];
    },

    userGroups: async (root, { filter }, { getUser, getPermissionController, loaders }): Promise<UserGroup[]> => {
        const user = await getUser();
        const permissionController = await getPermissionController();
        const hasManagement = permissionController.companies.hasPolicyForAction(CompanyPolicyAction.ManageUsers);

        if (root._id.equals(user?._id) || hasManagement) {
            const results = await loaders.userGroupsByUserId.load(root._id);
            if (filter?.companyIds?.length) {
                const companyIdKeys = filter.companyIds.reduce(
                    (acc, current) => ({
                        ...acc,
                        [current.toHexString()]: true,
                    }),
                    {}
                );

                return results.filter(userGroup => companyIdKeys[userGroup.companyId.toHexString()] ?? false);
            }

            return loaders.userGroupsByUserId.load(root._id);
        }

        return [];
    },
    versioning: root => root._versioning,
    userCompanyList: async (root, args, { loaders }) => {
        // get user groups and roles of user
        const userGroups = await loaders.userGroupsByUserId.load(root._id);
        const roles = await loaders.rolesByUserId.load(root._id);

        // list the company IDs
        const companyIds = [...userGroups, ...roles].map(item => item.companyId);

        // Check if available companies is not deleted
        const { collections } = await getDatabaseContext();

        const companies = await collections.companies
            .find({
                $and: [{ _id: { $in: companyIds } }, { isDeleted: false }],
            })
            .project({ _id: 1, displayName: 1 })
            .toArray();

        const companiesIds = companies.map(company => company._id);

        // load companies
        return companiesIds.map(companyId => loaders.companyById.load(companyId));
    },
    availableCompanies: async (root, { companyId }, { getUser, getPermissionController, loaders }) => {
        const user = await getUser();
        const permissionController = await getPermissionController();
        const hasManagement = permissionController.companies.hasPolicyForAction(CompanyPolicyAction.ManageUsers);

        if (root._id.equals(user?._id) || hasManagement) {
            // get user groups and roles of user
            const userGroups = await loaders.userGroupsByUserId.load(root._id);
            const roles = await loaders.rolesByUserId.load(root._id);

            // list the company IDs
            const companyIds = [...userGroups, ...roles].reduce<ObjectId[]>(
                (acc, item) => [...acc, item.companyId],
                []
            );

            if (hasManagement) {
                // get all companies that use has manage access
                const companiesWithManageAccess = await getCompaniesWithManageAccess(permissionController);
                companiesWithManageAccess.forEach(companyId => companyIds.push(companyId));
            }

            // make it unique
            const uniqCompanyIds = uniqWith((a, b) => a.equals(b), companyIds);

            // Check if available companies is not deleted
            const { collections } = await getDatabaseContext();

            const companies = await collections.companies
                .find({
                    $and: [{ _id: { $in: uniqCompanyIds } }, { isDeleted: false }],
                })
                .project({ _id: 1, displayName: 1 })
                .toArray();

            const companiesIds = companies
                .map(company => company._id)
                .filter(id => {
                    // filter to only this company id if it is passed
                    if (companyId) {
                        return id.equals(companyId);
                    }

                    return true;
                });

            // load companies
            return companiesIds.map(companyId => loaders.companyById.load(companyId));
        }

        return [];
    },
    isActive: root => root.isActive,
    hasRootPermission: async (root, args, context): Promise<boolean> => {
        const permissionController = await context.getPermissionController();

        return permissionController.hasRootPermission();
    },
};

export default UserGraphQL;

const getCompaniesWithManageAccess = async (permissionController: PermissionController) => {
    const { collections } = await getDatabaseContext();
    const allCompanies = await collections.companies.find({}).toArray();

    return allCompanies.reduce<ObjectId[]>((acc, company) => {
        if (permissionController.companies.mayOperateOn(company, CompanyPolicyAction.CreateDealer)) {
            return [...acc, company._id];
        }

        return acc;
    }, []);
};
