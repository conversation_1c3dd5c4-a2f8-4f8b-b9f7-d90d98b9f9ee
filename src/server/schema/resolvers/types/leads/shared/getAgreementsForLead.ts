import { ConsentsAndDeclarationsType, Lead } from '../../../../../database/documents';
import createLoaders from '../../../../../loaders';

const getAgreementsForLead = async (lead: Lead, loaders = createLoaders()) => {
    if (!lead.customerAgreements) {
        return [];
    }

    const { agreements } = lead.customerAgreements;

    return Promise.all(
        agreements.map(async agreement => {
            const consent = await loaders.consentById.load(agreement.consentId);

            switch (consent._type) {
                case ConsentsAndDeclarationsType.Text:
                case ConsentsAndDeclarationsType.Checkbox:
                    return { ...consent, isAgreed: agreement.isAgreed };

                case ConsentsAndDeclarationsType.Marketing:
                    return { ...consent, isAgreed: agreement.isAgreed, platformsAgreed: agreement.platformsAgreed };

                default:
                    throw new Error('Consent and declaration type is not supported');
            }
        })
    );
};

export default getAgreementsForLead;
