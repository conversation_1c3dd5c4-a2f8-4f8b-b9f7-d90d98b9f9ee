import { isEqual } from 'lodash/fp';
import { ApplicationScenario, ApplicationStage, EmailWithScenarioOverrides } from '../../../database/documents';

const getEmailContentForApplicationStages = <T>(
    emailContent: EmailWithScenarioOverrides<T>,
    applicationStages: ApplicationStage[]
): T => {
    const { overrides } = emailContent;

    const stages = applicationStages.map(stage => {
        if (stage === ApplicationStage.Lead) {
            return ApplicationScenario.LeadCapture;
        }
        if (stage === ApplicationStage.Reservation) {
            return ApplicationScenario.Payment;
        }

        return stage;
    });

    if (overrides && overrides.length > 0) {
        const emailOverride = overrides.find(override => isEqual(override.scenarios.sort(), stages.sort()));

        if (emailOverride?.isActive) {
            return emailOverride;
        }
    }

    return emailContent.defaultValue;
};

export default getEmailContentForApplicationStages;
