import { ObjectId } from 'mongodb';
import { getUrlForUpload } from '../../../core/storage';
import { ConsentsAndDeclarations, LaunchpadLead, SalesOffer, SalesOfferModule } from '../../../database/documents';
import getCompanyFontInBase64 from '../../../journeys/helper/getCompanyFontInBase64';
import getSigningProvider from '../../../journeys/helper/getSigningProvider';
import { Loaders } from '../../../loaders';
import { renderSalesOfferSpecificationPdf } from '../../../pdf';
import { salesOfferPDFRenderOptions } from '../../../pdf/shared';
import createI18nInstance from '../../../utils/createI18nInstance';
import ensureManyFromLoaders from '../../../utils/ensureManyFromLoaders';

type GenerateSpecificationDocumentParams = {
    languageId: ObjectId;
    salesOffer: SalesOffer;
    lead: LaunchpadLead;
    loaders: Loaders;
};
const generateSpecificationDocument = async ({
    languageId,
    salesOffer,
    lead,
    loaders,
}: GenerateSpecificationDocumentParams) => {
    // load translations
    const { i18n } = await createI18nInstance(languageId?.toHexString() ?? null);

    await i18n.loadNamespaces(['common', 'saleOfferPdf', 'applicationPdf']);

    const salesOfferModule = (await loaders.moduleById.load(salesOffer.moduleId)) as SalesOfferModule;
    const signingModule = await loaders.moduleById.load(salesOfferModule.signingModuleId);
    const signingProvider = getSigningProvider(signingModule);

    const company = await loaders.companyById.load(salesOfferModule.companyId);

    // get C&D document
    const vehicleConsent = salesOffer.consents?.specifications.agreements ?? [];
    const agreedConsentIds = vehicleConsent.map(agreement => agreement.consentId);
    const consentAndDeclaration = await loaders.consentById
        .loadMany(agreedConsentIds)
        .then(ensureManyFromLoaders<ConsentsAndDeclarations>);

    return renderSalesOfferSpecificationPdf(
        {
            companyLogo: company.logo ? await getUrlForUpload(company.logo) : null,
            agreements: vehicleConsent.map(agreement => ({
                ...agreement,
                consent: consentAndDeclaration.find(consent => consent._id.equals(agreement.consentId)),
            })),
            signingProvider,
            fontUrlInBase64: await getCompanyFontInBase64(company),
            pdsRenderOptions: salesOfferPDFRenderOptions,
        },
        i18n
    );
};

export default generateSpecificationDocument;
