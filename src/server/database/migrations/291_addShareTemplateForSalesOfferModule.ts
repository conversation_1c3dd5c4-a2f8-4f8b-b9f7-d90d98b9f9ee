import {
    defaultShareIntroTitle,
    defaultShareSubject,
    defaultShareContentText,
} from '../../schema/resolvers/mutations/modules/createSalesOfferModule';
import { ModuleType, SalesOfferEmailContents, SalesOfferModule } from '../documents';
import { DatabaseContext } from '../getDatabaseContext';

type OldSalesOfferModule = Omit<SalesOfferModule, 'emailContents'> & {
    emailContents: {
        combinedTemplate: SalesOfferEmailContents;
        singlePreOfferTemplate: SalesOfferEmailContents;
        salesOfferTemplate: SalesOfferEmailContents;
    };
};

export default {
    identifier: '291_addShareTemplateForSalesOfferModule',

    async up({ regular: { db } }: DatabaseContext): Promise<void> {
        await db.collection<OldSalesOfferModule>('modules').updateMany(
            { _type: ModuleType.SalesOfferModule },
            {
                $set: {
                    'emailContents.shareTemplate': {
                        introTitle: defaultShareIntroTitle,
                        subject: defaultShareSubject,
                        contentText: defaultShareContentText,
                    },
                },
            }
        );
    },
};
