/* eslint-disable max-len */
import type { DatabaseContext } from '../getDatabaseContext';
import initialMigrationsIndexes from './01_initialMigrationsIndexes';
import initialUsersIndexes from './02_initialUsersIndexes';
import addDefaultLocaleSetting from './03_addDefaultLocaleSetting';
import initialUser from './03_initialUser';
import initialExternalLinkIndexes from './04_initialExternalLinkIndexes';
import initialUserSessionIndexes from './05_initialUserSessionIndexes';
import initialUserWebCredentialsIndexes from './06_initialUserWebCredentialsIndexes';
import addSingleSessionMode from './07_addSingleSessionMode';
import updateRouters from './08_updateRouters';
import updateRoutersPart2 from './09_updateRoutersPart2';
import addInitialWithInsurance from './100_addInitialWithInsurance';
import cleanUpConfigurators from './100_cleanUpConfigurators';
import insertRequiredConfigurationFieldForEvent from './100_insertRequiredConfigurationFieldForEvent';
import renameFinanceSuiteIdsToReference from './101_renameFinanceSuiteIdsToReference';
import updateBankPermissions from './101_updateBankPermissions';
import resolveFinanceSuiteIdsToReference from './102_resolveFinanceSuiteIdsToReference';
import updateAuditTrailRecipient from './102_updateAuditTrailRecipient';
import updateBmwRelatedSettings from './102_updateBmwRelatedSettings';
import updateExistingKYCPresets from './102_updateExistingKYCPresets';
import addBankHasUploadDocuments from './103_addBankHasUploadDocuments';
import addShowCalculatorToApplicationModules from './103_addShowCalculatorToApplicationModules';
import changeInsuranceDisclaimerToTranslatedString from './103_changeInsuranceDisclaimerToTranslatedString';
import updateWebpageModulePermissions from './103_updateWebpageModulePermissions';
import addAllowedIPsOnBankSettings from './104_addAllowedIPsOnBankSettings';
import resolveMayApplyForInsurance from './104_resolveMayApplyForInsurance';
import addStatisticIndexes from './105_addStatisticIndexes';
import updateDeterministicNumberKey from './106_updateDeterministicNumberKey';
import updateStringDescriptionKey from './107_updateStringDescriptionKey';
import addMissingReservationStatus from './108_addMissingReservationStatus';
import addMissingReservationStatusPorsche from './109_addMissingReservationStatusPorsche';
import resolveUnitInBalloonSetting from './109_resolveUnitInBalloonSetting';
import addLanguagesOnRouters from './10_addLanguagesOnRouters';
import addBookingScenarioToAllMobilityModules from './110_addBookingScenarioToAllMobilityModules';
import renameFinderApplicationEntrypoint from './110_renameFinderApplicationEntrypoint';
import updateInsurancePermission from './110_updateInsurancePermission';
import updateMobilityModuleRentalRequirement from './110_updateMobilityModuleRentalRequirement';
import addPersistKYCDataToMobilityModule from './111_addPersistKYCDataToMobilityModule';
import generateDefaultInsuranceProducts from './111_generateDefaultInsuranceProducts';
import addMobilityInventoryBlockingDates from './112_addMobilityInventoryBlockingDates';
import updateMobilityModuleRentalRequirementAndDisclaimer from './112_updateMobilityModuleRentalRequirementAndDisclaimer';
import addDealerMobilityBookingCode from './113_addDealerMobilityBookingCode';
import createInitialInventoryAuditTrails from './113_createInitialInventoryAuditTrails';
import updateMobilityStock from './113_updateMobilityStock';
import addSigningInMobility from './114_addSigningInMobility';
import resolvePermissionOnFinderVehicle from './115_resolvePermissionOnFinderVehicle';
import resolveListFinderPermission from './116_resolveListFinderPermission';
import resolveUserGroupsMissingCompanyId from './117_resolveUserGroupsMissingCompanyId';
import updateAppointmentIdentifiers from './117_updateAppointmentIdentifiers';
import updateFinderApplicationEndpoints from './117_updateFinderApplicationEndpoints';
import reassignManageFinderPermission from './118_reassignManageFinderPermission';
import updateApplicationModulePermission from './118_updateApplicationModulePermission';
import triggerAppointmentAndMaintenancePermission from './119_triggerAppointmentAndMaintenancePermission';
import addDefaultValuesOnCompanies from './11_addDefaultValuesOnCompanies';
import updateSettingsIndexes from './11_updateSettingsIndexes';
import moveCtsConfigurationToSetting from './120_moveCtsConfigurationToSetting';
import updateApplicationPermissionRecalculate from './120_updateApplicationPermissionRecalculate';
import triggerCompanyUserPermission from './121_triggerCompanyUserPermission';
import retriggerCompanyUserPermission from './122_retriggerCompanyUserPermission';
import updateApplicationModulePermissions from './123_updateApplicationModulePermissions';
import addDealerOptions from './124_addDealerOptions';
import addDealerVehiclesToEvents from './124_addDealerVehiclesToEvent';
import updateFinanceProductInterestRate from './124_updateFinanceProductInterestRate';
import updateUserIndexes from './124_updateUserIndexes';
import updateExistingAppointmentStatus from './125_updateExistingAppointmentStatus';
import triggerApplicationPermission from './126_triggerApplicationPermission';
import updateMissingInventoriesIdentifier from './126_updateMissingInventoriesIdentifier';
import addNewFieldsToInterestRateTable from './127_addNewFieldsToInterestRateTable';
import updateFinderPrivateApplicationEndpoints from './127_updateFinderPrivateApplicationEndpoints';
import updateMobilityModuleAvailableRange from './127_updateMobilityModuleAvailableRange';
import deleteAndSyncFinanceProducts from './128_deleteAndSyncFinanceProducts';
import updateInventoryPermission from './129_updateInventoryPermission';
import addTranslationsOnCompanies from './12_addTranslationsOnCompanies';
import updateDefaultValuesOnUsers from './12_updateDefaultValuesOnUsers';
import updateBankAuditTrails from './130_updateBankAuditTrails';
import addVehicleParametersInFinanceProduct from './131_addVehicleParametersInFinanceProduct';
import regeneratePermissions from './131_regeneratePermissions';
import addShowResetKYCButtonToModule from './132_addShowResetKYCButtonToModule';
import changeCarConditionInFinderModule from './132_changeCarConditionInFinderModule';
import defaultBankFinancingDisclaimer from './132_defaultBankFinancingDisclaimer';
import addLiveChatSettingId from './133_addLiveChatSettingId';
import migrateMyInfoSettings from './133_migrateMyInfoSettings';
import updateInsuranceDisclaimer from './133_updateInsuranceDisclaimer';
import updateAppointmentCheckInStatus from './134_updateAppointmentCheckinStatus';
import updateMobilityModuleEmailContentEmailsEnabled from './134_updateMobilityModuleEmailContentEmailsEnabled';
import updateTestDriveSettingForAppointment from './135_updateTestDriveSettingForAppointment';
import addApplicationModulesToPromoCode from './136_addApplicationModulesToPromoCode';
import addAtlasAdviserIndexes from './137_addAtlasAdviserIndexes';
import addReservationInstructionsToFinderModule from './137_addReservationInstructionsToFinderModule';
import addReservationPeriodToFinderModule from './137_addReservationPeriodToFinderModule';
import addSendPDFEmailToCustomerToBankInsurerMobility from './137_addSendPDFEmailToCustomerToBankInsurerMobility';
import addVehicleParameterFilterTable from './137_addVehicleParameterFilterTable';
import addReminderEmailToFinderEmailContents from './138_addReminderEmailToFinderEmailContents';
import updateFinderModuleShowFinanceCalculator from './138_updateFinderModuleShowFinanceCalculator';
import updateFinderModuleShowVisitModelPage from './139_updateFinderModuleShowVisitModelPage';
import createPermissionIndexes from './13_createPermissionIndexes';
import addEndpointApiVersionPorschePaymentSetting from './140_addEndpointApiVersionPorschePaymentSetting';
import realignModulePermissionSystemName from './140_realignModulePermissionSystemName';
import regeneratePermissions16Nov from './141_regeneratePermissions';
import deleteRemovedPermissions from './142_deleteRemovedPermissions';
import cleanupRemovedPermissions from './143_cleanupRemovedPermissions';
import regeneratePermissionsForDealer from './144_regeneratePermissionsForDealer';
import reassignDealerPermission from './145_reassignDealerPermission';
import updateStageAssignees from './145_updateStageAssignees';
import addKycPresetInEvent from './146_addKycPresetInEvent';
import adjustUseMyinfoInApplication from './146_adjustUseMyinfoInApplication';
import regenerateDealerPermission from './146_regenerateDealerPermission';
import addIncludeDealerOptionsForFinancing from './147_addIncludeDealerOptionsForFinancing';
import updateKycFieldsInCustomerModule from './147_updateKycFieldsInCustomerModule';
import updateVariantYear from './148_updateVariantYear';
import addDefaultShowroomSalesShare from './149_addDefaultShowroomSalesShare';
import renameFinderApplicationEntities from './149_renameFinderApplicationEntities';
import addPermissionOrigins from './14_addPermissionOrigins';
import addAdditionalConsentsFeaturePurpose from './150_addAdditionalConsentsFeaturePurpose';
import addCompanyNameInEnbdBank from './150_addCompanyNameInEnbdBank';
import addTradeInVehiclesForKYCFields from './150_addTradeInVehiclesForKYCFields';
import removeFinderEntrypoints from './150_removeFinderEntrypoints';
import addEventCustomizedFields from './151_addEventCustomizedFields';
import generateDealerViewManagementPermission from './152_generateDealerViewManagementPermission';
import addMobilityLocationId from './153_addMobilityLocationId';
import changeFinancingOptionalModule from './153_changeFinancingOptionalModule';
import renameLeadStageStatus from './153_renameLeadStageStatus';
import sortSavedKycFields from './153_sortSavedKycFields';
import addLocationEmailContentMobility from './154_addLocationEmailContentMobility';
import renameMobilityStageStatus from './154_renameMobilityStageStatus';
import addDrivingLicenseThForKYCFields from './155_addDrivingLicenseThForKYCFields';
import removeSpecificKycPresetInEvent from './155_removeSpecificKycPresetInEvent';
import updateReservationStatuses from './155_updateReservationStatuses';
import refactorEmailContentsMobility from './156_refactorEmailContentsMobility';
import updateMobilityHomeDelivery from './156_updateMobilityHomeDelivery';
import updateWebsiteModule from './156_updateWebsiteModule';
import updateFlexibleDiscountModule from './157_updateFlexibleDiscountModule';
import updateMobilityOptionsPrice from './157_updateMobilityOptionsPrice';
import addTranslationsOnConfiguratorApplication from './158_addTranslationsOnConfiguratorApplication';
import addTranslationsOnConfiguratorModel from './158_addTranslationsOnConfiguratorModel';
import addTranslationsOnConfiguratorVariants from './158_addTranslationsOnConfiguratorVariants';
import addTranslationsOnInventory from './158_addTranslationsOnInventory';
import changeBannerTextToTranslatedStringForBanner from './158_changeBannerTextToTranslatedStringForBanner';
import fixConfiguratorOptionsValueTextNullData from './158_fixConfiguratorOptionsValueTextNullData';
import resolveEmailContentMobility from './158_resolveEmailContentMobility';
import updateBmwModulePermissions from './158_updateBmwModulePermissions';
import addCOEForCompanyAndDealer from './159_addCOEForCompanyAndDealer';
import addPorscheApprovedInfoForFinderModule from './159_addPorscheApprovedInfoForFinderModule';
import updateCTSSettingFinderModuleIds from './159_updateCTSSettingFinderModuleIds';
import addDefaultEmailContextSetting from './15_addDefaultEmailContextSetting';
import addTempApplicationDocumentIndex from './160_addTempApplicationDocumentIndex';
import removeFinancingFromEventModule from './160_removeFinancingFromEventModule';
import renameSdmBankToExternalBank from './160_renameSdmBankToExternalBank';
import removeEventModuleFromPromoCode from './161_removeEventModuleFromPromoCode';
import renameSdmCodeToSalesPersonCode from './161_renameSdmCodeToSalesPersonCode';
import updateFuelConsumption from './161_updateFuelConsumption';
import addDataPurgeInCompany from './162_addDataPurgeInCompany';
import cleanUpMobilityEmailContent from './162_cleanUpMobilityEmailContent';
import addDrivingLicenseMyForKYCFields from './163_addDrivingLicenseMyForKYCFields';
import addIsGenerateStepsInTableForFinanceProduct from './163_addIsGenerateStepsInTableForFinanceProduct';
import renameSdmModuleIdToFinancingModuleId from './163_renameSdmModuleIdToFinancingModuleId';
import updateCTSSettingActive from './163_updateCTSSettingActive';
import updateFinanceProductFinderVehicleCondition from './163_updateFinanceProductFinderVehicleCondition';
import removeInvalidRouterMenuItems from './164_removeInvalidRouterMenuItems';
import addSalesNameToBmwApplications from './165_addSalesNameToBmwApplications';
import updateEventPermissions from './165_updateEventPermissions';
import addApprovalApplicationPermission from './166_addApprovalApplicationPermission';
import addFeildsToBmwApplications from './166_addFieldsInBmwApplications';
import addNZFieldForCompanyAndDealer from './166_addNZFieldForCompanyAndDealer';
import addSkipForDeposit from './166_addSkipForDeposit';
import updateEventApplicationModuleEmailContent from './166_updateEventApplicationModuleEmailContent';
import addHasTestDriveProceedAppointmentModule from './167_addHasTestDriveProceedAppointmentModule';
import removeApplyDefaultKycInEvent from './167_removeApplyDefaultKycInEvent';
import renameVehicleOnBmwApplication from './167_renameVehicleOnBmwApplication';
import updateConfiguratorApplicationModuleEmailContent from './167_updateConfiguratorApplicationModuleEmailContent';
import updateFinderApplicationPrivateModuleEmailContent from './168_updateFinderApplicationPrivateModuleEmailContent';
import updateInsuranceApplicationPermissions from './168_updateInsuranceApplicationPermissions';
import addAudiFinancingPermissions from './169_addAudiFinancingPermissions';
import addSuiteIdOnProceedWithLink from './169_addSuiteIdOnProceedWithLink';
import updateFinderApplicationPublicModuleEmailContent from './169_updateFinderApplicationPublicModuleEmailContent';
import initialCounterIndexes from './16_initialCounterIndexes';
import addIsAddOnSpecificVehiclesForFinanceProductVRP from './170_addIsAddOnSpecificVehiclesForFinanceProductVRP';
import addJobTitleThForKYCFields from './170_addJobTitleThForKYCFields';
import updateFinderVehicleSettingIsTestDriveButtonHidden from './170_updateFinderVehicleSettingIsTestDriveButtonHidden';
import updateMobilityApplicationModuleEmailContent from './170_updateMobilityApplicationModuleEmailContent';
import rearrangeSavedKycFields from './171_sortSavedKycFields';
import setProLayoutOnRouter from './172_setProLayoutOnRouter';
import updateEventWithDealerPermissions from './173_updateEventWithDealerPermissions';
import updateLayoutsToProOnRouter from './173_updateLayoutsToProOnRouter';
import addAppointmentEmailContent from './174_addAppointmentEmailContent';
import addIsLTAUploaded from './175_addIsLTAUploaded';
import removeRolePermissions from './175_removeRolePermissions';
import removeUserGroupPermissions from './176_removeUserGroupPermissions';
import upsertCompanyPermissions from './177_upsertCompanyPermissions';
import addRegionToKycField from './178_addRegionToKycField';
import addSkipDepositOnFinderPublicModule from './178_addSkipDepositOnFinderPublicModule';
import updateDefaultAppointmentEmailContent from './178_updateDefaultAppointmentEmailContent';
import upsertEventPermissions from './178_upsertEventPermissions';
import addLastNameJapanAndFirstNameJapanKYCFields from './179_addLastNameJapanAndFirstNameJapanKYCFields';
import sortKycFieldAfterRegion from './179_sortKycFieldAfterRegion';
import addConditionsOnKYC from './17_addConditionsOnKYC';
import addVehicleContractEndKYCFields from './180_addVehicleContractEndKYCFields';
import updateCompanyPermissions from './181_updateCompanyPermissions';
import addBankIdsToModules from './182_addBankIdsToModules';
import addFinanceProductIdToAudiFinancing from './182_addFinanceProductIdToAudiFinancing';
import addTestDriveInitialSettingInJourney from './183_addTestDriveInitialSettingInJourney';
import resetExpireOnAvailableFinder from './183_resetExpireOnAvailableFinder';
import renameAppointmentEmailContent from './184_renameAppointmentEmailContent';
import renameCheckinCheckout from './184_renameCheckinCheckout';
import resolveApplicationStages from './185_resolveApplicationStages';
import customerIndex from './186_customerIndex';
import removeApplyPropertyFromModule from './186_removeApplyPropertyFromModule';
import addGiftVoucherPermissions from './187_addGiftVoucherPermissions';
import updateMobilitySubmissionAuditTrail from './187_updateMobilitySubmissionAuditTrail';
import updateAudiFinancingNullDocuments from './188_updateAudiFinancingNullDocuments';
import updateBankApplicationAccess from './189_updateBankApplicationAccess';
import addVersioningOnConsents from './18_addVersioningOnConsents';
import addBankEstFeeToNzMarket from './190_addBankEstFeeToNzMarket';
import addIdentifierToBank from './190_addIdentifierToBank';
import addBankModuleIdToAudiFinancingModule from './191_addBankModuleIdToAudiFinancingModule';
import addOrderInKycFieldsCustomerModule from './192_addOrderInKycFieldsCustomerModule';
import addVersioningOnTradeInCollection from './192_addVersioningOnTradeInCollection';
import addIdentifierToDeletedBank from './193_addIdentifierToDeletedBank';
import addShowDealershipToEvent from './193_addShowDealershipToEvent';
import addTradeInPermissions from './193_addTradeInPermissions';
import updateHasLegalMarkup from './194_updateHasLegalMarkup';
import addLeadsAndCustomerPermissions from './195_addLeadsAndCustomerPermissions';
import updateMissingLocalCustomerFieldKey from './195_updateMissingLocalCustomerFieldKey';
import addEventLevelCustomiseContentsDefaultValue from './196_addEventLevelCustomiseContentsDefaultValue';
import updatePermissions from './197_updatePermissions';
import addCompanyViewFromOptions from './198_addCompanyViewFromOptions';
import rearrangeVariantForDeletedSubmodel from './198_rearrangeVariantForDeletedSubmodel';
import addTradeInStatus from './199_addTradeInStatus';
import updateConsentsAndKycPresets from './199_updateConsentsAndKycPresets';
import addPurposeOnConsentsAndDeclaration from './19_addPurposeOnConsentsAndDeclaration';
import removeEndpointWithDeletedModule from './200_removeEndpointWithDeletedModule';
import updateConfiguratorApplicationModuleTerm from './201_updateConfiguratorApplicationModuleTerm';
import updateGiftVoucherConsents from './201_updateGiftVoucherConsents';
import addCustomerPermissionsToAllRoles from './202_addCustomerPermissionsToAllRoles';
import updateSdmAuthorToSalesforce from './202_updateSdmAuthorToSalesforce';
import removePermissions from './203_removePermissions';
import updateApplicationPermissions from './203_updateApplicationPermissions';
import addShowRemoteFlowAppointmentModule from './204_addShowRemoteFlowAppointmentModule';
import regenerateSimplifyPermissions from './204_regenerateSimplifyPermissions';
import populateReferenceNameLanguagePacks from './205_populateReferenceNameLanguagePacks';
import updateAgreementPermissions from './206_updateAgreementPermissions';
import addNewAppointmentEmailContents from './207_addNewAppointmentEmailContents';
import generateModulePermissions from './207_generateModulePermissions';
import addIsDeletedToDealers from './208_addIsDeletedToDealers';
import updateHlfReassignPartial from './208_updateHlfReassignPartial';
import addAllowVariantCodeMappingToBank from './209_addAllowVariantCodeMappingToBank';
import updateDubaiMarketplaceId from './209_updateDubaiMarketplaceId';
import addScenarioAndCounterPadding from './20_addScenarioAndCounterPadding';
import addBankCodeMappingToVariant from './210_addBankCodeMappingToVariant';
import removeIdentifierFromBank from './211_removeIdentifierFromBank';
import updateNamirialSettings from './211_updateNamirialSettings';
import addBankReferenceIdentifier from './212_addBankReferenceIdentifier';
import moveDealerCodeToBankIntegrationSetting from './212_moveDealerCodeToBankIntegrationSetting';
import updateModuleInDealerUpdateAccess from './212_updateModuleInDealerUpdateAccess';
import updateRouterPathScripts from './212_updateRouterPathScripts';
import addCommentsToBankAndInsurer from './213_addCommentsToBankAndInsurer';
import updateUnremovedPermissions from './214_updateUnremovedPermissions';
import fixHlfSubmissionTypo from './215_fixHlfSubmissionTypo';
import addDbsPayloadScheme from './216_addDbsPayloadScheme';
import addInstantApprovalStatsFlagToCompany from './216_addInstantApprovalStatsFlagToCompany';
import addTypeToInsuranceProducts from './217_addTypeToInsuranceProducts';
import updateApplicationInsurancing from './218_updateApplicationInsurancing';
import insurerInsuranceProductType from './219_insurerInsuranceProductType';
import addMenuItemsOnRouters from './21_addMenuItemsOnRouters';
import renameVariantIdsOnFinanceProduct from './21_renameVariantIdsOnFinanceProduct';
import addVehicleReferenceParametersToInsuranceProduct from './220_addVehicleReferenceParametersToInsuranceProduct';
import addDealerInsuranceProductsToModules from './221_addDealerInsuranceProductsToModules';
import addCurrentVehicleFieldsToModule from './222_addCurrentVehicleFieldToModule';
import addErgoLookupTableToErgoInsuranceProducts from './222_addErgoLookupTableToErgoInsuranceProducts';
import removeDuplicatedAuditTrails from './222_removeDuplicatedAuditTrails';
import removeNonPorscheModules from './222_removeNonPorscheModules';
import addCurrentAnotherVehicleFieldToModule from './223_addCurrentVehicleFieldToModule';
import removeTradeInVehicleFieldsonEventKycPreset from './223_removeTradeInVehicleFieldsonEventKycPreset';
import migrateVehicleContractEndKyc from './224_migrateVehicleContractEndKyc';
import updateAppointmentModuleEmailContent from './225_updateAppointmentModuleEmailContent';
import updateAppointmentModuleAddEndTestDriveReminder from './226_updateAppointmentModuleAddEndTestDriveReminder';
import addVehicleFilterParameterTableToFinanceProductWithTable from './227_addVehicleFilterParameterTableToFinanceProductWithTable';
import updateAppointmentModuleEmailContentToNewStructure from './227_updateAppointmentModuleEmailContentToNewStructure';
import updateStandardApplicationModuleAddEmailContents from './228_updateStandardApplicationModuleAddEmailContents';
import addCampaignSettingsToEvent from './229_addCampaignSettingsToEvent';
import separateCapIntegrationStatusFromApplicationStatus from './229_separateCapIntegrationStatusFromApplicationStatus';
import updateCustomerShareAndComparisonShareEmailContents from './229_updateCustomerShareAndComparisonShareEmailContents';
import renameApplicationStatus from './22_renameApplicationStatus';
import renameVariantIdsOnLeaseTable from './22_renameVariantIdsOnLeaseTable';
import changeQualifyAndUnqualifyAuditTrailKind from './230_changeQualifyAndUnqualifyAuditTrailKind';
import updateFinderAppModuleEmailContents from './230_updateFinderAppModuleEmailContents';
import initialValueForVariantImageReminderMail from './231_initialValueForVariantImageReminderMail';
import setUseDisclaimersInFinderModules from './231_setUseDisclaimersInFinderModules';
import updateReminderEndTestDriveApplicationId from './231_updateReminderEndTestDriveApplicationId';
import updateEventApplicationModuleEmailContents from './232_updateEventApplicationModuleEmailContents';
import addDisplayAppointmentDatepickerInEvent from './233_addDisplayAppointmentDatepickerInEvent';
import updateCapSetting from './233_updateCapSetting';
import updateShowroomEmailContentIntroImage from './233_updateShowroomEmailContentIntroImage';
import updateApplicationQuotationAmount from './234_updateApplicationQuotationAmount';
import updateStandardApplicationModuleAddSalesPersonCancelledEmail from './234_updateStandardApplicationModuleAddSalesPersonCancelledEmail';
import addDealerSpecificPaymentSettingToStandardModule from './235_addDealerSpecificPaymentSettingToStandardModule';
import addListingUrlFinderEndpoint from './235_addListingUrlFinderEndpoint';
import updateLeadGenFormDepositAmount from './235_updateLeadGenFormDepositAmount';
import addIsActiveToDealershipPaymentSetting from './236_addIsActiveToDealershipPaymentSetting';
import addDealerSpecificDepositAmountToModule from './237_addDealerSpecificDepositAmountToModule';
import updatePermissionUpdate from './238_updatePermissionUpdate';
import uploadPorscheFontToS3 from './238_uploadPorscheFontToS3';
import addMissingLeadStageToEventApplication from './239_addMissingLeadStageToEventApplication';
import updateCounterIndexes from './23_updateCounterIndexes';
import restructureEventUtmParameters from './240_restructureEventUtmParameters';
import updateJourneyIsImmutableForDeclined from './241_updateJourneyIsImmutableForDeclined';
import addApplicationIndexes from './242_addApplicationIndexes';
import addRouterPathScripts from './243_addRouterPathScripts';
import migrateMyInfoV5Settings from './243_migrateMyInfoV5Settings';
import addEventDisplayVisitAppointment from './244_addEventDisplayVisitAppointment';
import addIndexToLeadId from './244_addIndexToLeadId';
import addMobilityApplicationBookingConfirmationEmail from './244_addMobilityApplicationBookingConfirmationEmail';
import splitLeadStageFromApplications from './244_splitLeadStageFromApplications';
import updateDealerVehiclesLaunchPadModule from './244_updateDealerVehiclesLaunchPadModule';
import dropLeadCaptureScenario from './245_dropLeadCaptureScenario';
import updateConfigurationForEventApplication from './245_updateConfigurationForEventApplication';
import addMarketingDashboardPermission from './246_addMarketingDashboardPermission';
import addUserIdsToEvent from './246_addUserIdsToEvent';
import addUtmURLLeadCaptureForm from './246_addUtmURLLeadCaptureForm';
import splitApplicationAndLeadPermissions from './246_splitApplicationAndLeadPermission';
import addCompanyCalculationRounding from './247_addCompanyCalculationRounding';
import addHasVehicleIntegrationToEvent from './247_addHasVehicleIntegrationToEvent';
import initialTradeInForLead from './247_initialTradeInForLead';
import migrateApplicationAgreementToLead from './247_migrateApplicationAgreementToLead';
import migrateViewLeadPermissions from './247_migrateViewLeadPermissions';
import addLaunchpadPermissionOnLead from './248_addLaunchpadPermissionOnLead';
import reSplitLeadStageFromApplications from './248_reSplitLeadStageFromApplications';
import switchCapSetting from './248_switchCapSetting';
import addContactPermissionOnLead from './249_addContactPermissionOnLead';
import migrateCapStatusAuditTrail from './249_migrateCapStatusAuditTrail';
import addDraftAndStageOnApplications from './24_addDraftAndStageOnApplications';
import updateSessionTimeout from './24_updateSessionTimeout';
import reInitialTradeInForLead from './250_reInitialTradeInForLead';
import addRequestPermissionForLaunchpad from './251_addRequestPermissionForLaunchpad';
import applicationToLeadEndpoint from './252_applicationToLeadEndpoint';
import addStageToLeadListEndpoint from './253_addStageToLeadListEndpoint';
import migrateApplicationDocumentToLead from './254_migrateApplicationDocumentToLead';
import updateLeadPermission from './254_updateLeadPermission';
import updateLeadPermissions from './255_updateLeadPermission';
import addDraftOnLeads from './256_addDraftOnLeads';
import addIsLeadIntoLead from './256_addIsLeadIntoLead';
import copyCampaignValuesAndEventIdFromApplicationToLead from './256_copyCampaignValuesAndEventIdFromApplicationToLead';
import migrateOtherApplicationKindsToLead from './257_migrateOtherApplicationKindsToLead';
import updateLeadStatuses from './258_updateLeadStatuses';
import restructureLeadPermissions from './259_restructureLeadPermissions';
import updateCompanySmsSettings from './25_updateCompanySmsSettings';
import addCustomizedFieldToLeads from './260_addCustomizedFieldToLeads';
import updateLeadSubmissionFailStatus from './260_updateLeadSubmissionFailStatus';
import migrateLeadAuditTrail from './261_migrateLeadAuditTrail';
import simplifyCampaignId from './262_simplifyCampaignId';
import renameLeadStatus from './263_renameLeadStatus';
import updateIsLeadLogic from './264_updateIsLeadLogic';
import changeApplicationsOfLeadOnlyToDraft from './265_changeApplicationsOfLeadOnlyToDraft';
import addIdentifierCounterOnLaunchpad from './266_addIdentifierCounterOnLaunchpad';
import extendCoverageIsLeadLogic from './266_extendCoverageIsLeadLogic';
import renameModulesInLaunchpad from './267_renameModulesInLaunchpad';
import isLeadByCampaignId from './268_isLeadByCampaignId';
import generateIntegrationSettingForPorscheRetainModule from './269_generateIntegrationSettingForPorscheRetainModule';
import refactorUserGroupToRoles from './26_refactorUserGroupToRoles';
import addViewLaunchPadTradeInPermission from './270_addViewLaunchPadTradeInPermission';
import updateExistingLeadPermissions from './270_updateExistingLeadPermissions';
import addLaunchpadCreateApplicationPermission from './271_addLaunchpadCreateApplicationPermission';
import cleanupFinderAssignedStockOnLaunchpadModule from './271_cleanupFinderAssignedStockOnLaunchpadModule';
import addSalesOfferAndVehicleDataAndPorscheCodeIntegrationModulePermissions from './272_addSalesOfferAndVehicleDataAndPorscheCodeIntegrationModulePermissions';
import fixIncorrectPermissions from './272_fixIncorrectPermissions';
import removeLaunchpadModuleCreateApplication from './272_removeLaunchpadModuleCreateApplication';
import migrateOldApplicationLeadStatusAuditTrail from './273_migrateOldApplicationLeadStatusAuditTrail';
import removeDuplicatedLaunchPadLeadPermission from './273_removeDuplicatedLaunchPadLeadPermission';
import addAllowSearchEnginesSetting from './274_addAllowSearchEnginesSetting';
import addCompleteTestDriveEmailToAppointmentModule from './274_addCompleteTestDriveEmailToAppointmentModule';
import addCreateSalesOfferPermission from './275_addCreateSalesOfferPermission';
import initialKycFieldExtraSettings from './275_initialKycFieldExtraSettings';
import initialKycFieldExtraSettingsOnLCF from './276_initialKycFieldExtraSettingsOnLCF';
import setDefaultContentForBookingCompleteEmail from './276_setDefaultContentForBookingCompleteEmail';
import addLeadExportIndexes from './277_addLeadExportIndexes';
import updatePermissionsForLeadManageHierarchy from './277_updatePermissionsForLeadManageHierarchy';
import removeCapPreQualificationOnLaunchpadModule from './278_removeCapPreQualificationOnLaunchpadModule';
import addIndexToLead from './279_addIndexToLead';
import insertVisitAppointmentConfigurationForLaunchpad from './279_insertVisitAppointmentConfigurationForLaunchpad';
import updateAdminPermissions from './27_updateAdminPermissions';
import insertMobileVerificationOnKYCExtraSettings from './280_insertMobileVerificationOnKYCExtraSettings';
import addViewPorscheRetainPermission from './281_addViewPorscheRetainPermission';
import addAutoAssignmentToEvent from './282_addAutoAssignmentToEvent';
import addSalesOfferModuleEmailContent from './282_addSalesOfferModuleEmailContent';
import customerListIndexImprovements from './282_customerListIndexImprovements';
import insertDefaultAllowLimitDealerFeature from './282_insertDefaultAllowLimitDealerFeature';
import addVsaForSalesOffer from './283_addVsaForSalesOffer';
import addVsaSerialNumberForSalesOffer from './283_addVsaSerialNumberForSalesOffer';
import removeSalesOfferModuleCustomerModule from './283_removeSalesOfferModuleCustomerModule';
import updateTradeInSalesOfferLastUpdatedAt from './283_updateTradeInSalesOfferLastUpdatedAt';
import renameLeadStatusFromInProcessToSubmittedToCap from './284_renameLeadStatusFromInProcessToSubmittedToCap';
import removeCustomerModuleFromSalesOfferModule from './285_removeCustomerModuleFromSalesOfferModule';
import addIndexToDealerLocation from './286_addIndexToDealerLocation';
import addSalesOfferEmailTemplateSalesManager from './287_addSalesOfferEmailTemplateSalesManager';
import addSendCalendarInviteToCompany from './287_addSendCalendarInviteToCompany';
import unsetSalesOfferModulesTerms from './287_unsetSalesOfferModulesTerms';
import addViewFollowUpPermission from './288_addViewFollowUpPermission';
import addBookingAmendmentToAppointmentModules from './289_addBookingAmendmentToAppointmentModules';
import initialUserGroupIndexes from './28_initialUserGroupIndexes';
import updateMyInfoSetting from './28_updateMyInfoSetting';
import addCompanyIdToLeads from './290_addCompanyIdToLeads';
import addCreatedByForSalesOfferDocuments from './290_addCreatedByForSalesOfferDocuments';
import initialEventThankyouPageContent from './290_initialEventThankyouPageContent';
import addShareTemplateForSalesOfferModule from './291_addShareTemplateForSalesOfferModule';
import insertHasTradeInRequestOnLaunchpad from './291_insertHasTradeInRequestOnLaunchpad';
import applicationModuleLeadPermission from './292_applicationModuleLeadPermission';
import addCreateLeadPermission from './293_addCreateLeadPermission';
import fixBadDataAppModulesLinkedToInvalidInsuranceData from './294_fixBadDataAppModulesLinkedToInvalidInsuranceData';
import addModuleViewPermissionsForEventFullAccess from './295_addModuleViewPermissionsForEventFullAccess';
import addLeadMergeAuditTrails from './296_addLeadMergeAuditTrails';
import revertSalesControlBoardPermissions from './299_revertSalesControlBoardPermissions';
import initialDealer from './29_initialDealer';
import updateDealerVehiclesForApplicationModule from './29_updateDealerVehiclesForApplicationModule';
import addSalesPersonBookingConfirmationEmailContent from './300_addSalesPersonBookingConfirmationEmailContent';
import addCampaignPermissions from './301_addCampaignPermissions';
import initialCancellationEmailOnAppointment from './301_initialCancellationEmailOnAppointment';
import updateAddressAutofillType from './301_updateAddressAutofillType';
import addIndexToCampaign from './302_addIndexToCampaign';
import initialTestDriveEmailOnLCF from './302_initialTestDriveEmailOnLCF';
import updateStandardApplicationRouter from './30_updateApplicationListRouter';
import updateDealerAdminPermissions from './30_updateDealerAdminPermissions';
import addCompanyTheme from './31_addCompanyTheme';
import addDealerIdOnApplication from './31_addDealerIdOnApplication';
import updateDealerFinanceProductsForApplicationModule from './31_updateDealerFinanceProductsForApplicationModule';
import updateDealerIdsOnUserGroup from './31_updateDealerIdsOnUserGroups';
import addApplicationModuleOcrSettings from './32_addApplicationModuleOcrSettings';
import dropOriginKeyInAdyenSetting from './32_dropOriginKeyInAdyenSetting';
import updateApplicationListRouterTitle from './32_updateApplicationListRouterTitle';
import updateStandardApplicationModuleMarket from './32_updateStandardApplicationModuleMarket';
import initializeCompanyCountryCode from './33_initializeCompanyCountryCode';
import updateDealershipSettingFields from './33_updateDealershipSettingFields';
import updateApplicationFinancingFinancedAmount from './34_updateApplicationFinancingFinancedAmount';
import updateConfiguratorPermissions from './34_updateConfiguratorPermissions';
import updateEventApplicationModuleMarket from './34_updateEventApplicationModuleMarket';
import updateDealerApplicationMarketInput from './35_updateDealerApplicationMarketInputs';
import updateStandardApplicationDepositAmount from './36_updateStandardApplicationDepositAmount';
import updateModulesApplicationMarketType from './37_updateModulesApplicationMarketType';
import replaceUsernameWithEmailOnUsers from './38_replaceUsernameWithEmailOnUsers';
import updateOcrEnabledInConfigurator from './38_updateOcrEnabledInConfigurator';
import updatePromoCodePermissions from './38_updatePromoCodePermissions';
import addLastSignedInForUsers from './39_addLastSignedInforUsers';
import renameInitialQuantityPromoCode from './39_renameInitialQuantityPromoCode';
import updateStandardApplicationEmptyDepositAmount from './39_updateStandardApplicationEmptyDepositAmount';
import addBodyTypeToVehicles from './40_addBodyTypeToVehicles';
import addEmailContentsConfiguratorModule from './40_addEmailContentsConfiguratorModule';
import updateDefaultCompanyName from './40_updateDefaultCompanyName';
import updateToNewDefaultCompanyName from './40_updateToNewDefaultCompanyName';
import addEmailContentsEventModule from './41_addEmailContentsEventModule';
import updateBankIntegrationSettingId from './41_updateBankIntegrationSettingId';
import updateVariantTechInfo from './41_updateVariantTechInfo';
import addVariantImages from './42_addVariantImages';
import updateConfiguratorApplicationModule from './42_updateConfiguratorApplicationModule';
import addVersioningOnRequiredCollections from './43_addVersioningOnRequiredCollections';
import updateConfiguratorApplicationModulePriceDisclaimer from './43_updateConfiguratorApplicationModulePriceDisclaimer';
import updateSdmBankIntegration from './44_updateSdmBankIntegration';
import updateInventoryPermissions from './45_updateInventoryPermissions';
import updateConfiguratorWithInventory from './46_updateConfiguratorWithInventory';
import updateStandardApplicationPriceDisclaimer from './47_updateStandardApplicationPriceDisclaimer';
import addTemplateSettingToSimpleVehicleManagementModule from './48_addTemplateSettingToSimpleVehicleManagementModule';
import addWithCustomerDevice from './48_addWithCustomerDevice';
import deleteAllInventories from './48_deleteAllInventoriesStock';
import consentsAndDeclarationsMigration from './49_consentsAndDeclarationsMigration';
import updateCalculatorData from './49_updateCalculatorData';
import addModuleIdForVariantConfigurator from './50_addModuleIdForVariantConfigurator';
import addPasswordConfigurationCompany from './50_addPasswordConfigurationCompany';
import addCustomerDataMaskingToCompany from './51_addCustomerDataMaskingToCompany';
import updateApplicationJourneyImmutable from './51_updateApplicationJourney';
import addHlfIntegrationPartialUrls from './52_addHlfIntegrationPartialUrls';
import updateApplicationModuleApplyForFinance from './52_updateApplicationModule';
import updateEdmEmailFooter from './53_updateEdmEmailFooter';
import addActiveToUserRoleUserGroup from './54_addActiveToUserRoleUserGroup';
import updateSMTPSetting from './54_updateSMTPSetting';
import addRemarksOnApplicationModules from './55_addRemarksOnApplicationModules';
import updateCompanyName from './56_updateCompanyName';
import updateFinanceApplicationSuiteIds from './57_updateFinanceApplicationSuiteIds';
import configuratorURLIdentifier from './58_configuratorURLIdentifier';
import inventoryIdentifier from './59_inventoryIdentifier';
import updateConfiguratorPriceDisclaimerType from './60_updateConfiguratorPriceDisclaimerType';
import rollbackPriceDisclaimerStandardApplicationType from './61_rollbackPriceDisclaimerStandardApplicationType';
import addShowRemoteFlowButton from './62_addShowRemoteFlowButton';
import removeProvisioningDrivingLicensse from './62_removeProvisioningDrivingLicensse';
import renameBankCustomerInfoReceivedStatus from './62_renameBankCustomerInfoReceivedStatus';
import updateSDMModuleSetting from './62_updateSDMModuleSetting';
import addConfiguratorToDealership from './63_addConfiguratorToDealership';
import updateSdmBankIntegrationSettings from './63_updateSdmBankIntegrationSettings';
import addSentActivationEmailToUser from './64_addSentActivationEmailToUser';
import updateSdmModuleMappings from './65_updateSdmModuleMappings';
import updateDealershipFinanceProduct from './66_updateDealershipFinanceProduct';
import updateConsentsAndDeclarationsPurpose from './67_updateConsentsAndDeclarationsPurpose';
import updateApplicationAgreementPurpose from './68_updateApplicationAgreementPurpose';
import updateKycPresetPurpose from './69_updateKycPresetPurpose';
import addSkipDepositOnStandardModule from './70_addSkipDepositOnStandardModule';
import updateVariantConfiguratorVehiclePrice from './70_updateVariantConfiguratorVehiclePrice';
import updateBannerAndLabelsPermissions from './71_updateBannerAndLabelsPermissions';
import updateMarketingConsentsDefaultChecked from './71_updateMarketingConsentsDefaultChecked';
import addAllowedIPsOnUOBSettings from './72_addAllowedIPsOnUOBSettings';
import addShowFromValueOnVehicleDetails from './72_addShowFromValueOnVehicleDetails';
import addBankDisplayPreference from './73_addBankDisplayPreference';
import updateSDMApplicationVehicleScheme from './73_updateSDMApplicationVehicleScheme';
import addDefaultFinanceProductUnits from './74_addDefaultFinanceProductUnits';
import updateMobilityModule from './75_updateMobilityModule';
import addAmountForPorschePayment from './76_addAmountForPorschePayment';
import generateMobilityAndWebsitePermission from './76_generateMobilityAndWebsitePermissions';
import updateBmwModuleAndDbsBankSettings from './76_updateBmwModuleAndDbsBankSettings';
import updateWebpageColumnBlock from './76_updateWebpageColumnBlock';
import updateLocalVariantSeatsTransmission from './77_updateLocalVariantSeatsTransmission';
import updateMobilityModuleEmailContent from './77_updateMobilityModuleEmailContent';
import updateSDMStateStatusApprovalExpiry from './77_updateSDMStateStatusApprovalExpiry';
import updateStockForInventoryId from './77_updateStockForInventoryId';
import addMobilityScenario from './78_addMobilityScenario';
import updateMobilityDurationBeforeNextBooking from './78_updateMobilityDurationBeforeNextBooking';
import updateMonthlyInstalment from './78_updateMonthlyInstalment';
import updateMobilityModuleEmailContentBannerImage from './79_updateMobilityModuleEmailContentBannerImage';
import addDealerIdOnMobility from './80_addDealerIdOnMobility';
import updateWebpageUrlSlug from './80_updateWebpageUrlSlug';
import addMobilityEmailonApplication from './81_addMobilityEmailonApplication';
import prunePermissions from './81_prunePermissions';
import addMobilityModuleUnavailableTimeRange from './82_addMobilityModuleUnavailableTimeRange';
import addVehicleEngineType from './83_addVehicleEngineType';
import addDocumentPartialForBmwBanks from './84_addDocumentPartialForBmwBanks';
import addVariantsToPromoCode from './84_addVariantsToPromoCode';
import addFinanceProductTypeInBank from './85_addFinanceProductTypeToBank';
import addMappingsToDbsIntegration from './85_addMappingsToDbsIntegration';
import generateFinderVehiclePermissions from './85_generateFinderVehiclePermissions';
import addDayofWeekMobilityModule from './86_addDayofWeekMobilityModule';
import addBookingRangeMobilityModule from './87_addBookingRangeMobilityModule';
import fixFinanceProductWithDownpaymentTable from './88_fixFinanceProductWithDownpaymentTable';
import addEngineTypeForSDMApplication from './89_addEngineTypeForSDMApplication';
import addSalesNameToSdmApplications from './90_addSalesNameToSdmApplications';
import updateBankStaffViewApplicationPermission from './90_updateBankStaffViewApplicationPermission';
import eventURLSlug from './91_eventURLSlug';
import addShowMarginOfFinanceToBank from './92_addShowMarginOfFinanceToBank';
import addDisplayAppointmentDatepickerinApplicationModule from './93_addDisplayAppointmentDatepickerinApplicationModule';
import addTradeInOnStandardApplicationModule from './93_addTradeInOnStandardApplicationModule';
import updateApplicationStages from './93_updateApplicationStages';
import addHasVSOUploadToBank from './94_addHasVSOUploadToBank';
import addTradeInVehicleToStandardApplications from './94_addTradeInVehicleToStandardApplications';
import updateAuditTrailStages from './94_updateAuditTrailStages';
import removeUnselectedTradeInVehicle from './95_removeUnselectedTradeInVehicle';
import addAppointmentCounterToApplicationModule from './96_addAppointmentCounterToApplicationModule';
import generateEventPermissions from './96_generateEventPermissions';
import addConfiguratorDescriptionImages from './97_addConfiguratorDescriptionImages';
import generateInsurancePermissions from './97_generateInsurancePermissions';
import addApplicationSuiteIdIndexes from './97_updateApplicationIndexes';
import updateModulePermission from './97_updateLabelModulePermission';
import addInsuranceToApplicationModules from './98_AddInsuranceToApplicationModules';
import updatePromoCodeModulePermission from './98_updatePromoCodeModulePermission';
import updateScenarios from './98_updateScenarios';
import addMissingInsuranceInApplicationModules from './99_addMissingInsuranceInApplicationModules';
import addVersioningOnLanguagePacks from './99_addVersioningOnLanguagePacks';

export interface Migration {
    identifier: string;
    up: (context: DatabaseContext) => Promise<void>;
    squash?: (Migration | string)[];
    generatePermissions?: boolean;
    deleteUnusedPermissions?: boolean;
}

const migrations: Migration[] = [
    initialMigrationsIndexes,
    initialUsersIndexes,
    initialUser,
    addDefaultLocaleSetting,
    addDefaultEmailContextSetting,
    initialExternalLinkIndexes,
    initialUserSessionIndexes,
    initialUserWebCredentialsIndexes,
    addSingleSessionMode,
    updateRouters,
    updateRoutersPart2,
    addLanguagesOnRouters,
    updateSettingsIndexes,
    addDefaultValuesOnCompanies,
    updateDefaultValuesOnUsers,
    addTranslationsOnCompanies,
    createPermissionIndexes,
    addPermissionOrigins,
    initialCounterIndexes,
    addConditionsOnKYC,
    addVersioningOnConsents,
    addPurposeOnConsentsAndDeclaration,
    addScenarioAndCounterPadding,
    renameVariantIdsOnFinanceProduct,
    renameVariantIdsOnLeaseTable,
    renameApplicationStatus,
    updateCounterIndexes,
    updateSessionTimeout,
    addDraftAndStageOnApplications,
    updateCompanySmsSettings,
    refactorUserGroupToRoles,
    updateAdminPermissions,
    updateMyInfoSetting,
    initialUserGroupIndexes,
    updateDealerVehiclesForApplicationModule,
    initialDealer,
    updateDealerAdminPermissions,
    updateStandardApplicationRouter,
    updateDealerFinanceProductsForApplicationModule,
    updateDealerIdsOnUserGroup,
    addDealerIdOnApplication,
    addApplicationModuleOcrSettings,
    updateDealershipSettingFields,
    addCompanyTheme,
    updateApplicationListRouterTitle,
    initializeCompanyCountryCode,
    dropOriginKeyInAdyenSetting,
    updateStandardApplicationModuleMarket,
    updateEventApplicationModuleMarket,
    updateConfiguratorPermissions,
    updateDealerApplicationMarketInput,
    updateStandardApplicationDepositAmount,
    updateModulesApplicationMarketType,
    updatePromoCodePermissions,
    updateOcrEnabledInConfigurator,
    updateStandardApplicationEmptyDepositAmount,
    addLastSignedInForUsers,
    replaceUsernameWithEmailOnUsers,
    updateApplicationFinancingFinancedAmount,
    renameInitialQuantityPromoCode,
    updateDefaultCompanyName,
    updateToNewDefaultCompanyName,
    updateVariantTechInfo,
    addBodyTypeToVehicles,
    updateConfiguratorApplicationModule,
    updateBankIntegrationSettingId,
    addVersioningOnRequiredCollections,
    updateSdmBankIntegration,
    updateInventoryPermissions,
    updateConfiguratorWithInventory,
    addEmailContentsConfiguratorModule,
    addEmailContentsEventModule,
    updateConfiguratorApplicationModulePriceDisclaimer,
    updateStandardApplicationPriceDisclaimer,
    deleteAllInventories,
    addVariantImages,
    updateSdmBankIntegration,
    updateInventoryPermissions,
    updateConfiguratorWithInventory,
    updateConfiguratorApplicationModulePriceDisclaimer,
    updateStandardApplicationPriceDisclaimer,
    addTemplateSettingToSimpleVehicleManagementModule,
    updateCalculatorData,
    addModuleIdForVariantConfigurator,
    addWithCustomerDevice,
    updateApplicationJourneyImmutable,
    consentsAndDeclarationsMigration,
    addHlfIntegrationPartialUrls,
    updateEdmEmailFooter,
    addPasswordConfigurationCompany,
    updateApplicationModuleApplyForFinance,
    updateSMTPSetting,
    addRemarksOnApplicationModules,
    addMenuItemsOnRouters,
    addActiveToUserRoleUserGroup,
    updateCompanyName,
    updateFinanceApplicationSuiteIds,
    configuratorURLIdentifier,
    addCustomerDataMaskingToCompany,
    inventoryIdentifier,
    updateConfiguratorPriceDisclaimerType,
    rollbackPriceDisclaimerStandardApplicationType,
    removeProvisioningDrivingLicensse,
    renameBankCustomerInfoReceivedStatus,
    updateSdmBankIntegrationSettings,
    addShowRemoteFlowButton,
    addSentActivationEmailToUser,
    addConfiguratorToDealership,
    updateSDMModuleSetting,
    updateSdmModuleMappings,
    updateDealershipFinanceProduct,
    updateConsentsAndDeclarationsPurpose,
    updateApplicationAgreementPurpose,
    updateKycPresetPurpose,
    updateVariantConfiguratorVehiclePrice,
    updateMarketingConsentsDefaultChecked,
    addSkipDepositOnStandardModule,
    addAllowedIPsOnUOBSettings,
    updateBannerAndLabelsPermissions,
    updateSDMApplicationVehicleScheme,
    addDefaultFinanceProductUnits,
    updateMobilityModule,
    addShowFromValueOnVehicleDetails,
    addBankDisplayPreference,
    updateBmwModuleAndDbsBankSettings,
    updateLocalVariantSeatsTransmission,
    addAmountForPorschePayment,
    updateSDMStateStatusApprovalExpiry,
    updateMobilityModuleEmailContentEmailsEnabled,
    updateWebpageColumnBlock,
    updateStockForInventoryId,
    generateMobilityAndWebsitePermission,
    updateMobilityModuleEmailContent,
    updateMobilityDurationBeforeNextBooking,
    addMobilityScenario,
    updateMobilityModuleEmailContentBannerImage,
    updateMonthlyInstalment,
    addDealerIdOnMobility,
    prunePermissions,
    addMobilityEmailonApplication,
    addMobilityModuleUnavailableTimeRange,
    addVehicleEngineType,
    addDocumentPartialForBmwBanks,
    updateWebpageUrlSlug,
    addVariantsToPromoCode,
    addMappingsToDbsIntegration,
    addDayofWeekMobilityModule,
    addBookingRangeMobilityModule,
    fixFinanceProductWithDownpaymentTable,
    addEngineTypeForSDMApplication,
    generateFinderVehiclePermissions,
    addFinanceProductTypeInBank,
    addSalesNameToSdmApplications,
    eventURLSlug,
    addShowMarginOfFinanceToBank,
    updateBankStaffViewApplicationPermission,
    addTradeInOnStandardApplicationModule,
    addTradeInVehicleToStandardApplications,
    removeUnselectedTradeInVehicle,
    generateEventPermissions,
    addDisplayAppointmentDatepickerinApplicationModule,
    addAppointmentCounterToApplicationModule,
    addHasVSOUploadToBank,
    addConfiguratorDescriptionImages,
    updateModulePermission,
    addApplicationSuiteIdIndexes,
    updatePromoCodeModulePermission,
    addVersioningOnLanguagePacks,
    cleanUpConfigurators,
    updateBankPermissions,
    insertRequiredConfigurationFieldForEvent,
    updateBmwRelatedSettings,
    updateAuditTrailRecipient,
    updateWebpageModulePermissions,
    updateExistingKYCPresets,
    addBankHasUploadDocuments,
    addAllowedIPsOnBankSettings,
    addStatisticIndexes,
    updateDeterministicNumberKey,
    updateApplicationStages,
    updateAuditTrailStages,
    addDisplayAppointmentDatepickerinApplicationModule,
    addAppointmentCounterToApplicationModule,
    generateInsurancePermissions,
    addInsuranceToApplicationModules,
    addMissingInsuranceInApplicationModules,
    addInitialWithInsurance,
    updateScenarios,
    renameFinanceSuiteIdsToReference,
    resolveFinanceSuiteIdsToReference,
    addShowCalculatorToApplicationModules,
    resolveMayApplyForInsurance,
    changeInsuranceDisclaimerToTranslatedString,
    updateStringDescriptionKey,
    addMissingReservationStatus,
    addMissingReservationStatusPorsche,
    resolveUnitInBalloonSetting,
    renameFinderApplicationEntrypoint,
    addBookingScenarioToAllMobilityModules,
    addPersistKYCDataToMobilityModule,
    updateInsurancePermission,
    generateDefaultInsuranceProducts,
    updateMobilityModuleRentalRequirement,
    updateMobilityModuleRentalRequirementAndDisclaimer,
    addDealerMobilityBookingCode,
    addMobilityInventoryBlockingDates,
    deleteAndSyncFinanceProducts,
    updateMobilityStock,
    createInitialInventoryAuditTrails,
    addSigningInMobility,
    resolvePermissionOnFinderVehicle,
    resolveListFinderPermission,
    updateAppointmentIdentifiers,
    updateFinderApplicationEndpoints,
    reassignManageFinderPermission,
    triggerAppointmentAndMaintenancePermission,
    moveCtsConfigurationToSetting,
    resolveUserGroupsMissingCompanyId,
    updateApplicationModulePermission,
    updateApplicationPermissionRecalculate,
    triggerCompanyUserPermission,
    retriggerCompanyUserPermission,
    updateApplicationModulePermissions,
    updateUserIndexes,
    addDealerVehiclesToEvents,
    updateExistingAppointmentStatus,
    updateMissingInventoriesIdentifier,
    triggerApplicationPermission,
    updateFinderPrivateApplicationEndpoints,
    updateFinanceProductInterestRate,
    updateMobilityModuleAvailableRange,
    addDealerOptions,
    addNewFieldsToInterestRateTable,
    updateInventoryPermission,
    updateBankAuditTrails,
    addVehicleParametersInFinanceProduct,
    regeneratePermissions,
    addShowResetKYCButtonToModule,
    defaultBankFinancingDisclaimer,
    addLiveChatSettingId,
    updateInsuranceDisclaimer,
    updateAppointmentCheckInStatus,
    updateTestDriveSettingForAppointment,
    migrateMyInfoSettings,
    changeCarConditionInFinderModule,
    addApplicationModulesToPromoCode,
    addReservationInstructionsToFinderModule,
    updateFinderModuleShowFinanceCalculator,
    updateFinderModuleShowVisitModelPage,
    addAtlasAdviserIndexes,
    addReservationPeriodToFinderModule,
    addReminderEmailToFinderEmailContents,
    realignModulePermissionSystemName,
    regeneratePermissions16Nov,
    deleteRemovedPermissions,
    cleanupRemovedPermissions,
    addSendPDFEmailToCustomerToBankInsurerMobility,
    regeneratePermissionsForDealer,
    addVehicleParameterFilterTable,
    reassignDealerPermission,
    addEndpointApiVersionPorschePaymentSetting,
    adjustUseMyinfoInApplication,
    regenerateDealerPermission,
    addKycPresetInEvent,
    updateKycFieldsInCustomerModule,
    updateVariantYear,
    addIncludeDealerOptionsForFinancing,
    addDefaultShowroomSalesShare,
    renameFinderApplicationEntities,
    removeFinderEntrypoints,
    addCompanyNameInEnbdBank,
    addAdditionalConsentsFeaturePurpose,
    addTradeInVehiclesForKYCFields,
    addEventCustomizedFields,
    generateDealerViewManagementPermission,
    updateStageAssignees,
    addMobilityLocationId,
    sortSavedKycFields,
    renameLeadStageStatus,
    renameMobilityStageStatus,
    changeFinancingOptionalModule,
    updateReservationStatuses,
    addLocationEmailContentMobility,
    removeSpecificKycPresetInEvent,
    addDrivingLicenseThForKYCFields,
    refactorEmailContentsMobility,
    updateMobilityHomeDelivery,
    updateFlexibleDiscountModule,
    updateMobilityOptionsPrice,
    updateWebsiteModule,
    resolveEmailContentMobility,
    updateBmwModulePermissions,
    changeBannerTextToTranslatedStringForBanner,
    addTranslationsOnConfiguratorModel,
    addTranslationsOnConfiguratorVariants,
    addTranslationsOnInventory,
    addTranslationsOnConfiguratorApplication,
    fixConfiguratorOptionsValueTextNullData,
    addCOEForCompanyAndDealer,
    addPorscheApprovedInfoForFinderModule,
    addTempApplicationDocumentIndex,
    updateCTSSettingFinderModuleIds,
    removeFinancingFromEventModule,
    removeEventModuleFromPromoCode,
    renameSdmBankToExternalBank,
    renameSdmCodeToSalesPersonCode,
    cleanUpMobilityEmailContent,
    updateFuelConsumption,
    addDataPurgeInCompany,
    updateFinanceProductFinderVehicleCondition,
    renameSdmModuleIdToFinancingModuleId,
    updateCTSSettingActive,
    addIsGenerateStepsInTableForFinanceProduct,
    addDrivingLicenseMyForKYCFields,
    removeInvalidRouterMenuItems,
    updateEventPermissions,
    addApprovalApplicationPermission,
    addHasTestDriveProceedAppointmentModule,
    removeApplyDefaultKycInEvent,
    addSalesNameToBmwApplications,
    addFeildsToBmwApplications,
    renameVehicleOnBmwApplication,
    updateEventApplicationModuleEmailContent,
    updateConfiguratorApplicationModuleEmailContent,
    updateFinderApplicationPrivateModuleEmailContent,
    updateFinderApplicationPublicModuleEmailContent,
    updateMobilityApplicationModuleEmailContent,
    updateInsuranceApplicationPermissions,
    addAudiFinancingPermissions,
    addNZFieldForCompanyAndDealer,
    addJobTitleThForKYCFields,
    addSkipForDeposit,
    addSuiteIdOnProceedWithLink,
    rearrangeSavedKycFields,
    setProLayoutOnRouter,
    addIsAddOnSpecificVehiclesForFinanceProductVRP,
    updateLayoutsToProOnRouter,
    addAppointmentEmailContent,
    updateEventWithDealerPermissions,
    upsertCompanyPermissions,
    removeRolePermissions,
    removeUserGroupPermissions,
    updateFinderVehicleSettingIsTestDriveButtonHidden,
    addIsLTAUploaded,
    updateDefaultAppointmentEmailContent,
    upsertEventPermissions,
    addRegionToKycField,
    sortKycFieldAfterRegion,
    addLastNameJapanAndFirstNameJapanKYCFields,
    addVehicleContractEndKYCFields,
    updateCompanyPermissions,
    addSkipDepositOnFinderPublicModule,
    addFinanceProductIdToAudiFinancing,
    resetExpireOnAvailableFinder,
    addBankIdsToModules,
    addTestDriveInitialSettingInJourney,
    renameAppointmentEmailContent,
    renameCheckinCheckout,
    resolveApplicationStages,
    customerIndex,
    removeApplyPropertyFromModule,
    updateMobilitySubmissionAuditTrail,
    updateAudiFinancingNullDocuments,
    updateBankApplicationAccess,
    addBankEstFeeToNzMarket,
    addIdentifierToBank,
    addBankModuleIdToAudiFinancingModule,
    addGiftVoucherPermissions,
    addVersioningOnTradeInCollection,
    addShowDealershipToEvent,
    addIdentifierToDeletedBank,
    updateHasLegalMarkup,
    addOrderInKycFieldsCustomerModule,
    updateMissingLocalCustomerFieldKey,
    addLeadsAndCustomerPermissions,
    addEventLevelCustomiseContentsDefaultValue,
    addTradeInPermissions,
    updatePermissions,
    addCompanyViewFromOptions,
    updateConsentsAndKycPresets,
    addTradeInStatus,
    rearrangeVariantForDeletedSubmodel,
    removeEndpointWithDeletedModule,
    updateGiftVoucherConsents,
    updateConfiguratorApplicationModuleTerm,
    updateSdmAuthorToSalesforce,
    addCustomerPermissionsToAllRoles,
    updateApplicationPermissions,
    regenerateSimplifyPermissions,
    populateReferenceNameLanguagePacks,
    updateAgreementPermissions,
    addNewAppointmentEmailContents,
    updateHlfReassignPartial,
    addShowRemoteFlowAppointmentModule,
    generateModulePermissions,
    addAllowVariantCodeMappingToBank,
    addBankCodeMappingToVariant,
    updateDubaiMarketplaceId,
    updateNamirialSettings,
    updateRouterPathScripts,
    removeIdentifierFromBank,
    moveDealerCodeToBankIntegrationSetting,
    addBankReferenceIdentifier,
    removePermissions,
    addCommentsToBankAndInsurer,
    updateModuleInDealerUpdateAccess,
    updateUnremovedPermissions,
    addIsDeletedToDealers,
    fixHlfSubmissionTypo,
    addDbsPayloadScheme,
    addTypeToInsuranceProducts,
    updateApplicationInsurancing,
    addInstantApprovalStatsFlagToCompany,
    insurerInsuranceProductType,
    addVehicleReferenceParametersToInsuranceProduct,
    addDealerInsuranceProductsToModules,
    addErgoLookupTableToErgoInsuranceProducts,
    addCurrentVehicleFieldsToModule,
    removeTradeInVehicleFieldsonEventKycPreset,
    addCurrentAnotherVehicleFieldToModule,
    migrateVehicleContractEndKyc,
    removeNonPorscheModules,
    updateAppointmentModuleEmailContent,
    updateAppointmentModuleAddEndTestDriveReminder,
    updateAppointmentModuleEmailContentToNewStructure,
    removeDuplicatedAuditTrails,
    addVehicleFilterParameterTableToFinanceProductWithTable,
    updateStandardApplicationModuleAddEmailContents,
    separateCapIntegrationStatusFromApplicationStatus,
    changeQualifyAndUnqualifyAuditTrailKind,
    updateReminderEndTestDriveApplicationId,
    updateCustomerShareAndComparisonShareEmailContents,
    updateFinderAppModuleEmailContents,
    addCampaignSettingsToEvent,
    initialValueForVariantImageReminderMail,
    setUseDisclaimersInFinderModules,
    updateEventApplicationModuleEmailContents,
    updateCapSetting,
    addDisplayAppointmentDatepickerInEvent,
    updateShowroomEmailContentIntroImage,
    updateStandardApplicationModuleAddSalesPersonCancelledEmail,
    updateApplicationQuotationAmount,
    addListingUrlFinderEndpoint,
    updateLeadGenFormDepositAmount,
    addDealerSpecificPaymentSettingToStandardModule,
    addIsActiveToDealershipPaymentSetting,
    addDealerSpecificDepositAmountToModule,
    updatePermissionUpdate,
    addMissingLeadStageToEventApplication,
    uploadPorscheFontToS3,
    restructureEventUtmParameters,
    updateJourneyIsImmutableForDeclined,
    addApplicationIndexes,
    addRouterPathScripts,
    addMobilityApplicationBookingConfirmationEmail,
    updateDealerVehiclesLaunchPadModule,
    addEventDisplayVisitAppointment,
    updateConfigurationForEventApplication,
    addUtmURLLeadCaptureForm,
    addUserIdsToEvent,
    addHasVehicleIntegrationToEvent,
    addMarketingDashboardPermission,
    addMarketingDashboardPermission,
    addCompanyCalculationRounding,
    switchCapSetting,
    updateDealerVehiclesLaunchPadModule,
    splitLeadStageFromApplications,
    dropLeadCaptureScenario,
    splitApplicationAndLeadPermissions,
    addIndexToLeadId,
    migrateApplicationAgreementToLead,
    initialTradeInForLead,
    migrateViewLeadPermissions,
    addLaunchpadPermissionOnLead,
    addContactPermissionOnLead,
    reSplitLeadStageFromApplications,
    migrateCapStatusAuditTrail,
    reInitialTradeInForLead,
    addRequestPermissionForLaunchpad,
    applicationToLeadEndpoint,
    addStageToLeadListEndpoint,
    updateLeadPermission,
    updateLeadPermissions,
    copyCampaignValuesAndEventIdFromApplicationToLead,
    migrateOtherApplicationKindsToLead,
    addDraftOnLeads,
    migrateApplicationDocumentToLead,
    updateLeadStatuses,
    restructureLeadPermissions,
    addIsLeadIntoLead,
    updateLeadSubmissionFailStatus,
    addCustomizedFieldToLeads,
    migrateLeadAuditTrail,
    simplifyCampaignId,
    renameLeadStatus,
    updateIsLeadLogic,
    changeApplicationsOfLeadOnlyToDraft,
    addIdentifierCounterOnLaunchpad,
    renameModulesInLaunchpad,
    extendCoverageIsLeadLogic,
    isLeadByCampaignId,
    generateIntegrationSettingForPorscheRetainModule,
    addViewLaunchPadTradeInPermission,
    updateExistingLeadPermissions,
    addLaunchpadCreateApplicationPermission,
    cleanupFinderAssignedStockOnLaunchpadModule,
    fixIncorrectPermissions,
    removeLaunchpadModuleCreateApplication,
    removeDuplicatedLaunchPadLeadPermission,
    migrateOldApplicationLeadStatusAuditTrail,
    addSalesOfferAndVehicleDataAndPorscheCodeIntegrationModulePermissions,
    addAllowSearchEnginesSetting,
    addCreateSalesOfferPermission,
    addCompleteTestDriveEmailToAppointmentModule,
    migrateMyInfoV5Settings,
    setDefaultContentForBookingCompleteEmail,
    initialKycFieldExtraSettings,
    initialKycFieldExtraSettingsOnLCF,
    updatePermissionsForLeadManageHierarchy,
    removeCapPreQualificationOnLaunchpadModule,
    insertVisitAppointmentConfigurationForLaunchpad,
    insertMobileVerificationOnKYCExtraSettings,
    addIndexToLead,
    addLeadExportIndexes,
    addViewPorscheRetainPermission,
    insertDefaultAllowLimitDealerFeature,
    customerListIndexImprovements,
    addAutoAssignmentToEvent,
    addSalesOfferModuleEmailContent,
    updateTradeInSalesOfferLastUpdatedAt,
    renameLeadStatusFromInProcessToSubmittedToCap,
    addVsaSerialNumberForSalesOffer,
    addVsaForSalesOffer,
    removeCustomerModuleFromSalesOfferModule,
    addIndexToDealerLocation,
    removeSalesOfferModuleCustomerModule,
    addSalesOfferEmailTemplateSalesManager,
    addSendCalendarInviteToCompany,
    addViewFollowUpPermission,
    unsetSalesOfferModulesTerms,
    addBookingAmendmentToAppointmentModules,
    addCompanyIdToLeads,
    addCreatedByForSalesOfferDocuments,
    insertHasTradeInRequestOnLaunchpad,
    initialEventThankyouPageContent,
    addShareTemplateForSalesOfferModule,
    applicationModuleLeadPermission,
    addCreateLeadPermission,
    fixBadDataAppModulesLinkedToInvalidInsuranceData,
    addModuleViewPermissionsForEventFullAccess,
    addLeadMergeAuditTrails,
    revertSalesControlBoardPermissions,
    addSalesPersonBookingConfirmationEmailContent,
    updateAddressAutofillType,
    addCampaignPermissions,
    addIndexToCampaign,
    initialCancellationEmailOnAppointment,
    initialTestDriveEmailOnLCF,
];

export default migrations;
