import { ObjectId } from 'mongodb';
import type { DealershipSetting } from './DealershipSetting';
import type { KYCPreset } from './KYCPresets';
import type { TranslatedString } from './LanguagePack';
import type { SimpleVersioning } from './Versioning';
import {
    ApplicationScenario,
    type DealerTranslationText,
    type TimeSlotCore,
    TimeSlotEnum,
    type DealerDepositAmountSetting,
    type DealerVehicles,
    type EventApplicationModuleConfirmEmailContents,
    type AppointmentModuleEmailCustomer,
} from './moduleShared';
import type { LocalCustomerManagementKYCFieldExtraConfig } from './modules';
import { DayOfWeek, EventLeadOriginType, EventMediumType, type Period } from './shared';

export type EventUtmParametersDefaultValue = {
    capCampaignId?: string;
    capLeadSource: EventLeadOriginType;
    capLeadOrigin: EventMediumType;
};

export type EventUtmParametersOverride = EventUtmParametersDefaultValue & {
    /**
     * concatenate UTM values into URL string
     *
     * :: validation will be take place when FE and BE as well
     */
    utmUrl: string;
};

export type EventUtmParameters = {
    defaultValue: EventUtmParametersDefaultValue;
    overrides: EventUtmParametersOverride[];
};

export type EventEmailContents = {
    submitOrder: EventApplicationModuleConfirmEmailContents;
    testDrive: {
        customer: AppointmentModuleEmailCustomer;
    };
};

type ThankYouPageContent = {
    introTitle: DealerTranslationText;
    contentText: DealerTranslationText;
    isCustomRedirectionButton: boolean;
    redirectButton: {
        title: DealerTranslationText;
        url: string;
    };
};

export type Event = {
    _id: ObjectId;
    moduleId: ObjectId;
    displayName: string;
    name: TranslatedString;
    identifier: string;

    isActive: boolean;
    period: Period;

    publicSalesPerson?: DealershipSetting | undefined;
    myInfoSetting?: DealershipSetting;
    paymentSetting?: DealershipSetting;
    privateAccess: boolean;
    isAllowTradeIn: boolean;
    skipForDeposit: boolean;

    depositAmount: DealerDepositAmountSetting;

    urlSlug: string;

    isAllowTestDrive: boolean;
    isDeleted: boolean;
    _versioning: SimpleVersioning;
    scenarios: ApplicationScenario[];

    dealerIds: ObjectId[];
    dealerVehicles: DealerVehicles[];

    showLiveChat: boolean;
    showDealership: boolean;

    // KYC Presets; This will act as KYC field in this Event Journey
    // Special condition check will be applied to this KYC Preset
    // i.e. Trade in and test drive switch
    kycPresets: KYCPreset[];

    kycExtraSettings: LocalCustomerManagementKYCFieldExtraConfig;

    customizedFields: CustomizedField[];

    hasCustomiseEmail: boolean;

    hasCustomiseBanner: boolean;

    // Banner
    bannerId?: ObjectId | null;

    // TO DO VF-1472: Create migration for testDrive email contents on event
    // TO DO VF-1474 along with VF-1472: Create migration for cancellation email content on appointment
    // email contents
    emailContents: EventEmailContents;

    /* Integrated C@P Module */
    isSearchCapCustomerOptional: boolean;
    capPrequalification: boolean;

    enableDynamicUtmTracking: boolean;
    utmParametersSettings: EventUtmParameters;

    /* Porsche ID Integration */
    porscheIdModuleId?: ObjectId | null | undefined;
    isCustomerDataRetreivalByPorscheId?: boolean | null | undefined;
    isPorscheIdLoginMandatory?: boolean | null | undefined;

    // display Appointment Datepicker ( test drive ) in Application module
    displayAppointmentDatepicker: boolean;

    // display Appointment for Showroom visit Datepicker in Application module
    displayVisitAppointmentDatepicker: boolean;

    // userIds for User-Specific URL & QR Code
    userIds: ObjectId[];

    hasVehicleIntegration: boolean;
    isCapEnabled?: boolean;

    // Flag to enable automatic assignment of submissions from user-specific URLs to the origin sales consultant
    salesConsultantAutoAssignmentEnabled: boolean;

    hasCustomiseThankYouPage: boolean;
    thankYouPageContent: ThankYouPageContent;

    // Custom Test Drive Booking Slots configuration
    // Only applicable when displayAppointmentDatepicker is true
    // Uses existing TimeSlotCore structure for consistency with AppointmentModule
    customTestDriveBookingSlots?: CustomTestDriveBookingSlots;
};

export type CustomizedField = {
    _id: ObjectId;
    displayName: TranslatedString;
    order: number;
    characterLimit: number;
    isMandatory: boolean;
};

export enum BookingPeriodType {
    FixedPeriod = 'FIXED_PERIOD',
    BookingWindow = 'BOOKING_WINDOW',
}

export type CustomTestDriveBookingSlots = {
    isEnabled: boolean;
} & (
    | {
          bookingPeriodType: BookingPeriodType.FixedPeriod;
          fixedPeriods: TestDriveFixedPeriod[];
      }
    | {
          bookingPeriodType: BookingPeriodType.BookingWindow;
          bookingWindowSettings: TestDriveBookingWindowSettings;
      }
);

export type TestDriveFixedPeriod = {
    _id: ObjectId;
    startDate: Date;
    endDate: Date;
    advancedBookingLimit: number; // default 0 means no advance booking limit
    bookingTimeSlot: TimeSlotCore<TimeSlotEnum.Appointment>[]; // Reuse existing TimeSlotCore structure
};

// Aligned with existing AppointmentModule naming and type
export type TestDriveBookingWindowSettings = {
    unavailableDayOfWeek: DayOfWeek[];

    // set default 0 means no advance booking limit
    advancedBookingLimit: number;

    // empty means there is no limit for the last allowable appointment date
    maxAdvancedBookingLimit?: number | null | undefined;

    bookingTimeSlot: TimeSlotCore<TimeSlotEnum.Appointment>[]; // Reuse existing TimeSlotCore structure
};
