import { ObjectId } from 'mongodb';
import { TranslatedString } from './LanguagePack';
import { UploadedFileWithPreview } from './UploadedFile';

export enum CounterMethod {
    Monthly = 'monthly',
    Global = 'global',
    Yearly = 'yearly',
    Quarter = 'quarter',
}
export type EmailScenarioOverrides<T> = T & {
    scenarios: ApplicationScenario[];
    isActive: boolean;
};

export type EmailWithScenarioOverrides<T> = {
    defaultValue: T;
    overrides: EmailScenarioOverrides<T>[];
};
/**
 * Visit Appointment Types
 */

export type VisitAppointmentModuleEmailContent = {
    subject: DealerSpecificItem<TranslatedString>;
    introTitle: DealerSpecificItem<TranslatedString>;
    introImage?: UploadedFileWithPreview | null;
    contentText: DealerSpecificItem<TranslatedString>;
    isSummaryVehicleVisible: DealerSpecificItem<boolean>;
};

type BaseVisitAppointmentModuleEmail = {
    submitConfirmation: VisitAppointmentModuleEmailContent;
    bookingConfirmation: VisitAppointmentModuleEmailContent;
    bookingAmendment: VisitAppointmentModuleEmailContent;
    bookingCancellation: VisitAppointmentModuleEmailContent;
};

export type VisitAppointmentModuleEmailCustomer = BaseVisitAppointmentModuleEmail & {
    bookingComplete: VisitAppointmentModuleEmailContent;
};

export type VisitAppointmentModuleEmailContents = {
    customer: VisitAppointmentModuleEmailCustomer;
    salesPerson: BaseVisitAppointmentModuleEmail;
};

/**
 * Test Drive Types
 */
export type AppointmentModuleEmailContent = {
    subject: DealerSpecificItem<TranslatedString>;
    introTitle: DealerSpecificItem<TranslatedString>;
    introImage?: UploadedFileWithPreview | null;
    contentText: DealerSpecificItem<TranslatedString>;
    isSummaryVehicleVisible: DealerSpecificItem<boolean>;
};

export type AppointmentFinderReservation = AppointmentModuleEmailContent & {
    listTestDriveTitle?: DealerSpecificItem<TranslatedString>;
    listTestDriveItem?: DealerSpecificItem<TranslatedString>;
};

type BaseAppointmentModuleEmailCustomer = {
    submitConfirmation: AppointmentModuleEmailContent;
    bookingConfirmation: AppointmentModuleEmailContent;
    bookingAmendment: AppointmentModuleEmailContent;
    bookingCancellation: AppointmentModuleEmailContent;
};

export type AppointmentModuleEmailCustomer = BaseAppointmentModuleEmailCustomer & {
    endTestDriveWithProcess: AppointmentModuleEmailContent;
    completeTestDriveWithoutProcess: AppointmentModuleEmailContent;
};

export type AppointmentModuleEmailSalesPerson = BaseAppointmentModuleEmailCustomer & {
    finderReservation: AppointmentFinderReservation;
    endTestDriveReminder: AppointmentModuleEmailContent;
};

export type AppointmentModuleEmailContents = {
    customer: AppointmentModuleEmailCustomer;
    salesPerson: AppointmentModuleEmailSalesPerson;
};

export type StandardApplicationEmailContent = {
    subject: DealerSpecificItem<TranslatedString>;
    introTitle?: DealerSpecificItem<TranslatedString>;
    introImage?: DealerSpecificItem<UploadedFileWithPreview | null>;
    contentText: DealerSpecificItem<TranslatedString>;
    isSummaryVehicleVisible: DealerSpecificItem<boolean>;
};

export type StandardApplicationShareComparison = StandardApplicationEmailContent & {
    fileName?: DealerSpecificItem<TranslatedString>;
};

export type StandardApplicationModuleEmailCustomer = {
    share: StandardApplicationShareComparison;
    comparisonShare: StandardApplicationShareComparison;
    proceedWithCustomerDevice: StandardApplicationEmailContent;
    submissionConfirmation: StandardApplicationEmailContent;
    approved: StandardApplicationEmailContent;
    rejected: StandardApplicationEmailContent;
    cancelled: StandardApplicationEmailContent;
};

export type StandardApplicationModuleEmailSalesPerson = {
    approved: StandardApplicationEmailContent;
    rejected: StandardApplicationEmailContent;
    cancelled: StandardApplicationEmailContent;
};

export type StandardApplicationModuleEmailContents = {
    customer: StandardApplicationModuleEmailCustomer;
    salesPerson: StandardApplicationModuleEmailSalesPerson;
};

export type GiftVoucherModuleEmailContent = {
    subject: DealerSpecificItem<TranslatedString>;
    introTitle: DealerSpecificItem<TranslatedString>;
    introImage?: UploadedFileWithPreview | null;
    contentText: DealerSpecificItem<TranslatedString>;
};

export type GiftVoucherModuleCustomerEmails = {
    confirmation: GiftVoucherModuleEmailContent;
};

// we do not include salesperson email first
export type GiftVoucherModuleSalesPersonEmails = {
    confirmation: GiftVoucherModuleEmailContent;
};

export type GiftVoucherModuleEmailContents = {
    customer: GiftVoucherModuleCustomerEmails;
};

// we able extend the TimeSlot for other feature / module
export type TimeSlotCore<Type> = {
    slot?: Date | null;
    _type: Type;
    bookingLimit: number;
};

export enum TimeSlotEnum {
    Appointment = 'appointment',
    VisitAppointment = 'visitAppointment',
}

export type DealerSpecificItem<T> = {
    defaultValue: T;
    overrides: Array<{
        dealerId: ObjectId;
        value: T;
    }>;
};

export enum FinderVehicleCondition {
    New = 'new',
    Preowned = 'preowned',
    PorscheApproved = 'porscheApproved',
}

export enum FinancingPreferenceValue {
    Mandatory = 'mandatory',
    Optional = 'optional',
    Request = 'request',
}

export type FinderApplicationModuleConfirmEmailContents = {
    subject: DealerTranslationText;
    introTitle: DealerTranslationText;
    introImage?: UploadedFileWithPreview | null;
    contentText: DealerTranslationText;
    isSummaryVehicleVisible: boolean;
};

export type FinderApplicationModuleReminderEmailContents = {
    subject: DealerTranslationText;
    introTitle: DealerTranslationText;
    introImage?: UploadedFileWithPreview | null;
    contentText: DealerTranslationText;
    sendingTime: Date;
    isActive: boolean;
};

export type FinderApplicationModuleEmailContents = {
    submitOrder: EmailWithScenarioOverrides<FinderApplicationModuleConfirmEmailContents>;
    reminderEmail: EmailWithScenarioOverrides<FinderApplicationModuleReminderEmailContents>;
};

export type MobilityHomeDelivery = {
    _id: ObjectId;
    isEnable: boolean;
    assigneeId?: ObjectId;
};

export type MobilitySigningSetting = {
    isEnabled: boolean;
    moduleId?: ObjectId | null;
};

export type MobilityEmailContent = {
    subject: DealerTranslationText;
    introTitle: DealerTranslationText;
    introImage?: UploadedFileWithPreview | null;
    contentText: DealerTranslationText;
    isEmailEnabled: boolean;
};

export type MobilityCustomerEmailContent = {
    bookingConfirmation: MobilityEmailContent;
    bookingAmendment: MobilityEmailContent;
    bookingCancellation: MobilityEmailContent;
    bookingReminder: MobilityEmailContent;
    bookingCheckedIn: MobilityEmailContent;
    bookingComplete: MobilityEmailContent;
};

export type MobilityOperatorEmailContent = {
    bookingConfirmation: MobilityEmailContent;
    bookingAmendment: MobilityEmailContent;
    bookingCancellation: MobilityEmailContent;
};

export type MobilityEmailScenarioContent = {
    _id: ObjectId;
    locationId?: ObjectId;
    isHomeDelivery: boolean;
    customers: MobilityCustomerEmailContent;
    operators: MobilityOperatorEmailContent;
};

export type ConfiguratorSaveOrderEmailContents = {
    subject: DealerTranslationText;
    introTitle: DealerTranslationText;
    introImage?: UploadedFileWithPreview | null;
    contentText: DealerTranslationText;
    bannerImage?: UploadedFileWithPreview | null;
};

export type ConfiguratorConfirmEmailContents = {
    subject: DealerTranslationText;
    introTitle: DealerTranslationText;
    introImage?: UploadedFileWithPreview | null;
    contentText: DealerTranslationText;
    isPaymentSectionShown: boolean;
};

export type ConfiguratorEmailContents = {
    saveOrder: ConfiguratorSaveOrderEmailContents;
    submitOrder: ConfiguratorConfirmEmailContents;
};

export type ApplicationMarketCore<TType extends ApplicationMarket> = {
    type: TType;
};

export type SingaporeMarketType = ApplicationMarketCore<ApplicationMarket.Singapore> & {
    coe: DealerMarketSetting;
};

export type DefaultMarketType = ApplicationMarketCore<ApplicationMarket.Default>;

export type NewZealandMarketType = ApplicationMarketCore<ApplicationMarket.NewZealand> & {
    ppsr: DealerMarketSetting;
    estFee: DealerMarketSetting;
    bankEstFee: BankDealerMarketSetting;
    nzFees: NzFeesDealerMarketSetting;
};

export type DealerMarketSetting = {
    // singapore/NewZealand market do not use defaultValue and value of overrides.
    // it is controlled by company and dealer
    defaultValue: number;
    editable: boolean;
    overrides: Array<{
        dealerId: ObjectId;
        editable: boolean;
        value: number;
    }>;
};

export type BankDealerMarketSetting = {
    editable: boolean;
    overrides: Array<{
        dealerId: ObjectId;
        editable: boolean;
    }>;
};

export type NzFeesDealerMarketSetting = {
    viewable: boolean;
    overrides: Array<{
        dealerId: ObjectId;
        viewable: boolean;
    }>;
};

export type DealerBookingCodeSetting = {
    defaultValue: string;
    viewable: boolean;
    overrides: Array<{ dealerId: ObjectId; value: string; viewable: boolean }>;
};

export type DealerSetting<T> = {
    defaultValue: T;
    overrides: Array<{ dealerId: ObjectId; value: T }>;
};

export type DealerDepositAmountSetting = DealerSetting<number>;

export type DealerPriceDisclaimerSetting = DealerSetting<TranslatedString>;

export type DealerDisclaimersConfiguratorSetting = DealerSetting<Array<TranslatedString>>;

export type DealerTranslatedStringSetting = DealerSetting<TranslatedString>;

export type DealerTranslationTextOverride = {
    dealerId: ObjectId;
    value: TranslatedString;
};

export type DealerTranslationText = {
    defaultValue: TranslatedString;
    overrides: DealerTranslationTextOverride[];
};

export type MobilityEmailContentSetting<T> = {
    _id: ObjectId;
    // For location
    locationId?: ObjectId;
    // For home delivery
    isHomeDelivery: boolean;
    value: T;
};

export type MobilityLocation = {
    _id: ObjectId;
    name: string;
    address: string;
    url: string;
    phone: string;
    email: string;
    assigneeId?: ObjectId;
};

export type DealerVehicles = {
    dealerId: ObjectId;
    vehicleSuiteIds: ObjectId[];
};

export type DealerFinanceProducts = {
    dealerId: ObjectId;
    financeProductSuiteIds: ObjectId[];
};

export type DealerInsuranceProducts = {
    dealerId: ObjectId;
    insuranceProductSuiteIds: ObjectId[];
};

export type CounterSettings = { prefix: string; method: CounterMethod; padding: number };

export enum ApplicationMarket {
    Default = 'default',
    Singapore = 'singapore',
    NewZealand = 'newZealand',
}

export enum ApplicationScenario {
    Financing = 'financing',
    Payment = 'payment',
    Insurance = 'insurance',
    Appointment = 'appointment',
    LeadCapture = 'leadCapture',
    Booking = 'booking',
    VisitAppointment = 'visitAppointment',
}

export enum DisplayPreference {
    Hidden = 'hidden',
    AlwaysShown = 'alwaysShown',
    ShownWhenMoreThanOne = 'shownWhenMoreThanOne',
}

export enum BankDisplayInSharePdf {
    Hidden = 'hidden',
    FollowCalculator = 'followCalculator',
}

export type ApplicationMarketType = DefaultMarketType | SingaporeMarketType | NewZealandMarketType;

export enum TransportProtocolType {
    EMAIL = 'email',
    SMS = 'sms',
}

export type FlexibleDiscount = {
    isEnabled: boolean;
};

export enum TemplateType {
    AfcTemplate = 'afcTemplate',
}

export type EventApplicationModuleConfirmEmailContents = {
    subject: DealerTranslationText;
    introTitle: DealerTranslationText;
    introImage?: UploadedFileWithPreview | null;
    contentText: DealerTranslationText;
};

export type SalesOfferModuleEmailContents = {
    combinedTemplate: SalesOfferEmailContents;
    singlePreOfferTemplate: SalesOfferEmailContents;
    salesOfferTemplate: SalesOfferTemplateGroup;
    shareTemplate: SalesOfferEmailContents;
};

export type SalesOfferTemplateGroup = {
    salesManager: SalesOfferEmailContents;
    customer: SalesOfferEmailContents;
};

export type SalesOfferEmailContents = {
    subject: DealerSpecificItem<TranslatedString>;
    introTitle?: DealerSpecificItem<TranslatedString>;
    contentText: DealerSpecificItem<TranslatedString>;
};
