import { ObjectId } from 'mongodb';
import { EdmEmailSocialMedia } from './Company';
import { LocalCustomerFieldKey } from './Customer';
import { DealershipPublicSalesPerson, DealershipSetting } from './DealershipSetting';
import { KYCPreset } from './KYCPresets';
import { TranslatedString } from './LanguagePack';
import { OIDCClient, OIDCKeyPair } from './OIDC';
import { UploadedFileWithPreview } from './UploadedFile';
import { SimpleVersioning } from './Versioning';
import {
    type ApplicationMarketType,
    ApplicationScenario,
    type AppointmentModuleEmailContents,
    BankDisplayInSharePdf,
    type ConfiguratorEmailContents,
    type CounterSettings,
    type DealerBookingCodeSetting,
    type DealerDepositAmountSetting,
    type DealerDisclaimersConfiguratorSetting,
    type DealerFinanceProducts,
    type DealerInsuranceProducts,
    type DealerPriceDisclaimerSetting,
    type DealerSpecificItem,
    type DealerTranslatedStringSetting,
    type DealerVehicles,
    DisplayPreference,
    type EmailWithScenarioOverrides,
    type EventApplicationModuleConfirmEmailContents,
    FinancingPreferenceValue,
    type FinderApplicationModuleEmailContents,
    FinderVehicleCondition,
    type FlexibleDiscount,
    type GiftVoucherModuleEmailContents,
    type MobilityEmailScenarioContent,
    type MobilityHomeDelivery,
    type MobilityLocation,
    type MobilitySigningSetting,
    type SalesOfferModuleEmailContents,
    type StandardApplicationModuleEmailContents,
    TemplateType,
    type TimeSlotCore,
    TimeSlotEnum,
    TransportProtocolType,
    type VisitAppointmentModuleEmailContents,
} from './moduleShared';
import {
    type Authorization,
    type DateUnit,
    DayOfWeek,
    EventLeadOriginType,
    EventMediumType,
    type Period,
    type Phone,
    PreferenceValue,
} from './shared';

export enum ModuleType {
    SimpleVehicleManagement = 'simpleVehicleManagement',
    LocalCustomerManagement = 'localCustomerManagement',
    ConsentsAndDeclarations = 'consentsAndDeclarationsModule',
    BankModule = 'bankModule',
    BasicSigningModule = 'basicSigningModule',
    NamirialSigningModule = 'namirialSigningModule',
    StandardApplicationModule = 'standardApplicationModule',
    EventApplicationModule = 'eventApplicationModule',
    MyInfoModule = 'myInfoModule',
    ConfiguratorModule = 'configuratorModule',
    PromoCodeModule = 'promoCodeModule',
    MaintenanceModule = 'maintenanceModule',
    WebsiteModule = 'WebsiteModule',
    MobilityModule = 'mobilityModule',

    /**
     * Appointment Module will be treated as Test Drive Module
     * :: Featured into any test drive journey like Lead Capture / Finder journey
     */
    AppointmentModule = 'appointmentModule',
    /**
     * Appointment Visit Module will be mainly focusing on `Appointment Basis meetup / visit`
     */
    VisitAppointmentModule = 'visitAppointmentModule',
    InsuranceModule = 'insuranceModule',
    AutoplayModule = 'autoplayModule',

    // payment modules
    AdyenPaymentModule = 'adyenPaymentModule',
    PorschePaymentModule = 'porschePaymentModule',
    FiservPaymentModule = 'fiservPaymentModule',
    PayGatePaymentModule = 'paygatePaymentModule',
    TtbPaymentModule = 'ttbPaymentModule',

    // live chat modules
    WhatsappLiveChatModule = 'whatsAppLiveChatModule',
    UserlikeChatbotModule = 'userlikeChatbotModule',

    // labels modules
    LabelsModule = 'labelsModule',

    // Finder vehicle management module
    FinderVehicleManagement = 'finderVehicleManagement',
    FinderApplicationPrivateModule = 'finderApplicationPrivateModule',
    FinderApplicationPublicModule = 'finderApplicationPublicModule',

    // Porsche CTS
    CtsModule = 'ctsModule',

    // Porsche Master Data module
    PorscheMasterDataModule = 'porscheMasterDataModule’',

    GiftVoucherModule = 'giftVoucherModule',

    TradeInModule = 'tradeInModule',

    // Porsche C@P
    CapModule = 'capModule',

    // Porsche ID
    PorscheIdModule = 'porscheIdModule',

    // Porsche Retain
    PorscheRetainModule = 'porscheRetainModule',

    // Docusign
    Docusign = 'docusignModule',

    // Launch Pad
    LaunchPadModule = 'launchPadModule',

    // SSO Modules
    OIDC = 'oidc',

    // Marketing Module
    Marketing = 'marketingModule',

    // Sales Offer
    SalesOfferModule = 'salesOfferModule',

    // Vehicle Data With Porsche Code
    VehicleDataWithPorscheCodeIntegrationModule = 'vehicleDataWithPorscheCodeIntegrationModule',

    // Sales Control Board Module
    SalesControlBoardModule = 'salesControlBoardModule',
}

export type ModuleCore<Type> = {
    _id: ObjectId;
    _type: Type;
    companyId: ObjectId;
    displayName: string;
    _versioning: SimpleVersioning;
};

export type SimpleVehicleManagementModule = ModuleCore<ModuleType.SimpleVehicleManagement> & {
    defaultTemplate: TemplateType;
    porscheMasterDataModuleId?: ObjectId | null;
    porscheMasterDataSyncedAt?: Date | null;
    porscheMasterDataAutoSyncUpdateEmailRecipient?: ObjectId[];
    sendWeeklyReminderForImage: Boolean;
    emailRecipient: ObjectId[];
};

export type FinderVehicleManagementModule = ModuleCore<ModuleType.FinderVehicleManagement> & {
    autoplaySettingId?: ObjectId | null;
};

export type ConsentsAndDeclarationsModule = ModuleCore<ModuleType.ConsentsAndDeclarations>;

export type LocalCustomerManagementModuleKycField = {
    field: LocalCustomerFieldKey;
    order: number;
};

export enum AgeCalculationMethod {
    BirthdayBased = 'birthdayBased',
    CalendarYearBased = 'calendarYearBased',
}

export type LocalCustomerManagementKYCFieldExtraConfig = {
    minimumAge: number;
    ageCalculationMethod: AgeCalculationMethod;
    mobileVerification: boolean;
};

export type LocalCustomerManagementModule = ModuleCore<ModuleType.LocalCustomerManagement> & {
    kycFields: LocalCustomerManagementModuleKycField[];
    kycPresets: KYCPreset[];
    extraSettings: LocalCustomerManagementKYCFieldExtraConfig;
};

export type BankModule = ModuleCore<ModuleType.BankModule>;

export type InsuranceModule = ModuleCore<ModuleType.InsuranceModule>;

export type BasicSigningModule = ModuleCore<ModuleType.BasicSigningModule> & {
    lifeTime: number;
    transportProtocol: TransportProtocolType;
};

export type NamirialSigningModule = ModuleCore<ModuleType.NamirialSigningModule>;

export type StandardApplicationModule = ModuleCore<ModuleType.StandardApplicationModule> & {
    // customer module
    customerModuleId: ObjectId;

    // vehicle management module
    vehicleModuleId: ObjectId;

    // bank module
    bankModuleId?: ObjectId | null;

    // insurance module
    insuranceModuleId?: ObjectId | null;

    // agreement module
    agreementsModuleId: ObjectId;

    // payment settings
    paymentSetting?: DealershipSetting;

    // MyInfo setting
    myInfoSettingId?: ObjectId | null;

    // display Appointment Datepicker in Application module
    displayAppointmentDatepicker: boolean;

    // Appointment module
    appointmentModuleId?: ObjectId | null;

    // Visit Appointment module
    visitAppointmentModuleId?: ObjectId | null;

    market: ApplicationMarketType;

    // scenario to be applied on application
    scenarios: ApplicationScenario[];

    // deposit amount for the payment in journey
    depositAmount?: DealerDepositAmountSetting;

    // price disclaimer in journey
    priceDisclaimer?: DealerPriceDisclaimerSetting;

    // counters
    applicationCounter: CounterSettings;
    leadCounter: CounterSettings;
    reservationCounter: CounterSettings;
    appointmentCounter: CounterSettings;
    insuranceCounter: CounterSettings;
    dealerVehicles: DealerVehicles[];
    dealerFinanceProducts: DealerFinanceProducts[];
    dealerInsuranceProducts: DealerInsuranceProducts[];

    // ocr enabled, to extract customer information from uploaded files
    isOcrEnabled: boolean;

    // promo code module
    promoCodeModuleId?: ObjectId | null;

    /* Versioning */
    _versioning: SimpleVersioning;

    showResetKYCButton: boolean;

    /* Show From Value on Vehicle Details in Calculator */
    showFromValueOnVehicleDetails: boolean;

    /* Determine when to show Bank in Calculator */
    bankDisplayPreference: DisplayPreference;

    /* Determine bank visibility in share PDF */
    bankDisplayInSharePdf: BankDisplayInSharePdf;

    showRemoteFlowButtonInKYCPage: boolean;

    skipForDeposit: boolean;

    isTradeInAmountVisible?: boolean;

    tradeIn: boolean;

    testDrive: boolean;

    isFinancingOptional: boolean;
    isInsuranceOptional: boolean;

    showFinanceCalculator: boolean;
    showInsuranceCalculator: boolean;

    /* Email Content */
    emailContents: StandardApplicationModuleEmailContents;

    /* when application has insurance scenario, this will be filled */
    insurerIds: ObjectId[];

    /* when application has financing scenario, this will be filled */
    bankIds: ObjectId[];

    /* Preference to show in CI */
    insurerDisplayPreference: DisplayPreference;

    hasDealerOptions: boolean;

    includeDealerOptionsForFinancing: boolean;

    flexibleDiscount: FlexibleDiscount;

    /* Integrated C@P Module */
    capModuleId?: ObjectId | undefined | null;
    isSearchCapCustomerOptional: boolean;
    capPrequalification: boolean;
    leadOrigin?: EventLeadOriginType | undefined | null;
    leadMedium?: EventMediumType | undefined | null;
    leadCampaignId?: string | undefined | null;
};

export type SalesOfferModule = ModuleCore<ModuleType.SalesOfferModule> & {
    /**
     * Feature Module for Sales Offer
     * :: Customer && Consents
     * :: Vehicle
     * :: Bank (Financing)
     * :: Insurance
     * :: Deposit (Optional)
     * :: Launchpad
     */
    vehicleModuleId: ObjectId;
    bankModuleId: ObjectId | null;

    insuranceModuleId: ObjectId | null;
    paymentSetting?: DealershipSetting;
    vehicleDataWithPorscheCodeIntegrationSettingId: ObjectId | null;
    signingModuleId?: ObjectId;

    // market module
    market: ApplicationMarketType;
    dealerFinanceProducts: DealerFinanceProducts[];
    dealerInsuranceProducts: DealerInsuranceProducts[];

    vsaSigningInstructions: DealerTranslatedStringSetting;
    emailContents: SalesOfferModuleEmailContents;

    /* when application has insurance scenario, this will be filled */
    insurerIds: ObjectId[];

    /* when application has financing scenario, this will be filled */
    bankIds: ObjectId[];

    // counters
    applicationCounter: CounterSettings;
    reservationCounter: CounterSettings;
    insuranceCounter: CounterSettings;
    vsaCounter: CounterSettings;

    /* Versioning */
    _versioning: SimpleVersioning;
};

export type EventApplicationModuleEmailContents = {
    submitOrder: EmailWithScenarioOverrides<EventApplicationModuleConfirmEmailContents>;
};

export type EventApplicationModule = ModuleCore<ModuleType.EventApplicationModule> & {
    // customer module
    customerModuleId: ObjectId;

    // vehicle management module
    vehicleModuleId: ObjectId;

    // agreement module
    agreementsModuleId: ObjectId;

    // display Appointment Datepicker ( Test Drive ) in Application module
    displayAppointmentDatepicker: boolean;

    // display Appointment Datepicker in Application module
    displayVisitAppointmentDatepicker: boolean;

    // Appointment module
    appointmentModuleId?: ObjectId | null;

    // Visit Appointment module
    visitAppointmentModuleId?: ObjectId | null;

    // counters
    eventCounter: CounterSettings;
    applicationCounter: CounterSettings;
    leadCounter: CounterSettings;
    appointmentCounter: CounterSettings;
    reservationCounter: CounterSettings;
    dealerVehicles: DealerVehicles[];
    dealerFinanceProducts: DealerFinanceProducts[];

    // ocr enabled, to extract customer information from uploaded files
    isOcrEnabled: boolean;

    /* Versioning */
    _versioning: SimpleVersioning;

    // email contents
    emailContents: EventApplicationModuleEmailContents;

    showResetKYCButton: boolean;

    showRemoteFlowButtonInKYCPage: boolean;

    // live chat settings
    liveChatSettingId?: ObjectId | null;

    isCapEnabled?: boolean;
    capModuleId?: ObjectId | undefined | null;
};

export type AdyenPaymentModule = ModuleCore<ModuleType.AdyenPaymentModule>;

export type PorschePaymentModule = ModuleCore<ModuleType.PorschePaymentModule>;

export type FiservPaymentModule = ModuleCore<ModuleType.FiservPaymentModule>;

export type PayGatePaymentModule = ModuleCore<ModuleType.PayGatePaymentModule>;

export type TtbPaymentModule = ModuleCore<ModuleType.TtbPaymentModule>;

export type WhatsappLiveChatModule = ModuleCore<ModuleType.WhatsappLiveChatModule>;

export type UserlikeChatbotModule = ModuleCore<ModuleType.UserlikeChatbotModule>;

export type PromoCodeModule = ModuleCore<ModuleType.PromoCodeModule>;

/**
 * CTS Module
 *
 * For cts setting, look into `CtsSetting` document
 */
export type CtsModule = ModuleCore<ModuleType.CtsModule> & {
    authorization: Authorization;
};

/**
 * Porsche Master Data Module
 *
 * For PCCD MasterData Integration for Vehicle Management
 */
export type PorscheMasterDataModule = ModuleCore<ModuleType.PorscheMasterDataModule>;

/**
 * Autoplay Module
 */
export type AutoplayModule = ModuleCore<ModuleType.AutoplayModule>;

/**
 * Vehicle Data With Porsche Code Module
 */
export type VehicleDataWithPorscheCodeIntegrationModule =
    ModuleCore<ModuleType.VehicleDataWithPorscheCodeIntegrationModule>;

/**
 * Docusign Module
 */
export type DocusignModule = ModuleCore<ModuleType.Docusign>;

/**
 * Marketing Module
 */
export type MarketingModule = ModuleCore<ModuleType.Marketing>;

export type SalesControlBoardModule = ModuleCore<ModuleType.SalesControlBoardModule> & {
    testDriveMonthlyTarget: DealerSpecificItem<number>;
    orderIntakesMonthlyTarget: DealerSpecificItem<number>;
    retailsMonthlyTarget: DealerSpecificItem<number>;

    /**
     * finnance and insurance commission monthly targets should be percentage
     * the inputs should be within 1-100,
     * will rounding to the closest integer when inputs are outside the range
     */
    financeCommissionMonthlyTarget: DealerSpecificItem<number>;
    insuranceCommissionMonthlyTarget: DealerSpecificItem<number>;

    salesConsultantsAssignments: DealerSpecificItem<ObjectId[]>;
};

export type Module =
    | SimpleVehicleManagementModule
    | ConsentsAndDeclarationsModule
    | LocalCustomerManagementModule
    | BankModule
    | BasicSigningModule
    | NamirialSigningModule
    | StandardApplicationModule
    | EventApplicationModule
    | AdyenPaymentModule
    | PorschePaymentModule
    | FiservPaymentModule
    | PayGatePaymentModule
    | TtbPaymentModule
    | MyInfoModule
    | ConfiguratorModule
    | WhatsappLiveChatModule
    | UserlikeChatbotModule
    | PromoCodeModule
    | MaintenanceModule
    | WebsiteModule
    | MobilityModule
    | LabelsModule
    | FinderVehicleManagementModule
    | FinderApplicationPublicModule
    | FinderApplicationPrivateModule
    | AutoplayModule
    | CtsModule
    | AppointmentModule
    | InsuranceModule
    | PorscheMasterDataModule
    | GiftVoucherModule
    | TradeInModule
    | CapModule
    | PorscheIdModule
    | PorscheRetainModule
    | DocusignModule
    | LaunchPadModule
    | VisitAppointmentModule
    | OIDCModule
    | MarketingModule
    | VehicleDataWithPorscheCodeIntegrationModule
    | SalesOfferModule
    | SalesControlBoardModule;

export type ApplicationModule =
    | StandardApplicationModule
    | EventApplicationModule
    | ConfiguratorModule
    | MobilityModule
    | FinderApplicationPublicModule
    | FinderApplicationPrivateModule
    | LaunchPadModule
    | SalesOfferModule;

export type ApplicationModuleWithFinancing =
    | StandardApplicationModule
    | ConfiguratorModule
    | FinderApplicationPublicModule
    | FinderApplicationPrivateModule;

// currently they are the same
export type ApplicationModuleWithInsurancing = ApplicationModuleWithFinancing;

export type LeadModule =
    | StandardApplicationModule
    | EventApplicationModule
    | ConfiguratorModule
    | MobilityModule
    | FinderApplicationPublicModule
    | FinderApplicationPrivateModule
    | LaunchPadModule;

export type TestDriveApplicationModule =
    | StandardApplicationModule
    | EventApplicationModule
    | ConfiguratorModule
    | FinderApplicationPublicModule
    | FinderApplicationPrivateModule
    | LaunchPadModule;

export const applicationModuleTypes: Array<ModuleType> = [
    ModuleType.EventApplicationModule,
    ModuleType.StandardApplicationModule,
    ModuleType.ConfiguratorModule,
    ModuleType.FinderApplicationPublicModule,
    ModuleType.FinderApplicationPrivateModule,
    ModuleType.LaunchPadModule,
];
export type PaymentModule =
    | AdyenPaymentModule
    | PorschePaymentModule
    | FiservPaymentModule
    | PayGatePaymentModule
    | TtbPaymentModule;

export type MyInfoModule = ModuleCore<ModuleType.MyInfoModule>;

export type ConfiguratorModule = ModuleCore<ModuleType.ConfiguratorModule> & {
    // customer module
    customerModuleId: ObjectId;

    // vehicle management module
    vehicleModuleId: ObjectId;

    // consent module
    agreementsModuleId: ObjectId;

    // bank module
    bankModuleId?: ObjectId | null;

    // insurance module
    insuranceModuleId?: ObjectId | null;

    // payment settings
    paymentSetting?: DealershipSetting;

    // deposit amount
    depositAmount?: DealerDepositAmountSetting;

    // MyInfo setting
    myInfoSettingId?: ObjectId | null;

    // display Appointment Datepicker in Application module
    displayAppointmentDatepicker: boolean;

    // Appointment module
    appointmentModuleId?: ObjectId | null;

    // Visit Appointment module
    visitAppointmentModuleId?: ObjectId | null;

    // market module
    market: ApplicationMarketType;

    // scenario to be applied on application
    scenarios: ApplicationScenario[];

    // counters
    applicationCounter: CounterSettings;
    leadCounter: CounterSettings;
    reservationCounter: CounterSettings;
    appointmentCounter: CounterSettings;
    insuranceCounter: CounterSettings;
    dealerVehicles: DealerVehicles[];
    dealerFinanceProducts: DealerFinanceProducts[];
    dealerInsuranceProducts: DealerInsuranceProducts[];

    isTradeInAmountVisible?: boolean;

    // toggle trade in
    tradeIn: boolean;

    // toggle test drive
    testDrive: boolean;

    financingPreference: FinancingPreferenceValue;
    isInsuranceOptional: boolean;

    showFinanceCalculator: boolean;
    showInsuranceCalculator: boolean;

    // live chat settings
    liveChatSettingId?: ObjectId | null;

    // assignee
    assignee: DealershipPublicSalesPerson;

    // ocr enabled, to extract customer information from uploaded files
    isOcrEnabled: boolean;

    // promo code module
    promoCodeModuleId?: ObjectId | null;

    priceDisclaimer: DealerDisclaimersConfiguratorSetting;

    termsTitle: DealerTranslatedStringSetting;

    termsText: DealerTranslatedStringSetting;

    isInventoryEnabled: boolean;

    /* Versioning */
    _versioning: SimpleVersioning;

    // email contents
    emailContents: ConfiguratorEmailContents;

    showResetKYCButton: boolean;

    /* Show From Value on Vehicle Details in Calculator */
    showFromValueOnVehicleDetails: boolean;

    /* Determine when to show Bank in Calculator */
    bankDisplayPreference: DisplayPreference;

    showRemoteFlowButtonInKYCPage: boolean;

    skipForDeposit: boolean;

    /* External URL */
    externalUrl?: string;

    /* when application has insurance scenario, this will be filled */
    insurerIds: ObjectId[];

    /* when application has financing scenario, this will be filled */
    bankIds: ObjectId[];

    /* Preference to show in CI */
    insurerDisplayPreference: DisplayPreference;

    /* Integrated C@P Module */
    capModuleId?: ObjectId | undefined | null;
    isSearchCapCustomerOptional: boolean;
    capPrequalification: boolean;
    leadOrigin?: EventLeadOriginType | undefined | null;
    leadMedium?: EventMediumType | undefined | null;
    leadCampaignId?: string | undefined | null;

    /* Porsche ID Integration */
    porscheIdModuleId?: ObjectId | null | undefined;
    isCustomerDataRetreivalByPorscheId?: boolean | null | undefined;
    isPorscheIdLoginMandatory?: boolean | null | undefined;
};

export type MaintenanceModule = ModuleCore<ModuleType.MaintenanceModule> & {
    // Page Title - Translation
    pageTitle?: TranslatedString;

    // Page Content - Translation
    pageContent?: TranslatedString | null;

    // Image
    image?: UploadedFileWithPreview | null;

    // period
    period?: Period;

    // Warning Before
    warningBefore?: number | null;

    // IsActive
    isActive?: boolean;

    /* Versioning */
    _versioning: SimpleVersioning;
};

export type WebsiteModule = ModuleCore<ModuleType.WebsiteModule> & {
    phone: Phone;
    email: string;
    // live chat settings
    liveChatSettingId?: ObjectId | null;
    options: {
        locations: Array<{ country: string; region: string; url: string }>;
    };
    socialMedia: EdmEmailSocialMedia[];
    address?: string | null;
};

export type MobilityModule = ModuleCore<ModuleType.MobilityModule> & {
    // customer module
    customerModuleId: ObjectId;

    // vehicle management module
    vehicleModuleId: ObjectId;

    // agreement module
    agreementsModuleId: ObjectId;

    // scenario to be applied on application
    scenarios: ApplicationScenario[];

    // payment settings
    paymentSettingsId?: ObjectId | null;

    // live chat settings
    liveChatSettingId?: ObjectId | null;

    // promo code module
    promoCodeModuleId?: ObjectId | null;

    // gift voucher module
    giftVoucherModuleId?: ObjectId | null;

    // counters
    bookingsCounter: CounterSettings;

    minimumAdvancedBooking: number;

    amendmentCutOff: number;

    durationBeforeNextBooking: number;

    locations: MobilityLocation[];

    rentalDisclaimer: DealerTranslatedStringSetting;

    rentalRequirement: DealerTranslatedStringSetting;

    externalModelInfo: boolean;

    baseUrl?: string | null;

    unavailableTimeRange: Period[];

    unavailableDayOfWeek: DayOfWeek[];

    // set default 0 means no booking range
    availableNumberOfBookingRange: DateUnit;

    emailContents: MobilityEmailScenarioContent[];

    persistKYCData: boolean;

    bookingCode: DealerBookingCodeSetting;

    signing: MobilitySigningSetting;

    showResetKYCButton: boolean;

    sendEmailToCustomer: boolean;

    sendPDFToCustomer: boolean;

    homeDelivery: MobilityHomeDelivery;
};

export type LabelsModule = ModuleCore<ModuleType.LabelsModule>;

export type FinderApplicationCore<Type> = ModuleCore<Type> & {
    // customer module
    customerModuleId: ObjectId;

    // porsche finder vehicle management module
    vehicleModuleId: ObjectId;

    // consent module
    agreementsModuleId: ObjectId;

    // bank module
    bankModuleId?: ObjectId | null;

    insuranceModuleId?: ObjectId | null;

    // payment settings
    paymentSetting?: DealershipSetting;

    // display Appointment Datepicker in Application module
    displayAppointmentDatepicker: boolean;

    // Appointment module
    appointmentModuleId?: ObjectId | null;

    // Visit Appointment module
    visitAppointmentModuleId?: ObjectId | null;

    // deposit amount
    depositAmount?: DealerDepositAmountSetting;

    // MyInfo setting
    myInfoSettingId?: ObjectId | null;

    // market module
    market: ApplicationMarketType;

    // scenario to be applied on application
    scenarios: ApplicationScenario[];

    // counters
    applicationCounter: CounterSettings;
    leadCounter: CounterSettings;
    reservationCounter: CounterSettings;
    appointmentCounter: CounterSettings;
    insuranceCounter: CounterSettings;
    dealerVehicles: DealerVehicles[];
    dealerFinanceProducts: DealerFinanceProducts[];
    dealerInsuranceProducts: DealerInsuranceProducts[];

    // toggle trade in
    tradeIn: boolean;
    isTradeInAmountVisible?: boolean;

    // toggle test drive
    testDrive: boolean;

    isInsuranceOptional: boolean;

    financingPreference: FinancingPreferenceValue;
    displayFinanceCalculator: PreferenceValue;

    showInsuranceCalculator: boolean;

    // is calculator visible, only applicable for lead c@pture
    isCalculatorVisible?: boolean;

    // live chat settings
    liveChatSettingId?: ObjectId | null;

    // ocr enabled, to extract customer information from uploaded files
    isOcrEnabled: boolean;

    // promo code module
    promoCodeModuleId?: ObjectId | null;

    // price disclaimer
    priceDisclaimer: DealerDisclaimersConfiguratorSetting;

    reservationInstructions?: DealerTranslatedStringSetting;

    // financing disclaimer, only when scenario has financing
    financingDisclaimer?: DealerDisclaimersConfiguratorSetting;

    /* Versioning */
    _versioning: SimpleVersioning;

    // email contents
    emailContents: FinderApplicationModuleEmailContents;

    showResetKYCButton: boolean;

    /* Show From Value on Vehicle Details in Calculator */
    showFromValueOnVehicleDetails: boolean;

    /* Determine when to show Bank in Calculator */
    bankDisplayPreference: DisplayPreference;

    finderVehicleConditions: FinderVehicleCondition[];
    porscheApprovedInfo: TranslatedString;

    /* when application has insurance scenario, this will be filled */
    insurerIds: ObjectId[];

    /* when application has financing scenario, this will be filled */
    bankIds: ObjectId[];

    /* Preference to show in CI */
    insurerDisplayPreference: DisplayPreference;

    /* Reservation period */
    reservationPeriod: number;

    /* Integrated C@P Module */
    capModuleId?: ObjectId | undefined | null;
    isSearchCapCustomerOptional: boolean;
    capPrequalification: boolean;
    leadOrigin?: EventLeadOriginType | undefined | null;
    leadMedium?: EventMediumType | undefined | null;
    leadCampaignId?: string | undefined | null;

    useBankDisclaimers?: boolean;
    useInsurerDisclaimers?: boolean;

    disclaimers?: {
        financingDisclaimer?: DealerDisclaimersConfiguratorSetting;
        insuranceDisclaimer?: DealerDisclaimersConfiguratorSetting;
    };
};

export type FinderApplicationPrivateModule = FinderApplicationCore<ModuleType.FinderApplicationPrivateModule> & {
    skipForDeposit: boolean;

    showRemoteFlowButtonInKYCPage: boolean;

    hasDealerOptions: boolean;

    includeDealerOptionsForFinancing: boolean;

    flexibleDiscount: FlexibleDiscount;

    allowSaveDraftAndReserveStock: boolean;
};

export type FinderApplicationPublicModule = FinderApplicationCore<ModuleType.FinderApplicationPublicModule> & {
    // assignee
    assignee: DealershipPublicSalesPerson;

    skipForDeposit: boolean;

    /* Porsche ID Integration */
    porscheIdModuleId?: ObjectId | null | undefined;
    isCustomerDataRetreivalByPorscheId?: boolean | null | undefined;
    isPorscheIdLoginMandatory?: boolean | null | undefined;
};

export type FinderApplicationModule = FinderApplicationPrivateModule | FinderApplicationPublicModule;

export type AppointmentModule = ModuleCore<ModuleType.AppointmentModule> & {
    /* Versioning */
    _versioning: SimpleVersioning;

    unavailableDayOfWeek: DayOfWeek[];

    bookingTimeSlot: TimeSlotCore<TimeSlotEnum.Appointment>[];

    // set default 0 means no advance booking limit
    advancedBookingLimit: number;

    // empty means there is no limit for the last allowable appointment date
    maxAdvancedBookingLimit?: number | null | undefined;

    bookingInformation?: TranslatedString | null | undefined;

    hasTestDriveProcess: boolean;

    hasTestDriveSigning?: boolean;

    isReminderTimeEnabled?: boolean;

    timeToSendReminder?: number;

    signingModuleId?: ObjectId;

    emailContents: AppointmentModuleEmailContents;

    // display remote flow ( proceed with customer device)
    showRemoteFlowButtonInKYCPage: boolean;
};

export type VisitAppointmentModule = ModuleCore<ModuleType.VisitAppointmentModule> & {
    /* Versioning */
    _versioning: SimpleVersioning;

    unavailableDayOfWeek: DayOfWeek[];

    bookingTimeSlot: TimeSlotCore<TimeSlotEnum.Appointment>[];

    // set default 0 means no advance booking limit
    advancedBookingLimit: number;

    // empty means there is no limit for the last allowable appointment date
    maxAdvancedBookingLimit?: number | null | undefined;

    bookingInformation?: TranslatedString | null | undefined;

    emailContents: VisitAppointmentModuleEmailContents;
};

export type GiftVoucherModule = ModuleCore<ModuleType.GiftVoucherModule> & {
    emailContents: GiftVoucherModuleEmailContents;

    // consent module
    agreementsModuleId: ObjectId;

    // customer module
    customerModuleId: ObjectId;

    // payment settings
    paymentSettingsId?: ObjectId | null;
};

export type TradeInModule = ModuleCore<ModuleType.TradeInModule> & {
    authorization: Authorization;
};

export type CapModule = ModuleCore<ModuleType.CapModule>;

export type PorscheIdModule = ModuleCore<ModuleType.PorscheIdModule>;

export type PorscheRetainModule = ModuleCore<ModuleType.PorscheRetainModule> & {
    link: string;

    // launch pad module
    launchPadModuleId: ObjectId;
};

export type LaunchPadModule = ModuleCore<ModuleType.LaunchPadModule> & {
    // customer module
    customerModuleId: ObjectId;

    // agreement module
    agreementsModuleId: ObjectId;

    // vehicle management module
    vehicleModuleId: ObjectId;

    // Appointment module
    appointmentModuleId?: ObjectId | null;

    // Visit Appointment module
    visitAppointmentModuleId?: ObjectId | null;

    /* Integrated C@P Module */
    capModuleId?: ObjectId | undefined | null;
    capLeadOrigin?: EventLeadOriginType | undefined | null;
    capLeadMedium?: EventMediumType | undefined | null;
    capCampaignIds: string[];

    financeAndInsuranceCalculator?: ObjectId;
    // finder private entrypoint
    finderAssignedStock?: ObjectId;
    salesManager?: ObjectId;

    dealerVehicles: DealerVehicles[];

    leadCounter: CounterSettings;
    appointmentCounter: CounterSettings;

    // decide if show sales offer button in lead details
    salesOfferModuleId?: ObjectId;

    hasTradeInRequest: boolean;

    /* Versioning */
    _versioning: SimpleVersioning;
};

export type OIDCModule = ModuleCore<ModuleType.OIDC> & {
    clients: Array<OIDCClient>;
    keyPairs: Array<OIDCKeyPair>;
};
